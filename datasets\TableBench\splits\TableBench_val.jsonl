{"id": "9bbb4e79fd68658cd40f7088028db149", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["order", "couple", "karen", "nicky", "jason", "ruthie", "robin", "total", "scoreboard", "song", "public vote %", "result"], "data": [["1", "gareth & maria", "4.5", "4.0", "3.5", "3.5", "4.0", "19 , 5", "3rd", "wake me up when september ends - green day", "6.81%", "safe"], ["2", "linda & daniel", "3.5", "3.0", "3.0", "4.0", "3.0", "16.5", "5th", "candyman - christina aguilera", "9.09%", "safe"], ["3", "samantha & pavel", "3.5", "3.0", "3.0", "3.5", "3.0", "16.0", "7th", "you can't hurry love - the supremes", "3.30%", "eliminated"], ["4", "chris & frankie", "5.0", "5.0", "4.0", "4.5", "5.0", "23.5", "1st", "rule the world - take that", "19.20%", "safe"], ["5", "aggie & sergey", "2.5", "2.0", "2.0", "3.5", "2.5", "12.5", "10th", "total eclipse of the heart - bonnie tyler", "5.00%", "safe"], ["6", "steve & susie", "3.0", "3.5", "2.0", "3.0", "3.0", "14.5", "9th", "mony mony - billy idol", "4.68%", "bottom two"], ["7", "greg & kristina", "3.5", "3.5", "2.5", "3.0", "3.0", "15.5", "8th", "licence to kill - gladys knight", "12.90%", "safe"], ["8", "zaraah & fred", "4.0", "4.5", "3.0", "3.5", "3.5", "18.5", "4th", "take a chance on me - abba", "7.88%", "safe"]]}, "question": "Could you describe the structure and content of the table, highlighting the main columns and offering initial insights into the data presented?", "answer": "The table details the performance metrics of dance couples in a competition, encompassing scores from five judges, cumulative scores, rankings, song selections, public voting percentages, and competition outcomes. It elucidates the interplay between judges' scores and public votes in determining the final results for each couple."}
{"id": "db1f2cac2692f10d66aee19c3bc2ae6c", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["ecozone", "area (km square) territorial waters", "area (km square) exclusive economic zone", "percentage of total area (foreez)", "percentage of marine area (foreez)"], "data": [["pacific marine", "102920", "457646", "3.1", "8.3"], ["arctic basin marine", "24997", "704849", "4.8", "12.7"], ["arctic archipelago marine", "2051393", "2178998", "14.8", "39.3"], ["northwest atlantic marine", "536895", "1205981", "8.2", "21.8"], ["atlantic marine", "72144", "996439", "6.8", "17.9"], ["total", "2788349", "5543913", "37.7", "100.0"]]}, "question": "What is the correlation between the 'area (km square)' of territorial waters and the 'percentage of total area (foreez)' for each ecozone? Provide the correlation coefficient as evidence.", "answer": "Strong positive correlation, 0.92"}
{"id": "240b0d45a41eb8f3b90989092af6da7b", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["agent", "melting / boiling point", "effectiveness as blood agent", "persistence , open area", "persistence , enclosed area", "field stability", "storage stability", "toxicity as blood agent"], "data": [["hydrogen cyanide", "- 13 / 26 degree", "10", "2", "9", "10", "8", "10"], ["cyanogen", "- 28 / - 21 degree", "9", "2", "9", "8", "7", "9"], ["cyanogen chloride", "- 6 / 14 degree", "8", "3", "9", "9", "9", "8"], ["cyanogen bromide", "52 / 62 degree", "9", "5", "8", "5", "6", "8"], ["arsine", "- 117 / - 62 degree", "9", "3", "8", "5", "9", "9"], ["vinyl arsine", "124 degree (boiling)", "7", "7", "9", "8", "9", "6"], ["phosgene", "- 118 / 8", "10", "6", "9", "5", "8", "6"]]}, "question": "How many agents have a melting point below 0 degrees?", "answer": "5"}
{"id": "5946c6b67b854d696437dfa3cf9aa73b", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["hand", "1 credit", "2 credits", "3 credits", "4 credits", "5 credits"], "data": [["royal flush", "250", "500", "750", "1000", "4000"], ["straight flush", "50", "100", "150", "200", "250"], ["four aces w / 2 , 3 , or 4", "400", "800", "1200", "1600", "2000"], ["four 2 , 3 , or 4 w / a - 4", "160", "320", "480", "640", "800"], ["four aces", "160", "320", "480", "640", "800"], ["four 2 , 3 , or 4", "80", "160", "240", "320", "400"], ["four 5 - k", "50", "100", "150", "200", "250"], ["full house", "10", "20", "30", "40", "50"], ["flush", "6", "12", "18", "24", "30"], ["straight", "4", "8", "12", "16", "20"], ["three of a kind", "3", "6", "9", "12", "15"], ["two pair", "1", "2", "3", "4", "5"], ["jacks or better", "1", "2", "3", "4", "5"], ["theoretical return", "98.9%", "98.9%", "98.9%", "98.9%", "100.1%"]]}, "question": "Can you describe the content of the table, explain the significance of each main column, and provide some initial insights into the data presented?", "answer": "The table shows payout amounts for various poker hands in a video poker game, categorized by credits wagered (1 to 5 credits). Each row represents a specific hand, with payouts increasing as more credits are bet. It also includes theoretical return percentages, emphasizing a player advantage when betting the maximum of 5 credits."}
{"id": "60670a8d9b1e39dd845fb1639d0d8b86", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["rank", "circuit", "headquarters", "screens", "sites"], "data": [["1", "regal entertainment group", "knoxville , tn", "7367", "580"], ["2", "amc entertainment inc", "kansas city , mo", "5894", "483"], ["3", "cinemark theatres", "plano , tx", "3895", "298"], ["4", "carmike cinemas , inc", "columbus , ga", "2242", "232"], ["5", "cineplex entertainment", "toronto , on", "1438", "133"], ["6", "rave motion pictures", "dallas , tx", "939", "62"], ["7", "marcus theatres", "milwaukee , wi", "687", "55"], ["8", "national amusements", "dedham , ma", "450", "34"], ["9", "empire theatres", "stellarton , ns", "438", "53"]]}, "question": "Can you calculate the standard deviation of the number of screens operated by the top 5 movie theater chains?", "answer": "2472.33"}
{"id": "77f8372afde69977a62eda34f4cae760", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Month", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Year"], "data": [["Record high °F (°C)", "76\n(24)", "86\n(30)", "96\n(36)", "96\n(36)", "95\n(35)", "104\n(40)", "111\n(44)", "109\n(43)", "105\n(41)", "96\n(36)", "90\n(32)", "78\n(26)", "111\n(44)"], ["Average high °F (°C)", "46.2\n(7.9)", "50.4\n(10.2)", "59.1\n(15.1)", "68.7\n(20.4)", "75.5\n(24.2)", "83.5\n(28.6)", "88.7\n(31.5)", "89.4\n(31.9)", "80.8\n(27.1)", "70.3\n(21.3)", "59.2\n(15.1)", "48.0\n(8.9)", "68.3\n(20.2)"], ["Average low °F (°C)", "26.1\n(−3.3)", "29.0\n(−1.7)", "37.8\n(3.2)", "46.9\n(8.3)", "55.7\n(13.2)", "64.1\n(17.8)", "68.7\n(20.4)", "67.7\n(19.8)", "58.9\n(14.9)", "47.6\n(8.7)", "39.2\n(4)", "28.1\n(−2.2)", "47.5\n(8.6)"], ["Record low °F (°C)", "−23\n(−31)", "−24\n(−31)", "−11\n(−24)", "18\n(−8)", "28\n(−2)", "41\n(5)", "48\n(9)", "44\n(7)", "29\n(−2)", "17\n(−8)", "5\n(−15)", "−12\n(−24)", "−24\n(−31)"], ["Precipitation inches (mm)", "1.99\n(50.5)", "2.43\n(61.7)", "3.54\n(89.9)", "4.05\n(102.9)", "4.99\n(126.7)", "4.59\n(116.6)", "3.18\n(80.8)", "2.75\n(69.9)", "4.43\n(112.5)", "3.83\n(97.3)", "4.19\n(106.4)", "2.73\n(69.3)", "42.7\n(1,084.6)"], ["Snowfall inches (cm)", "1.6\n(4.1)", "2.0\n(5.1)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", "0\n(0)", ".7\n(1.8)", "4.4\n(11.2)"], ["Avg. precipitation days (≥ 0.01 in)", "5.4", "6.6", "8.2", "9.0", "11.2", "8.9", "7.0", "6.4", "7.6", "7.9", "7.0", "6.3", "91.4"], ["Avg. snowy days (≥ 0.1 in)", ".7", ".9", "0", "0", "0", "0", "0", "0", "0", "0", "0", ".5", "2.3"]]}, "chart_type": "bar", "question": "According to the table, draw a bar chart to illustrate record high recorded in celsius degrees.", "answer": "y_references = [[24, 30, 36, 36, 35, 40, 44, 43, 41, 36, 32, 26]]"}
{"id": "73cb636df01548c38396262253f15f4f", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["player", "tackles", "solo", "assisted", "sack", "yards", "td 's"], "data": [["rex motes", "26.0", "24", "4", "0", "0", "0"], ["nathan creer", "25.5", "24", "3", "0", "0", "0"], ["walter holman", "21.0", "17", "8", "2", "3", "0"], ["pete stubbs", "19.5", "18", "3", "3", "0", "0"], ["michael witteck", "16.0", "14", "4", "2", "0", "0"], ["jon roehlk", "15.0", "11", "8", "6", "0", "0"], ["dwayne dixon", "13.0", "12", "2", "0", "0", "0"], ["sean mcinerney", "9.0", "8", "2", "3", "0", "0"], ["robert goins", "9.0", "9", "0", "0", "0", "0"], ["richard dupree", "8.5", "6", "5", "0", "0", "0"], ["brett wilson", "8.0", "7", "2", "0", "0", "0"], ["wes walton", "7.0", "4", "6", "2", "0", "0"], ["fernando mcwherter", "5.0", "5", "0", "0", "0", "0"], ["mike calhoun", "3.0", "2", "2", "3", "0", "0"], ["kendall walls", "2.0", "2", "0", "0", "0", "0"], ["steve griffin", "1.0", "1", "0", "0", "0", "0"]]}, "question": "Which is the main factor in the table, such as 'solo', 'assisted', 'sack', 'yards', and 'td 's', significantly contribute to the 'tackles' total for each player?", "answer": "solo"}
{"id": "74b748c6679b4c2e6349f304ff08ff01", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["name of county", "county seat", "area (km square)", "population", "population density", "towns / villages"], "data": [["bács - kiskun", "kecskemét", "8445", "541584", "64", "119"], ["baranya", "pécs", "4430", "402260", "91", "301"], ["békés", "békéscsaba", "5631", "392845", "70", "75"], ["borsod - abaúj - zemplén", "miskolc", "7247", "739143", "102", "355"], ["csongrád", "szeged", "4263", "425785", "100", "60"], ["fejér", "székesfehérvár", "4359", "428579", "98", "108"], ["győr - moson - sopron", "győr", "4208", "440138", "105", "182"], ["hajdú - bihar", "debrecen", "6211", "550265", "89", "82"], ["heves", "eger", "3637", "323769", "89", "119"], ["jász - nagykun - szolnok", "szolnok", "5582", "413174", "74", "75"], ["komárom - esztergom", "tatabánya", "2265", "315886", "139", "76"], ["nógrád", "salgótarján", "2546", "218218", "86", "129"], ["pest", "budapest", "6393", "1124395", "176", "186"], ["somogy", "kaposvár", "6036", "334065", "55", "244"], ["szabolcs - szatmár - bereg", "nyíregyháza", "5936", "583564", "98", "228"], ["tolna", "szekszárd", "3703", "247287", "67", "108"], ["vas", "szombathely", "3336", "266342", "80", "216"], ["veszprém", "veszprém", "4493", "368519", "82", "217"]]}, "question": "Can you provide a detailed description of the table, including the main columns and highlight any notable trends or characteristics observed in the data?**", "answer": "The table summarizes Hungarian counties by seat, area, population, density, and number of towns/villages. Pest leads in population and density due to Budapest. Komárom-Esztergom has the highest density among smaller counties, while Bács-Kiskun, the largest by area, shows lower density, suggesting a more spread-out population."}
{"id": "042e0ea557cc503992dd7e6fd9630480", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["district", "2010 population (000)", "2008 gdp (usd bn) a", "2008 gdp per capita (usd) a", "agri culture b", "mining b", "manufac turing b", "services & cons truction b", "exports (usd mn) 2011", "median mo salary (usd) a e", "vehicles (per 1000) d", "income poverty f", "structural poverty g"], "data": [["city of buenos aires", "2890", "118.0", "40828", "0.3", "1.0", "12.9", "85.8", "426", "1618", "528", "7.3", "7.8"], ["buenos aires province", "15625", "161.0", "10303", "4.5", "0.1", "21.3", "74.1", "28134", "1364", "266", "16.2", "15.8"], ["catamarca", "368", "2.331", "6009", "3.6", "20.8", "12.1", "63.5", "1596", "1241", "162", "24.3", "21.5"], ["chaco", "1055", "2.12", "2015", "12.6", "0.0", "7.5", "79.9", "602", "1061", "137", "35.4", "33.0"], ["chubut", "509", "7.11", "15422", "6.9", "21.3", "10.0", "61.8", "3148", "2281", "400", "4.6", "15.5"], ["córdoba", "3309", "33.239", "10050", "10.6", "0.2", "14.0", "75.2", "10635", "1200", "328", "14.8", "13.0"], ["corrientes", "993", "4.053", "4001", "12.6", "0.0", "8.2", "79.2", "230", "1019", "168", "31.5", "28.5"], ["entre ríos", "1236", "7.137", "5682", "11.9", "0.3", "11.6", "76.2", "1908", "1063", "280", "13.0", "17.6"], ["formosa", "530", "1.555", "2879", "7.6", "1.5", "6.4", "84.5", "40", "1007", "107", "30.7", "33.6"], ["jujuy", "673", "2.553", "3755", "5.5", "0.7", "14.6", "79.2", "456", "1123", "153", "30.0", "28.8"], ["la pampa", "319", "2.0", "5987", "19.0", "3.7", "5.3", "72.0", "378", "1164", "364", "13.6", "10.3"], ["la rioja", "334", "1.419", "4162", "3.9", "0.1", "16.8", "79.2", "281", "1040", "172", "22.0", "20.4"], ["mendoza", "1739", "18.8", "10758", "5.4", "6.1", "17.5", "71.0", "1862", "1153", "313", "12.2", "15.4"], ["misiones", "1102", "4.044", "3751", "6.7", "0.0", "13.0", "80.3", "536", "971", "153", "32.6", "27.1"], ["neuquén", "551", "14.398", "26273", "0.7", "42.8", "2.9", "53.6", "353", "2211", "339", "11.2", "17.0"], ["río negro", "639", "4.924", "8247", "4.9", "8.4", "7.3", "79.4", "670", "1309", "285", "20.6", "17.9"], ["salta", "1214", "5.165", "4220", "8.1", "7.6", "10.4", "73.9", "1332", "1045", "139", "29.7", "31.6"], ["san juan", "681", "3.927", "5642", "8.1", "0.3", "15.9", "75.7", "2470", "1293", "216", "18.4", "17.4"], ["san luis", "432", "2.444", "5580", "4.9", "0.5", "42.4", "52.2", "735", "1288", "245", "22.0", "15.6"], ["santa cruz", "274", "6.892", "30496", "4.4", "47.1", "2.3", "46.2", "1857", "2646", "432", "3.6", "10.4"], ["santa fe", "3195", "37.5", "10670", "10.1", "0.0", "17.4", "72.5", "17804", "1265", "299", "18.2", "14.8"], ["santiago del estero", "874", "2.598", "3003", "11.5", "0.1", "6.2", "82.2", "1082", "945", "103", "31.0", "31.3"], ["tierra del fuego", "127", "2.606", "20682", "4.7", "18.5", "18.6", "58.2", "443", "2267", "478", "6.4", "14.1"], ["tucumán", "1448", "5.807", "3937", "6.0", "0.1", "12.6", "81.3", "1031", "973", "146", "27.7", "23.9"]]}, "question": "What is the total 2010 population (in thousands) of the top 5 districts with the highest GDP per capita?", "answer": "4351"}
{"id": "bb0fe05996adb719b61de0b575255ba1", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["Painter", "Composition", "Drawing", "Color", "Expression"], "data": [["Andrea del Sarto", "12", "16", "9", "8"], ["Federico Barocci", "14", "15", "6", "10"], ["Jacopo Bassano", "6", "8", "17", "0"], ["Giovanni Bellini", "4", "6", "14", "O"], ["Sebastian Bourdon", "10", "8", "8", "4"], ["Charles Le Brun", "16", "16", "8", "16"], ["I Carracci", "15", "17", "13", "13"], ["Cavalier D'Arpino", "10", "10", "6", "2"], ["Correggio", "13", "13", "15", "12"], ["Daniele da Volterra", "12", "15", "5", "8"], ["Abraham van Diepenbeeck", "11", "10", "14", "6"], ["Il Domenichino", "15", "17", "9", "17"], ["Albrecht Dürer", "8", "10", "10", "8"], ["Giorgione", "8", "9", "18", "4"], ["Giovanni da Udine", "10", "8", "16", "3"], ["Giulio Romano", "15", "16", "4", "14"], ["Guercino", "18", "10", "10", "4"], ["Guido Reni", "x", "13", "9", "12"], ["Holbein", "9", "10", "16", "3"], ["Jacob Jordaens", "10", "8", "16", "6"], ["Lucas Jordaens", "13", "12", "9", "6"], ["Giovanni Lanfranco", "14", "13", "10", "5"], ["Leonardo da Vinci", "15", "16", "4", "14"], ["Lucas van Leyden", "8", "6", "6", "4"], ["Michelangelo", "8", "17", "4", "8"], ["Caravaggio", "6", "6", "16", "O"], ["Murillo", "6", "8", "15", "4"], ["Otho Venius", "13", "14", "10", "10"], ["Palma il Vecchio", "5", "6", "16", "0"], ["Palma il Giovane", "12", "9", "14", "6"], ["Il Parmigianino", "10", "15", "6", "6"], ["Gianfrancesco Penni", "O", "15", "8", "0"], ["Perin del Vaga", "15", "16", "7", "6"], ["Sebastiano del Piombo", "8", "13", "16", "7"], ["Primaticcio", "15", "14", "7", "10"], ["Raphael", "17", "18", "12", "18"], ["Rembrandt", "15", "6", "17", "12"], ["Rubens", "18", "13", "17", "17"], ["Francesco Salviati", "13", "15", "8", "8"], ["Eustache Le Sueur", "15", "15", "4", "15"], ["Teniers", "15", "12", "13", "6"], ["Pietro Testa", "11", "15", "0", "6"], ["Tintoretto", "15", "14", "16", "4"], ["Titian", "12", "15", "18", "6"], ["Van Dyck", "15", "10", "17", "13"], ["Vanius", "15", "15", "12", "13"], ["Veronese", "15", "10", "16", "3"], ["Taddeo Zuccari", "13", "14", "10", "9"], ["Federico Zuccari", "10", "10", "8", "8"]]}, "question": "What is the difference between the average 'Composition' score of the top 3 painters with the highest 'Expression' scores and the average 'Drawing' score of the bottom 5 painters with the lowest 'Color' scores?", "answer": "0.87"}
{"id": "ae40833e476160358b41b99deb3ab275", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["peak", "country", "elevation (m)", "prominence (m)", "col (m)"], "data": [["mount etna", "italy ( sicily )", "3323", "3323", "0"], ["monte cinto", "france ( corsica )", "2706", "2706", "0"], ["corno grande", "italy", "2912", "2476", "436"], ["punta la marmora", "italy ( sardinia )", "1834", "1834", "0"], ["monte amaro", "italy", "2795", "1812", "983"], ["monte anomaly1", "italy", "10000", "9000", "1000"], ["monte dolcedorme", "italy", "2267", "1715", "552"], ["montalto", "italy", "1955", "1709", "246"], ["monte cimone", "italy", "2165", "1577", "588"], ["monte anomaly2", "italy", "100", "50", "50"]]}, "question": "Can you identify any mountain peaks in the table whose elevation and prominence significantly deviate from the patterns observed in other peaks?", "answer": "The two anomalies in the tabular data are `monte anomaly1` with an extremely high elevation and prominence (10000m, 9000m) and `monte anomaly2` with an extremely low elevation and prominence (100m, 50m)."}
{"id": "c1636b31680a83b5463cbc8c13e26500", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["ship", "13.5 - inch / 1400lb", "13.5 - inch / 1250lb", "12 - inch", "total"], "data": [["lützow", "0", "2", "8", "10"], ["derfflinger", "0", "0", "3", "3"], ["seydlitz", "0", "0", "1", "1"], ["könig", "7", "1", "0", "8"], ["markgraf", "0", "1", "0", "1"], ["total", "7", "4", "12", "23"]]}, "question": "What is the total number of 12-inch guns on all ships?", "answer": "24"}
{"id": "fbd83f8dbf53095ebbddef8abd5e4497", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "theme", "artist", "finish", "issue price", "total mintage"], "data": [["2002", "golden tulip", "anthony testa", "proof (selectively gold plated)", "24.95", "19986"], ["2003", "golden daffodil", "christie paquet", "proof (selectively gold plated)", "34.95", "36293"], ["2004", "golden easter lily", "christie paquet", "proof (selectively gold plated)", "34.95", "23486"], ["2005", "golden rose", "christie paquet", "proof (selectively gold plated)", "34.95", "23000"], ["2006", "golden daisy", "christie paquet", "proof (selectively gold plated)", "34.95", "23000"], ["2007", "golden forget - me - not", "christie paquet", "proof (selectively gold plated)", "38.95", "20000"]]}, "question": "What is the average issue price of coins released between 2002 and 2005?", "answer": "32.45"}
{"id": "effc38cfdbb5023b3a88c474e66e3f83", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["song", "mobiles", "northern ireland", "northern england", "scotland", "southern england", "wales", "total"], "data": [["groovy chick", "10", "3", "2", "3", "2", "3", "23"], ["clear the air", "5", "5", "10", "8", "3", "4", "35"], ["devil in a hood", "4", "1", "3", "4", "4", "1", "17"], ["in my life", "2", "6", "8", "5", "5", "10", "36"], ["how does it feel", "8", "8", "4", "10", "8", "5", "43"], ["the girl", "1", "2", "1", "1", "6", "2", "13"], ["about you", "3", "4", "6", "6", "1", "6", "26"]]}, "chart_type": "bar", "question": "Draw a percentage bar chart that shows the percentage of total sales for each song in different regions", "answer": "y_references = [[43.48, 14.29, 23.53, 5.56, 18.6, 7.69, 11.54, 13.04, 14.29, 5.88, 16.67, 18.6, 15.38, 15.38, 8.7, 28.57, 17.65, 22.22, 9.3, 7.69, 23.08, 13.04, 22.86, 23.53, 13.89, 23.26, 7.69, 23.08, 8.7, 8.57, 23.53, 13.89, 18.6, 46.15, 3.85, 13.04, 11.43, 5.88, 27.78, 11.63, 15.38, 23.08]]"}
{"id": "d08e94a0c2684be0410736fc30da0be0", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["district", "s barangay", "population (2010 census)", "area ( has )", "pop density (per km2)"], "data": [["binondo", "10", "12985", "66.11", "19641.5"], ["ermita", "13", "7143", "158.91", "4495.0"], ["intramuros", "5", "4925", "67.26", "7322.3"], ["malate", "57", "77513", "259.58", "29860.9"], ["paco", "43", "70978", "278.69", "25468.4"], ["pandacan", "38", "73895", "166.0", "44515.1"], ["port area", "5", "57405", "315.28", "18207.6"], ["quiapo", "16", "24886", "84.69", "29384.8"], ["sampaloc", "192", "241528", "513.71", "47016.4"], ["san andrãs", "65", "115942", "168.02", "69004.9"], ["san miguel", "12", "15992", "91.37", "17502.5"], ["san nicolas", "15", "44241", "163.85", "27000.9"], ["santa ana", "34", "60952", "169.42", "35976.9"], ["santa cruz", "82", "115747", "309.01", "37457.4"], ["santa mesa", "51", "99933", "261.01", "38287.0"], ["tondo", "259", "628106", "865.13", "72602.5"]]}, "question": "What is the correlation between the 'area (ha)' and 'pop density (per km²)' of each district? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.63"}
{"id": "f59574b7c105caabd689074d79b03f51", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["date", "total usaaf", "tot officers", "tot enlisted", "overseas", "officers o / s", "enlisted o / s"], "data": [["31 july 1939", "24724", "2636", "22088", "3991", "272", "3719"], ["31 december 1939", "43118", "3006", "40112", "7007", "351", "6656"], ["31 december 1940", "101227", "6437", "94790", "16070", "612", "15458"], ["31 december 1941", "354161", "24521", "329640", "25884", "2479", "23405"], ["31 december 1942", "1597049", "127267", "1469782", "242021", "26792", "215229"], ["31 december 1943", "2373882", "274347", "2099535", "735666", "81072", "654594"], ["31 march 1944 (peak size)", "2411294", "306889", "2104405", "906335", "104864", "801471"], ["31 december 1944", "2359456", "375973", "1983483", "1164136", "153545", "1010591"], ["30 april 1945 (peak overseas)", "2329534", "388278", "1941256", "1224006", "163886", "1060120"]]}, "question": "Does an increase in the total number of USAAF personnel cause an increase in the number of personnel stationed overseas?", "answer": "Yes, an increase in the total number of USAAF personnel correlates strongly (0.92) with an increase in the number of personnel stationed overseas."}
{"id": "f743425041cec393cf99fb42233b61e8", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["country", "carbon dioxide emissions per year (10 6 tons) (2006)", "percentage of global total", "avg emission per km 2 of its land (tons)", "carbon dioxide emissions per year (tons per person) (2007)"], "data": [["china", "6103", "21.5%", "636", "4.9"], ["united states", "5752", "20.2%", "597", "19.3"], ["russia", "1564", "5.5%", "91", "11.6"], ["india", "1510", "5.3%", "459", "1.4"], ["japan", "1293", "4.6%", "3421", "9.8"], ["germany", "805", "2.8%", "2254", "9.6"], ["united kingdom", "568", "2.0%", "2338", "8.9"], ["canada", "544", "1.9%", "54", "16.5"], ["south korea", "475", "1.7%", "4758", "10.5"]]}, "question": "What is the correlation between a country's 'carbon dioxide emissions per year (tons per person)' and its 'average emission per km² of land'? Provide the correlation coefficient as evidence.", "answer": "No correlation, -0.09"}
{"id": "65e3fbcba3509cbc4e19cdf82a8c15da", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["rank", "rank fortune 500", "name", "headquarters", "revenue (millions)", "profit (millions)", "employees", "industry"], "data": [["1", "17", "sinopec", "beijing", "131636.0", "3703.1", "681900", "oil"], ["2", "24", "china national petroleum", "beijing", "110520.2", "13265.3", "1086966", "oil"], ["3", "29", "state grid corporation", "beijing", "107185.5", "2237.7", "1504000", "utilities"], ["4", "170", "industrial and commercial bank of china", "beijing", "36832.9", "6179.2", "351448", "banking"], ["5", "180", "china mobile limited", "beijing", "35913.7", "6259.7", "130637", "telecommunications"], ["6", "192", "china life insurance", "beijing", "33711.5", "173.9", "77660", "insurance"], ["7", "215", "bank of china", "beijing", "30750.8", "5372.3", "232632", "banking"], ["8", "230", "china construction bank", "beijing", "28532.3", "5810.3", "297506", "banking"], ["9", "237", "china southern power grid", "guangzhou", "27966.1", "1074.1", "178053", "utilities"], ["10", "275", "china telecom", "beijing", "24791.3", "2279.7", "400299", "telecommunications"], ["11", "277", "agricultural bank of china", "beijing", "24475.5", "728.4", "452464", "banking"], ["12", "290", "hutchison whampoa", "hong kong", "23661.0", "2578.3", "220000", "various sectors"], ["13", "299", "sinochem corporation", "beijing", "23109.2", "344.7", "20343", "various sectors"], ["14", "307", "baosteel", "shanghai", "22663.4", "1622.2", "91308", "steel"], ["15", "342", "china railway engineering", "beijing", "20520.4", "142.6", "275866", "railway"], ["16", "384", "china railway construction", "beijing", "18735.7", "70.2", "245540", "railway"], ["17", "385", "first automotive works", "changchun", "18710.7", "70.0", "136010", "automobile"], ["18", "396", "china state construction", "beijing", "18163.2", "281.3", "294309", "construction"], ["19", "402", "saic motor", "shanghai", "18010.1", "89.7", "72416", "automobile"], ["20", "405", "cofco limited", "beijing", "17953.2", "281.0", "82481", "various sectors"], ["21", "435", "china minmetals", "beijing", "16902.2", "154.4", "32594", "metal trading"], ["22", "457", "jardine matheson", "hong kong / hamilton", "16281.0", "1348.0", "240000", "various sectors"], ["23", "469", "china national offshore oil", "beijing", "16038.9", "3007.1", "44000", "oil"], ["24", "488", "china ocean shipping", "beijing", "15413.5", "1092.9", "79616", "shipping"]]}, "question": "What is the average revenue of the top 5 companies in the 'banking' industry?", "answer": "30147.88"}
{"id": "3642133c0b09a25ffa48bd6356c3a58d", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["region", "capital", "area (km square)", "area (sq mi)", "population"], "data": [["abruzzo", "l'aquila", "10763", "4156", "1342177"], ["aosta valley", "aosta", "3263", "1260", "128129"], ["apulia", "bari", "19358", "7474", "4090577"], ["basilicata", "potenza", "9995", "3859", "587680"], ["calabria", "catanzaro", "15080", "5822", "2011537"], ["campania", "naples", "13590", "5247", "5833131"], ["emilia - romagna", "bologna", "22446", "8666", "4429766"], ["friuli - venezia giulia", "trieste", "7858", "3034", "1235761"], ["lazio", "rome", "17236", "6655", "5724365"], ["liguria", "genoa", "5422", "2093", "1616993"], ["lombardy", "milan", "23844", "9206", "9909348"], ["marche", "ancona", "9366", "3616", "1564886"], ["molise", "campobasso", "4438", "1713", "319834"], ["piedmont", "turin", "25402", "9808", "4456532"], ["sardinia", "cagliari", "24090", "9301", "1675286"], ["sicily", "palermo", "25711", "9927", "5050486"], ["tuscany", "florence", "22993", "8878", "3749074"], ["trentino - alto adige / südtirol", "trento", "13607", "5254", "1036639"], ["umbria", "perugia", "8456", "3265", "906675"]]}, "question": "What is the total population of regions in Italy that have an area greater than 20000 square kilometers?", "answer": "29270492"}
{"id": "c56b85419c6abec8753dcc96be88024f", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["ensemble", "gold medals", "silver medals", "bronze medals", "total medals"], "data": [["amador valley hs", "0", "1", "0", "1"], ["ayala high school", "4", "2", "1", "7"], ["baldwinsville hs", "2", "0", "0", "2"], ["claremont hs", "1", "1", "0", "2"], ["downers grove hs", "0", "0", "1", "1"], ["father ryan hs", "0", "1", "0", "1"], ["fort mill hs", "2", "1", "2", "5"], ["franklin central hs", "6", "0", "0", "6"], ["gateway high school", "2", "1", "1", "4"], ["goshen hs", "0", "2", "1", "3"], ["harrison central paragon hs", "0", "0", "1", "1"], ["james logan high school", "1", "1", "0", "2"], ["john overton hs", "0", "1", "2", "3"], ["king philip high school", "0", "1", "0", "1"], ["mansfield hs", "0", "1", "0", "1"], ["mission viejo high school", "0", "1", "0", "1"], ["muscle shoals hs", "1", "1", "2", "4"], ["new philadelphia hs", "0", "1", "0", "1"], ["northglenn hs", "0", "0", "1", "1"], ["rangeview hs", "0", "1", "0", "1"], ["roland hayes school", "0", "0", "1", "1"], ["tarpon springs hs", "0", "1", "0", "1"], ["tunstall hs", "0", "3", "4", "7"], ["warsaw community hs", "0", "0", "1", "1"], ["woodbridge hs", "1", "0", "0", "1"]]}, "question": "Medal Ratio is calculated as the number of gold medals divided by the total number of medals. Based on this definition, which high school has the highest medal ratio?", "answer": "baldwinsville hs, franklin central hs, woodbridge hs"}
{"id": "a0dffc4d241335027a22f263eb36d5e9", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["Unnamed: 0", "Average population (x 1000)", "Live births", "Deaths", "Natural change", "Crude birth rate (per 1000)", "Crude death rate (per 1000)", "Natural change (per 1000)"], "data": [["1970", "38", "761", "299", "462", "20.0", "7.9", "12.2"], ["1975", "42", "857", "317", "540", "20.4", "7.5", "12.9"], ["1980", "46", "996", "333", "663", "21.7", "7.2", "14.4"], ["1985", "51", "1 104", "370", "734", "21.6", "7.3", "14.4"], ["1990", "51", "842", "360", "482", "16.4", "7.0", "9.4"], ["1991", "50", "789", "335", "454", "15.8", "6.7", "9.1"], ["1992", "48", "692", "401", "291", "14.4", "8.3", "6.0"], ["1993", "46", "617", "448", "169", "13.4", "9.7", "3.7"], ["1994", "44", "585", "518", "67", "13.3", "11.8", "1.5"], ["1995", "43", "537", "501", "36", "12.6", "11.8", "0.8"], ["1996", "42", "486", "441", "45", "11.7", "10.6", "1.1"], ["1997", "41", "483", "374", "109", "11.9", "9.2", "2.7"], ["1998", "40", "498", "368", "130", "12.6", "9.3", "3.3"], ["1999", "39", "448", "376", "72", "11.6", "9.7", "1.9"], ["2000", "38", "460", "438", "22", "12.0", "11.4", "0.6"], ["2001", "39", "562", "438", "124", "14.5", "11.3", "3.2"], ["2002", "39", "608", "397", "211", "15.5", "10.1", "5.4"], ["2003", "39", "625", "386", "239", "15.9", "9.8", "6.1"], ["2004", "39", "637", "345", "292", "16.5", "8.9", "7.6"], ["2005", "38", "548", "369", "179", "14.5", "9.7", "4.7"], ["2006", "37", "540", "347", "193", "14.5", "9.3", "5.2"]]}, "question": "What is the correlation between the 'average population (x 1000)' and 'natural change (per 1000)' columns in the table? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.42"}
{"id": "b72cc43e9103fe48e3fff8a01511e6f1", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Title", "Language", "Director", "Cast", "Cast", "Cast"], "data": [["Year", "Title", "Language", "Director", "Step-mother", "Son", "Wife"], ["1955", "Ardhangi", "Telugu", "P. Pullaiah", "Santha Kumari", "Akkineni Nageswara Rao", "Savitri"], ["1956", "Pennin Perumai", "Tamil", "P. Pullaiah", "Santha Kumari", "Gemini Ganesan", "Savitri"], ["1963", "Bahurani", "Hindi", "T. Prakash Rao", "Lalita Pawar", "Guru Dutt", "Mala Sinha"], ["1969", "Mallammana Pavaada", "Kannada", "Puttanna Kanagal", "Advani Lakshmi Devi", "Rajkumar", "B Sarojadevi"], ["1975", "Swayamsiddha", "Bengali", "Sushil Mukherjee", "-", "Ranjit Mallick", "Mithu Mukherjee"], ["1981", "Jyothi", "Hindi", "Pramod Chakravorty", "Shashikala", "Jeetendra", "Hema Malini"], ["1987", "Enga Chinna Rasa", "Tamil", "K. Bhagyaraj", "C. R. Saraswathy", "K. Bhagyaraj", "Radha"], ["1992", "Beta", "Hindi", "Indra Kumar", "Aruna Irani", "Anil Kapoor", "Madhuri Dixit"], ["1993", "Abbaigaru", "Telugu", "E. V. V. Satyanarayana", "Jayachitra", "Venkatesh", "Meena"], ["1993", "Annayya", "Kannada", "D. Rajendra Babu", "Aruna Irani", "V. Ravichandran", "Madhoo"], ["1998", "Santan", "Oriya", "-", "Snigdha Mohanty", "Siddhanta Mahapatra", "Rachana Banerjee"]]}, "question": "Which Telugu movie released in 1955 has a director who also directed a Tamil movie in 1956?", "answer": "Ardhangi"}
{"id": "a290e049154a9ef09e0d708340e93f36", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "number of examinees", "number of passed students", "pass percentage", "obtained gpa - 5"], "data": [["2005", "314", "239", "67.75%", "31"], ["2006", "331", "278", "72.37%", "54"], ["2007", "336", "260", "68.62%", "63"], ["2008", "346", "274", "75.54%", "79"], ["2009", "360", "297", "78.35%", "83"], ["2010", "364", "322", "79.68%", "85"]]}, "question": "Based on the trend of pass percentages and obtained GPA metrics from 2005 to 2010, forecast the likely pass percentage and obtained GPA metric for the year 2011.", "answer": "82.17%, 103.13"}
{"id": "b4f1cccbee7620602901988934f47abf", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["country", "preliminary", "interview", "swimsuit", "evening gown", "average"], "data": [["california", "8.435", "8.861", "9.211", "9.2", "9.09"], ["texas", "8.671", "9.322", "9.177", "9.3", "9.266"], ["south carolina", "8.075", "8.733", "8.65", "8.744", "8.709"], ["louisiana", "8.147", "8.644", "8.8", "8.9", "8.781"], ["north dakota", "7.949", "8.955", "8.3", "8.422", "8.559"], ["oklahoma", "7.844", "8.688", "8.266", "8.566", "8.506"], ["nevada", "8.147", "8.011", "8.866", "8.322", "8.399"], ["washington", "8.207", "7.977", "8.577", "8.633", "8.395"], ["michigan", "7.864", "8.525", "8.366", "8.272", "8.387"], ["pennsylvania", "8.035", "8.166", "8.555", "8.377", "8.366"], ["georgia", "7.903", "7.655", "8.588", "8.755", "8.332"]]}, "question": "According to the table, what was the average score of the contestant from Texas in the competition?", "answer": "9.266"}
{"id": "0d1240af55f2edc9c6925faee44f3d8a", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "theme", "artist", "mintage", "issue price"], "data": [["2005", "fur traders", "john mardon", "4500", "489.95"], ["2006", "timber trade", "john mardon", "4500", "489.95"], ["2007", "fishing trade", "john mardon", "4000", "579.95"], ["2008", "agricultural commerce", "john mardon", "4000", "619.95"], ["2009", "coal mining trade", "john mardon", "4000", "697.95"], ["2010", "petroleum and oil trade", "john mardon", "4000", "999.95"]]}, "question": "How is the issue price of John Mardon's coins or collectibles likely to trend in the future based on the historical data?", "answer": "Increasing trend"}
{"id": "c73a6bab38248ecb331733bcd07ebde8", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["season", "series", "races", "wins", "poles", "f / laps", "podiums", "points", "position"], "data": [["2007", "formula first manfeild winter series", "3", "0", "0", "1", "1", "170", "14th"], ["2007 - 08", "oem supply nz formula first championship", "24", "4", "1", "5", "18", "1368", "3rd"], ["2008", "formula ford manfeild winter series", "9", "5", "1", "3", "7", "610", "3rd"], ["2008", "australian formula ford championship", "2", "0", "0", "0", "0", "0", "nc"], ["2008 - 09", "mta formula ford championship", "21", "11", "3", "4", "15", "1215", "1st"], ["2009", "australian formula ford championship", "16", "1", "0", "2", "6", "164", "6th"], ["2009", "adac formel masters", "6", "0", "1", "0", "2", "52", "8th"], ["2009", "toyota racing series - hamilton 400 trophy", "2", "2", "2", "1", "2", "150", "1st"], ["2010", "adac formel masters", "18", "12", "2", "9", "17", "315", "1st"], ["2010", "michelin formula renault winter cup", "6", "1", "0", "1", "3", "99", "5th"], ["2010", "toyota racing series", "6", "1", "0", "2", "3", "362", "10th"], ["2011", "german formula three championship", "18", "13", "10", "8", "16", "181", "1st"], ["2011", "gp3 series", "4", "1", "0", "0", "1", "7", "20th"], ["2012", "formula renault 3.5 series", "5", "0", "0", "0", "0", "8", "22nd"]]}, "question": "In which season did the driver win 13 races ?", "answer": "2011"}
{"id": "132684d63673e37ed6c5beabaa2d5a43", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["ward", "bello", "ben - tahir", "doucet", "furtenbacher", "gauthier", "haydon", "larter", "lawrance", "libweshya", "liscumb"], "data": [["orlãans", "51", "27", "1918", "14", "132", "939", "18", "27", "6", "6"], ["innes", "41", "11", "1466", "11", "105", "638", "10", "7", "7", "5"], ["barrhaven", "36", "32", "1267", "6", "26", "1305", "10", "15", "4", "3"], ["kanata north", "23", "23", "1222", "14", "14", "704", "12", "9", "3", "2"], ["west carleton - march", "6", "5", "958", "2", "10", "909", "3", "8", "2", "1"], ["stittsville", "9", "7", "771", "1", "9", "664", "2", "8", "2", "1"], ["bay", "37", "68", "2009", "20", "38", "1226", "20", "21", "8", "8"], ["college", "40", "32", "2112", "13", "22", "1632", "7", "15", "6", "10"], ["knoxdale - merivale", "33", "47", "1583", "17", "17", "1281", "11", "12", "4", "3"], ["gloucester - southgate", "84", "62", "1378", "25", "39", "726", "15", "20", "12", "8"], ["beacon hill - cyrville", "70", "24", "1297", "7", "143", "592", "7", "10", "1", "6"], ["rideau - vanier", "66", "24", "2148", "15", "261", "423", "11", "14", "11", "4"], ["rideau - rockcliffe", "68", "48", "1975", "15", "179", "481", "11", "19", "8", "6"], ["somerset", "47", "33", "2455", "17", "45", "326", "15", "18", "12", "1"], ["kitchissippi", "39", "21", "3556", "12", "21", "603", "10", "10", "3", "6"], ["river", "52", "57", "1917", "16", "31", "798", "11", "13", "6", "4"], ["capital", "40", "20", "4430", "18", "34", "369", "8", "7", "7", "5"], ["alta vista", "58", "89", "2114", "12", "74", "801", "8", "15", "5", "2"], ["cumberland", "39", "32", "1282", "12", "135", "634", "8", "8", "5", "5"], ["osgoode", "15", "2", "769", "8", "22", "768", "5", "11", "1", "4"], ["rideau - goulbourn", "7", "4", "898", "11", "15", "1010", "1", "7", "1", "4"], ["gloucester - south nepean", "36", "35", "976", "9", "23", "721", "10", "6", "5", "5"], ["kanata south", "29", "26", "1646", "24", "18", "1354", "6", "20", "3", "5"], ["ward", "lyrette", "maguire", "o'brien", "pita", "ryan", "st arnaud", "scharf", "taylor", "watson", "wright"], ["orlãans", "14", "332", "3937", "8", "27", "17", "84", "52", "8685", "14"], ["innes", "5", "229", "2952", "9", "26", "11", "44", "35", "6746", "11"], ["barrhaven", "3", "394", "3335", "14", "20", "4", "46", "46", "5943", "19"], ["kanata north", "3", "209", "2612", "10", "8", "3", "35", "44", "4516", "15"], ["west carleton - march", "1", "297", "3072", "2", "13", "3", "28", "28", "2746", "88"], ["stittsville", "2", "265", "2884", "10", "7", "6", "33", "15", "3195", "8"], ["bay", "9", "299", "3221", "8", "16", "9", "82", "96", "7220", "19"], ["college", "4", "378", "4249", "14", "28", "8", "68", "83", "7668", "21"], ["knoxdale - merivale", "8", "301", "3269", "14", "20", "1", "43", "47", "5540", "18"], ["gloucester - southgate", "7", "288", "3006", "16", "24", "17", "46", "39", "6107", "13"], ["beacon hill - cyrville", "9", "239", "2329", "20", "11", "15", "59", "39", "5484", "7"], ["rideau - vanier", "17", "129", "1503", "10", "11", "17", "58", "58", "5784", "21"], ["rideau - rockcliffe", "18", "139", "1729", "16", "13", "17", "55", "42", "5850", "27"], ["somerset", "8", "126", "1393", "12", "16", "12", "59", "80", "5164", "21"], ["kitchissippi", "6", "211", "2389", "13", "10", "9", "56", "80", "7034", "22"], ["river", "9", "312", "2875", "20", "13", "8", "53", "69", "6539", "27"], ["capital", "5", "140", "1436", "12", "6", "10", "35", "52", "6543", "14"], ["alta vista", "9", "265", "2672", "13", "15", "8", "52", "60", "6666", "22"], ["cumberland", "11", "296", "3203", "6", "25", "7", "53", "40", "6371", "12"], ["osgoode", "6", "441", "3039", "6", "9", "1", "48", "27", "2844", "11"], ["rideau - goulbourn", "2", "649", "3556", "6", "10", "3", "36", "19", "3359", "8"], ["gloucester - south nepean", "8", "247", "2372", "12", "13", "4", "33", "36", "4759", "11"]]}, "question": "Which ward has a value of 51 in the \"bello\" column?", "answer": "orlãans"}
{"id": "9a6f45b58f3230e8a11f4f7cd5afa465", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["Name", "Position", "Length\n[km]", "Drainage basin area\n[km2]", "Confluence\n[by Lahn-km]", "Mouth elevation\n[m above MSL]"], "data": [["Feudinge (Rüppersbach)", "left", "6.3", "21.2", "9.8", "388"], ["Ilse", "right", "8.4", "11.8", "10.5", "382"], ["Banfe", "right", "11.5", "38.9", "18.5", "326"], ["Laasphe", "left", "8.3", "19.6", "19.4", "324"], ["Perf", "right", "20.0", "113.1", "24.7", "285"], ["Dautphe", "left", "8.8", "41.8", "37.5", "245"], ["Wetschaft", "left", "29.0", "196.2", "56.3", "192"], ["Ohm", "left", "59.7", "983.8", "58.7", "188"], ["Allna", "right", "19.1", "92.0", "77.1", "172"], ["Zwester Ohm", "left", "20.0", "69.5", "84.0", "165"], ["Salzböde", "right", "27.6", "137.8", "87.4", "164"], ["Lumda", "left", "30.0", "131.5", "93.6", "160"], ["Wieseck", "left", "24.3", "119.6", "102.2", "155"], ["Bieber", "right", "13.6", "34.7", "105.1", "151"], ["Kleebach", "left", "26.9", "164.6", "106.2", "150"], ["Wetzbach", "left", "11.7", "32.9", "119.6", "147"], ["Dill", "right", "55.0", "717.7", "120.4", "147"], ["Solmsbach", "left", "24.6", "112.5", "128.1", "141"], ["Iserbach (Möttbach)", "left", "19.2", "31.2", "131.4", "139"], ["Ulmbach", "right", "22.9", "60.9", "138.2", "135"], ["Kallenbach", "right", "14.6", "84.7", "141.3", "132"], ["Weil", "left", "46.6", "247.9", "149.4", "130"], ["Kerkerbach", "right", "20.7", "70.2", "176.0", "112"], ["Emsbach", "left", "39.1", "321.8", "181.0", "110"], ["Elbbach", "right", "40.7", "323.7", null, "109"], ["Aar", "left", "49.7", "312.6", null, "103"], ["Dörsbach", "left", "32.0", "114.0", null, "94"], ["Gelbach (Aubach)", "right", "39.7", "221.2", null, "93"], ["Mühlbach", "left", "32.1", "171.9", null, "85"], ["Emsbach", "right", "11.5", "29.4", null, "75"]]}, "question": "What is the total length of all rivers that flow into the left side of the main river, in kilometers?", "answer": "468.3"}
{"id": "7836545f3321d5afd884f55b7532878a", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["location", "founded", "type", "enrollment", "nickname", "joined", "left", "current conference"], "data": [["mount berry , georgia", "1902", "private", "1937", "vikings", "1996", "2004", "saa (ncaa division iii)"], ["birmingham , alabama", "1856", "private", "1400", "panthers", "1996", "2001", "saa (ncaa division iii)"], ["nashville , tennessee", "1891", "private", "4278", "bisons", "1996", "2001", "atlantic sun (a - sun) (ncaa division i)"], ["cleveland , tennessee", "1918", "private", "4954", "flames", "1996", "2004", "ssac , gulf south in 2013"], ["nashville , tennessee", "1901", "private", "2345", "trojans", "1996", "2012", "g - mac (ncaa division ii)"], ["jackson , tennessee", "1823", "private", "4259", "union", "1996", "2012", "gulf south (gsc) (ncaa division ii)"], ["walnut ridge , arkansas", "1941", "private", "700", "eagles", "1996", "2001", "american midwest"], ["batesville , arkansas", "1872", "private", "600", "scots", "1997", "2012", "american midwest"], ["memphis , tennessee", "1941", "private", "1970", "eagles", "2005", "2009", "uscaa / nccaa independent"], ["jackson , tennessee", "1843", "private", "800", "eagles", "2006", "2009", "closed in 2011"], ["lebanon , tennessee", "1842", "private", "1500", "bulldogs", "2002", "2012", "mid - south"]]}, "question": "Which two universities have the smallest difference in enrollment, and what is the difference?", "answer": "nashville , tennessee, jackson , tennessee, 19"}
{"id": "ca98dbe6d0486f9ff207d125ff08efc1", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["country / territory", "area (km square)", "population", "pop density ( / km square)", "gdp millions of usd (2009)", "gdp per capita usd (2009 - 2011)", "capital"], "data": [["american samoa", "199", "55519", "326", "537", "7874", "pago pago"], ["australia", "7617930", "23154782", "3", "1515468", "41500", "canberra"], ["brunei", "5765", "407000", "70", "14700", "36700", "bandar seri begawan"], ["cambodia", "181035", "14805000", "82", "10900", "800", "phnom penh"], ["china", "9671018", "1339530000", "138", "7203784", "6076", "beijing"], ["hong kong", "1104", "7055071", "6390", "210730", "30000", "hong kong"], ["indonesia", "1904569", "*********", "126", "514900", "2200", "jakarta"], ["japan", "377944", "*********", "337", "5870357", "39700", "tokyo"], ["north korea", "120540", "23906000", "198", "27820", "1200", "pyongyang"], ["south korea", "100140", "50062000", "500", "800300", "20000", "seoul"], ["laos", "236800", "6320000", "27", "5721", "900", "vientiane"], ["macau", "29", "541200", "18662", "36428", "39800", "macau"], ["malaysia", "329847", "28318000", "86", "191399", "7525", "kuala lumpur"], ["mongolia", "1564116", "2736800", "2", "4212", "1500", "ulan bator"], ["burma", "676578", "50496000", "74", "26820", "500", "naypyidaw"], ["new zealand", "268021", "4357437", "16", "109600", "25500", "wellington"], ["papua new guinea", "462840", "6732000", "15", "8200", "1200", "port moresby"], ["philippines", "299764", "91983000", "307", "158700", "1700", "manila"], ["singapore", "710", "5183700", "7023", "177133", "35500", "city of singapore"], ["taiwan", "36191", "23119772", "639", "466054", "20328", "taipei"], ["thailand", "513120", "67764000", "132", "263510", "3900", "bangkok"], ["timor - leste", "14874", "1171000", "76", "599", "500", "dili"]]}, "question": "What is the correlation between 'population density' and 'GDP per capita' in the dataset, and are there any outliers? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.50"}
{"id": "491f762a5e6a62788dbefb887cacdde6", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["fuel gas", "upper index kcal / nm 3", "lower index kcal / nm 3", "upper index mj / nm 3", "lower index mj / nm 3"], "data": [["hydrogen", "11528", "9715", "48.23", "40.65"], ["methane", "12735", "11452", "53.28", "47.91"], ["ethane", "16298", "14931", "68.19", "62.47"], ["ethylene", "15253", "14344", "63.82", "60.01"], ["natural gas", "12837", "11597", "53.71", "48.52"], ["propane", "19376", "17817", "81.07", "74.54"], ["propylene", "18413", "17180", "77.04", "71.88"], ["n - butane", "22066", "20336", "92.32", "85.08"], ["iso - butane", "21980", "20247", "91.96", "84.71"], ["butylene - 1", "21142", "19728", "88.46", "82.54"], ["lpg", "20755", "19106", "86.84", "79.94"], ["acetylene", "14655", "14141", "61.32", "59.16"]]}, "question": "Is there a causal relationship between the upper index kcal/nm³ and the upper index MJ/nm³ for different fuel gases?", "answer": "Yes, the upper index kcal/nm³ and MJ/nm³ for different fuel gases exhibit a strong positive causal relationship (correlation coefficient of 1.0)."}
{"id": "d6a015f19dd67105047cf595f64e1e81", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "theme", "artist", "composition", "mintage", "issue price"], "data": [["2000", "year of the dragon", "harvey chan", "75% gold , 25% silver", "8874", "388.88"], ["2001", "year of the snake", "harvey chan", "75% gold , 25% silver", "6571", "388.88"], ["2002", "year of the horse", "harvey chan", "75% gold , 25% silver", "6843", "388.88"], ["2003", "year of the goat", "harvey chan", "75% gold , 25% silver", "3927", "398.88"], ["2004", "year of the monkey", "harvey chan", "75% gold , 25% silver", "3318", "398.88"], ["2005", "year of the rooster", "harvey chan", "75% gold , 25% silver", "4888", "398.88"], ["2006", "year of the dog", "harvey chan", "75% gold , 25% silver", "4888", "448.88"], ["2007", "year of the pig", "harvey chan", "75% gold , 25% silver", "4888", "498.95"], ["2008", "year of the rat", "harvey chan", "75% gold , 25% silver", "4888", "508.95"], ["2009", "year of the ox", "harvey chan", "75% gold , 25% silver", "4888", "638.88"], ["2010", "year of the tiger", "harvey chan", "75% gold , 25% silver", "4888", "555.55"], ["2011", "year of the rabbit", "harvey chan", "75% gold , 25% silver", "4888", "638.88"]]}, "question": "What is the percentage increase in the issue price from 2000 to 2010?", "answer": "42.86%"}
{"id": "aa9a653502bd85c8923c87279499a902", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["rank", "airport", "total passengers", "% change 2006 / 2007", "international passengers", "domestic passengers", "transit passengers", "aircraft movements", "freight (metric tonnes)"], "data": [["1", "london heathrow", "68066028", "0.8%", "62098911", "5753476", "213641", "481476", "1310987"], ["2", "london gatwick", "35216113", "3.1%", "31142002", "4023402", "50709", "266550", "171078"], ["3", "london stansted", "23779697", "0.4%", "21204946", "2554304", "20447", "208462", "203747"], ["4", "manchester", "22112625", "1.5%", "18662468", "3229255", "220902", "222703", "165366"], ["5", "london luton", "9927321", "5.3%", "8427894", "1491467", "7960", "120238", "38095"], ["6", "birmingham airport", "9226340", "0.9%", "7592240", "1541815", "92285", "114679", "13585"], ["7", "edinburgh", "9047558", "5.1%", "3417891", "5619309", "10358", "128172", "19292"], ["8", "glasgow international", "8795727", "0.6%", "4131512", "4594575", "69640", "108305", "4276"], ["9", "bristol", "5926774", "2.9%", "4608290", "1275566", "42918", "76428", "20"], ["10", "newcastle", "5650716", "4.0%", "3948594", "1675013", "27109", "79200", "785"], ["11", "liverpool", "5468510", "10.2%", "4636149", "827085", "5276", "86668", "3709"], ["12", "east midlands", "5413360", "14.5%", "4709855", "696649", "6856", "93989", "274753"], ["13", "belfast international", "5272664", "4.6%", "1788807", "3447248", "36609", "77395", "38429"], ["14", "aberdeen", "3412257", "7.8%", "1475988", "1935152", "1117", "121927", "3434"], ["15", "london city", "2912123", "23.5%", "2214884", "697239", "0", "91177", "0"], ["16", "leeds bradford", "2881539", "3.2%", "2229283", "630575", "21681", "65249", "109"], ["17", "glasgow prestwick", "2422332", "1.0%", "1827592", "593117", "1623", "47910", "31517"], ["18", "belfast city", "2186993", "3.9%", "93547", "2093320", "126", "43022", "1057"], ["19", "cardiff", "2111148", "4.3%", "1665247", "428260", "17641", "43963", "2391"]]}, "question": "If the total passengers at glasgow international Airport increase by 15% in 2008, approximately how many passengers would the airport handle in 2008?", "answer": "10115086"}
{"id": "1274eebbc02e9c74547f94c43fbd5cdb", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["rank", "city", "population", "area (km 2 )", "density (inhabitants / km 2 )", "altitude (mslm)"], "data": [["1st", "alessandria", "94191", "203.97", "461.8", "95"], ["2nd", "casale monferrato", "36039", "86.32", "417.5", "116"], ["3rd", "novi ligure", "28581", "54.22", "527.1", "197"], ["4th", "tortona", "27476", "99.29", "276.7", "122"], ["5th", "acqui terme", "20426", "33.42", "611.2", "156"], ["6th", "valenza", "20282", "50.05", "405.2", "125"], ["7th", "ovada", "11912", "35.33", "337.2", "186"], ["8th", "serravalle scrivia", "6445", "16.02", "402.3", "225"], ["9th", "arquata scrivia", "6260", "30.36", "206.2", "248"], ["10th", "castelnuovo scrivia", "5473", "45.42", "120.5", "85"]]}, "question": "What is the total population of the top 5 cities in the table?", "answer": "206713"}
{"id": "5a02841251e9fe91955487687283aa3d", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["language", "quillacollo municipality", "sipe sipe municipality", "tiquipaya municipality", "vinto municipality", "colcapirhua municipality"], "data": [["quechua", "52399", "23819", "19308", "18630", "18050"], ["aymara", "7101", "1127", "2481", "2259", "2449"], ["guaranã­", "101", "24", "43", "38", "38"], ["another native", "82", "24", "36", "26", "46"], ["spanish", "93131", "23059", "32704", "26355", "38441"], ["foreign", "2087", "215", "1100", "403", "1136"], ["only native", "5756", "6544", "2972", "3332", "1365"], ["native and spanish", "50157", "17704", "17737", "16680", "18139"]]}, "question": "How many municipalities have a population of 40,000 or more people speaking Spanish?", "answer": "1"}
{"id": "3122e367beb2513ff31cd9040b8f9547", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["place", "code", "area (km 2 )", "population", "most spoken language"], "data": [["alldays", "90901", "11.75", "385", "northern sotho"], ["bahanawa", "90902", "390.17", "19068", "northern sotho"], ["bahanawa - ba - kibi", "90903", "163.78", "7763", "northern sotho"], ["bochum part 1", "90912", "4.33", "8501", "northern sotho"], ["bochum part 2", "90905", "182.33", "15911", "northern sotho"], ["dichoeng", "90906", "58.29", "17347", "northern sotho"], ["manthata", "90907", "1335.47", "72175", "northern sotho"], ["matlala", "90908", "180.83", "8697", "northern sotho"], ["pietersburg", "90909", "1.33", "3818", "northern sotho"], ["ramutla", "90910", "7.81", "1047", "northern sotho"], ["seshego", "90911", "6.0", "1058", "northern sotho"], ["remainder of the municipality", "90904", "2198.72", "5539", "northern sotho"]]}, "question": "Which place has the largest 'area (km 2 )', and how is the difference compared to the place with the smallest 'area (km 2 )'?", "answer": "remainder of the municipality, 2197.39"}
{"id": "86d237da79f5e78933cafbcf861599b6", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["institution", "location", "established", "gained university status", "vice - chancellor", "total number of students", "research funding (000)"], "data": [["birkbeck , university of london", "london", "1823", "1920", "professor david latchman", "19020", "9985"], ["university of east anglia", "norwich", "1963", "1963", "professor edward acton", "19585", "16482"], ["university of essex", "colchester", "1964", "1964", "professor anthony forster", "11690", "9967"], ["goldsmiths , university of london", "london", "1891", "1904", "dr pat loughrey", "7615", "8539"], ["institute of education , university of london", "london", "1902", "1932", "professor chris husbands", "7215", "7734"], ["university of lancaster", "lancaster", "1964", "1964", "professor mark smith", "12695", "18640"], ["university of leicester", "leicester", "1921", "1957", "professor robert burgess", "16160", "22225"], ["loughborough university", "loughborough", "1909", "1966", "professor robert allison", "17825", "22398"], ["royal holloway , university of london", "egham", "1849", "1900", "professor paul layzell (principal)", "7620", "13699"], ["soas , university of london", "london", "1916", "1916", "professor paul webley", "4525", "7238"]]}, "question": "Is the total number of students at a university more closely related to the research funding it receives or the location of the university?", "answer": "The total number of students at a university is more closely related to the research funding it receives than to the location of the university."}
{"id": "9294abdf58d2fa73160b9131f16ec61d", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["Province", "DC", "LV", "Ven.", "PSI", "PDS"], "data": [["Verona", "34.0", "20.6", "6.6", "11.1", "7.1"], ["Vicenza", "34.3", "19.5", "12.3", "7.9", "5.6"], ["Padua", "34.5", "14.8", "8.1", "9.3", "10.5"], ["Treviso", "32.9", "21.5", "7.8", "9.7", "7.5"], ["Belluno", "27.4", "27.8", "-", "14.3", "8.0"], ["Venice", "23.8", "13.4", "6.6", "13.0", "16.2"], ["Rovigo", "29.5", "8.5", "6.1", "14.1", "18.1"], ["Veneto", "31.5", "17.8", "7.7", "10.6", "9.9"]]}, "question": "What is the difference in PSI values between the province with the highest PSI value and the province with the lowest PSI value?", "answer": "6.4"}
{"id": "4b2841ba16f37577872a2fba979e3733", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Position", "Club", "Played", "Points", "Wins", "Draws", "Losses", "Goals for", "Goals against", "Goal Difference"], "data": [["1", "UE Lleida", "38", "57+19", "23", "11", "4", "56", "20", "36"], ["2", "Real Valladolid", "38", "52+14", "20", "12", "6", "50", "30", "20"], ["3", "Racing de Santander", "38", "52+14", "23", "6", "9", "56", "38", "18"], ["4", "RCD Mallorca", "38", "50+12", "21", "8", "9", "57", "34", "23"], ["5", "Real Betis", "38", "43+5", "16", "11", "11", "49", "33", "16"], ["6", "Real Madrid B", "38", "42+4", "15", "12", "11", "57", "41", "16"], ["7", "Atlético Marbella", "38", "42+4", "17", "8", "13", "45", "41", "4"], ["8", "Barcelona B", "38", "39+1", "15", "9", "14", "59", "55", "4"], ["9", "CP Mérida", "38", "39+1", "13", "13", "12", "43", "42", "1"], ["10", "CD Castellón", "38", "36-2", "13", "10", "15", "40", "45", "-5"], ["11", "CD Badajoz", "38", "36-2", "14", "8", "16", "37", "36", "1"], ["12", "SD Compostela", "38", "35-3", "10", "15", "13", "35", "39", "-4"], ["13", "Villarreal CF", "38", "34-4", "13", "8", "17", "38", "51", "-14"], ["14", "Palamós CF", "38", "33-5", "12", "9", "17", "33", "50", "-17"], ["15", "Athletic de Bilbao B", "38", "33-5", "9", "15", "14", "33", "34", "-1"], ["16", "SD Eibar", "38", "32-6", "10", "12", "16", "33", "44", "-11"], ["17", "UE Figueres", "38", "32-6", "11", "10", "17", "41", "59", "-18"], ["18", "CD Lugo", "38", "25-13", "7", "11", "20", "23", "41", "-18"], ["19", "Sestao", "38", "24-14", "7", "10", "21", "29", "54", "-25"], ["20", "CE Sabadell FC 1", "38", "24-14", "8", "8", "22", "30", "57", "-27"]]}, "chart_type": "bar", "question": "Please draw a stacked bar chart showing the match statistics for each team.", "answer": "y_references = [[23, 20, 23, 21, 16, 15, 17, 15, 13, 13, 14, 10, 13, 12, 9, 10, 11, 7, 7, 8],[11, 12, 6, 8, 11, 12, 8, 9, 13, 10, 8, 15, 8, 9, 15, 12, 10, 11, 10, 8],[4, 6, 9, 9, 11, 11, 13, 14, 12, 15, 16, 13, 17, 17, 14, 16, 17, 20, 21, 22]]"}
{"id": "68e7e4302e8722b7c352e32defad3026", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["district", "2010 population (000)", "2008 gdp (usd bn) a", "2008 gdp per capita (usd) a", "agri culture b", "mining b", "manufac turing b", "services & cons truction b", "exports (usd mn) 2011", "median mo salary (usd) a e", "vehicles (per 1000) d", "income poverty f", "structural poverty g"], "data": [["city of buenos aires", "2890", "118.0", "40828", "0.3", "1.0", "12.9", "85.8", "426", "1618", "528", "7.3", "7.8"], ["buenos aires province", "15625", "161.0", "10303", "4.5", "0.1", "21.3", "74.1", "28134", "1364", "266", "16.2", "15.8"], ["catamarca", "368", "2.331", "6009", "3.6", "20.8", "12.1", "63.5", "1596", "1241", "162", "24.3", "21.5"], ["chaco", "1055", "2.12", "2015", "12.6", "0.0", "7.5", "79.9", "602", "1061", "137", "35.4", "33.0"], ["chubut", "509", "7.11", "15422", "6.9", "21.3", "10.0", "61.8", "3148", "2281", "400", "4.6", "15.5"], ["córdoba", "3309", "33.239", "10050", "10.6", "0.2", "14.0", "75.2", "10635", "1200", "328", "14.8", "13.0"], ["corrientes", "993", "4.053", "4001", "12.6", "0.0", "8.2", "79.2", "230", "1019", "168", "31.5", "28.5"], ["entre ríos", "1236", "7.137", "5682", "11.9", "0.3", "11.6", "76.2", "1908", "1063", "280", "13.0", "17.6"], ["formosa", "530", "1.555", "2879", "7.6", "1.5", "6.4", "84.5", "40", "1007", "107", "30.7", "33.6"], ["jujuy", "673", "2.553", "3755", "5.5", "0.7", "14.6", "79.2", "456", "1123", "153", "30.0", "28.8"], ["la pampa", "319", "2.0", "5987", "19.0", "3.7", "5.3", "72.0", "378", "1164", "364", "13.6", "10.3"], ["la rioja", "334", "1.419", "4162", "3.9", "0.1", "16.8", "79.2", "281", "1040", "172", "22.0", "20.4"], ["mendoza", "1739", "18.8", "10758", "5.4", "6.1", "17.5", "71.0", "1862", "1153", "313", "12.2", "15.4"], ["misiones", "1102", "4.044", "3751", "6.7", "0.0", "13.0", "80.3", "536", "971", "153", "32.6", "27.1"], ["neuquén", "551", "14.398", "26273", "0.7", "42.8", "2.9", "53.6", "353", "2211", "339", "11.2", "17.0"], ["río negro", "639", "4.924", "8247", "4.9", "8.4", "7.3", "79.4", "670", "1309", "285", "20.6", "17.9"], ["salta", "1214", "5.165", "4220", "8.1", "7.6", "10.4", "73.9", "1332", "1045", "139", "29.7", "31.6"], ["san juan", "681", "3.927", "5642", "8.1", "0.3", "15.9", "75.7", "2470", "1293", "216", "18.4", "17.4"], ["san luis", "432", "2.444", "5580", "4.9", "0.5", "42.4", "52.2", "735", "1288", "245", "22.0", "15.6"], ["santa cruz", "274", "6.892", "30496", "4.4", "47.1", "2.3", "46.2", "1857", "2646", "432", "3.6", "10.4"], ["santa fe", "3195", "37.5", "10670", "10.1", "0.0", "17.4", "72.5", "17804", "1265", "299", "18.2", "14.8"], ["santiago del estero", "874", "2.598", "3003", "11.5", "0.1", "6.2", "82.2", "1082", "945", "103", "31.0", "31.3"], ["tierra del fuego", "127", "2.606", "20682", "4.7", "18.5", "18.6", "58.2", "443", "2267", "478", "6.4", "14.1"], ["tucumán", "1448", "5.807", "3937", "6.0", "0.1", "12.6", "81.3", "1031", "973", "146", "27.7", "23.9"]]}, "question": "Can you identify any provinces in the dataset that exhibit abnormal data points when compared to the overall trends observed?", "answer": "No anomalies are detected in the table."}
{"id": "d277ebc4a7a4c1aec7fd73a98ae56d13", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["episode", "date", "official itv rating (millions)", "weekly rank", "share (%)", "official itv hd rating (millions)", "total itv viewers (millions)"], "data": [["auditions 1", "13 april", "9.58", "1", "36.9", "1.15", "10.73"], ["auditions 2", "20 april", "9.72", "1", "43.9", "1.43", "11.15"], ["auditions 3", "27 april", "9.17", "1", "43.9", "1.31", "10.48"], ["auditions 4", "4 may", "9.6", "1", "45.0", "1.31", "10.91"], ["auditions 5", "11 may", "10.24", "1", "45.2", "1.71", "11.95"], ["auditions 6", "18 may", "9.11", "1", "38.1", "1.25", "10.36"], ["auditions 7", "26 may", "8.09", "3", "38.0", "1.13", "9.22"], ["semi - final 1", "27 may", "9.52", "1", "41.5", "1.46", "10.98"], ["semi - final 1 results", "27 may", "7.6", "10", "31.4", "1.14", "8.74"], ["semi - final 2", "28 may", "8.54", "6", "36.5", "1.21", "9.75"], ["semi - final 2 results", "28 may", "7.13", "14", "28.5", "n / a", "7.13"], ["semi - final 3", "30 may", "8.17", "8", "37.5", "1.27", "9.44"], ["semi - final 3 results", "30 may", "7.18", "13", "32.3", "n / a", "7.18"], ["semi - final 4", "31 may", "8.28", "7", "37.5", "1.12", "9.4"], ["semi - final 4 results", "31 may", "7.29", "12", "32.7", "n / a", "7.29"], ["semi - final 5", "1 june", "8.02", "9", "41.9", "1.20", "9.22"], ["semi - final 5 results", "1 june", "7.46", "11", "32.8", "1.07", "8.53"], ["live final", "8 june", "10.43", "1", "48.9", "1.80", "12.23"]]}, "question": "In the context of TV ratings, the \"audience share\" is defined as the percentage of viewers watching a particular channel out of the total number of viewers watching TV at a given time. Based on this definition, which episode of the show had the highest audience share?", "answer": "live final"}
{"id": "3a29c3a2e897c3551da6c1be2f183cc6", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["Unnamed: 0", "no", "title", "directed by", "written by", "viewers", "original airdate", "prod code"], "data": [["13", "1", "live and let doyle", "james allodi", "allan hawco", "1038000", "january 12 , 2011", "201"], ["14", "2", "popeye doyle", "steve scaini", "allan hawco", "944000", "january 19 , 2011", "202"], ["15", "3", "a stand up guy", "steve scaini", "perry chafe", "776000", "january 26 , 2011", "203"], ["16", "4", "the son also rises", "steve dimarco", "jesse mckeown", "899000", "february 2 , 2011", "204"], ["17", "5", "something old , someone blue", "james allodi", "adam higgs & jackie may", "854000", "february 9 , 2011", "205"], ["18", "6", "the ryans and the pittmans", "steve dimarco", "greg nelson", "10000000", "february 16 , 2011", "206"], ["19", "7", "crashing on the couch", "keith samples", "jackie may", "760000", "february 23 , 2011", "207"], ["20", "8", "sympathy for the devil", "stacey curtis", "john callaghan", "834400", "march 2 , 2011", "208"], ["21", "9", "will the real des courtney please stand up", "keith samples", "greg nelson", "1026000", "march 9 , 2011", "209"], ["22", "10", "the special detective", "steve scaini", "adam higgs", "836000", "march 16 , 2011", "210"], ["23", "11", "don't gamble with city hall", "john vatcher", "jackie may", "1000", "march 23 , 2011", "211"], ["24", "12", "st john 's town", "keith samples", "perry chafe", "730000", "march 30 , 2011", "212"]]}, "question": "What are the anomalies in the viewership data for the TV episodes?", "answer": "Two anomalies are Episode 6 with a possibly inaccurate high viewer count of 10,000,000 and Episode 11 with a suspect low count of 1,000"}
{"id": "d3ff0f656633ba71cbecf712c6d234cd", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["member state", "population millions", "meps", "inhabitants per mep", "influence"], "data": [["austria", "8.27", "17", "486235", "1.71"], ["belgium", "10.51", "22", "477773", "1.74"], ["bulgaria", "7.72", "17", "454059", "1.83"], ["cyprus", "0.77", "6", "127667", "6.52"], ["czech republic", "10.25", "22", "465955", "1.79"], ["denmark", "5.43", "13", "417538", "1.99"], ["estonia", "1.34", "6", "224000", "3.72"], ["finland", "5.26", "13", "404308", "2.06"], ["france", "62.89", "72", "873417", "0.95"], ["germany", "82.43", "99", "832606", "1.0"], ["greece", "11.13", "22", "505682", "1.65"], ["hungary", "10.08", "22", "458045", "1.82"], ["ireland", "4.21", "12", "350750", "2.37"], ["italy", "58.75", "72", "816000", "1.02"], ["latvia", "2.3", "8", "286875", "2.9"], ["lithuania", "3.4", "12", "283583", "2.94"], ["luxembourg", "0.46", "6", "76667", "10.86"], ["malta", "0.4", "5", "80800", "10.3"], ["netherlands", "16.33", "25", "653360", "1.27"], ["poland", "38.16", "50", "763140", "1.09"], ["portugal", "10.57", "22", "480455", "1.73"], ["romania", "21.61", "33", "654848", "1.27"], ["slovakia", "5.39", "13", "414538", "2.01"], ["slovenia", "2.0", "7", "286143", "2.91"], ["spain", "43.76", "50", "875160", "0.95"], ["sweden", "9.05", "18", "502667", "1.66"], ["united kingdom", "60.64", "72", "839194", "0.99"]]}, "question": "Which 3 member states have the highest inhabitants per MEP?", "answer": "spain, france, united kingdom"}
{"id": "2263f3aabca0e99e20653ff6bf45b738", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "population", "Catholics (based on registration by the church itself)", "Percentage (based on registration by the church itself)"], "data": [["1970", "12,957,621", "5,320,000", "40.5"], ["1980", "14,091,014", "5,620,000", "39.5"], ["1990", "14,892,574", "5,560,000", "37.0"], ["1995", "15,424,122", "5,385,258", "34.8"], ["2000", "15,863,950", "5,060,413", "31.6"], ["2005", "16,305,526", "4,406,000", "27.0"], ["2010", "16,574,989", "4,166,000", "25.0"], ["2015", "16,900,726", "3,882,000", "22.9"], ["2016", "16,979,120", "3,832,000", "22.4"], ["2017", "17,081,057", "3,769,000", "21.9"]]}, "question": "Based on the historical trend of Catholic population percentage, what can we predict about the percentage of Catholics in the population by 2025?", "answer": "19.08"}
{"id": "44c97ca5774b136c1cc34aa547a5c2d3", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing Norway", "Representing Norway", "Representing Norway", "Representing Norway", "Representing Norway", "Representing Norway"], ["1980", "Stockholm Marathon", "Stockholm, Sweden", "1st", "Marathon", "2:38:45"], ["1981", "Stockholm Marathon", "Stockholm, Sweden", "1st", "Marathon", "2:41:34"], ["1981", "New York City Marathon", "New York, United States", "2nd", "Marathon", "2:30:08"], ["1982", "Stockholm Marathon", "Stockholm, Sweden", "1st", "Marathon", "2:34:26"], ["1982", "European Championships", "Athens, Greece", "3rd", "Marathon", "2:36:38"], ["1982", "New York City Marathon", "New York, United States", "5th", "Marathon", "2:33:36"], ["1983", "Houston Marathon", "Houston, United States", "1st", "Marathon", "2:33:27"], ["1984", "Houston Marathon", "Houston, United States", "1st", "Marathon", "2:27:51"], ["1984", "World Cross Country Championships", "New York, United States", "4th", "-", "-"], ["1984", "London Marathon", "London, United Kingdom", "1st", "Marathon", "2:24:26"], ["1984", "Olympic Games", "Los Angeles, United States", "4th", "Marathon", "2:27:14"], ["1985", "World Cross Country Championships", "Lisbon, Portugal", "3rd", "-", "-"], ["1985", "London Marathon", "London, United Kingdom", "1st", "Marathon", "2:21:06"], ["1985", "Chicago Marathon", "Chicago, United States", "2nd", "Marathon", "2:23:05"], ["1986", "Boston Marathon", "Boston, United States", "1st", "Marathon", "2:24:55"], ["1986", "European Championships", "Stuttgart, West Germany", "1st", "10,000 m", "30:23.25"], ["1986", "Chicago Marathon", "Chicago, United States", "1st", "Marathon", "2:27:08"], ["1987", "World Cross Country Championships", "Warsaw, Poland", "3rd", "-", "-"], ["1987", "London Marathon", "London, United Kingdom", "1st", "Marathon", "2:22:48"], ["1987", "World Championships", "Rome, Italy", "1st", "10,000 m", "31:05.85"], ["1987", "World Road Race Championships", "Monte Carlo, Monaco", "1st", "15 km", "47:17"], ["1988", "World Road Race Championships", "Adelaide, Australia", "1st", "15 km", "48:24"], ["1988", "World Cross Country Championships", "Auckland, New Zealand", "1st", "-", "-"], ["1988", "London Marathon", "London, United Kingdom", "1st", "Marathon", "2:25:41"], ["1988", "Olympic Games", "Seoul, South Korea", "—", "10,000 m", "DNF"], ["1989", "Boston Marathon", "Boston, United States", "1st", "Marathon", "2:24:33"], ["1989", "New York City Marathon", "New York, United States", "1st", "Marathon", "2:25:30"], ["1991", "World Championships", "Tokyo, Japan", "7th", "10,000 m", "32:10.75"]]}, "question": "What is the year in which the athlete won the Stockholm Marathon with a time of less than 2:35:00?", "answer": "1982"}
{"id": "766afe58ffd3cac9bbdec711f8b9b2ef", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["Temperature T (°C)", "Speed of sound c (m/s)", "Density of air ρ (kg/m3)", "Characteristic specific acoustic impedance z0 (Pa·s/m)"], "data": [["35", "351.88", "1.1455", "403.2"], ["30", "349.02", "1.1644", "406.5"], ["25", "346.13", "1.1839", "409.4"], ["20", "343.21", "1.2041", "413.3"], ["15", "340.27", "1.2250", "416.9"], ["10", "337.31", "1.2466", "420.5"], ["5", "334.32", "1.2690", "424.3"], ["0", "331.30", "1.2922", "428.0"], ["−5", "328.25", "1.3163", "432.1"], ["−10", "325.18", "1.3413", "436.1"], ["−15", "322.07", "1.3673", "440.3"], ["−20", "318.94", "1.3943", "444.6"], ["−25", "315.77", "1.4224", "449.1"]]}, "question": "Which has a greater causal influence on the speed of sound c (m/s), the density of air ρ (kg/m³) or the temperature T (°C)?", "answer": "Temperature T (°C) has a positive influence on the speed of sound c (m/s) (correlation coefficient of 1), while air density ρ (kg/m³) has an equally strong negative influence (correlation coefficient of -1)."}
{"id": "d5f9a7bbcbc5f26735c8f332d75a2c36", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["region", "land area (km 2 )", "rainfall by depth (mm / year)", "rainfall by volume (km 3 / year)", "surface run off (km 3 / year)", "infiltration (km 3 / year)", "evapotranspiration (km 3 / year)"], "data": [["chorotega", "9552.4", "2006", "19.2", "5.7", "3.5", "10.3"], ["huetar norte", "9001.5", "3527", "31.8", "14.9", "9.6", "7.5"], ["huetar atlántico", "9688.5", "3933", "38.1", "17.6", "9.3", "11.1"], ["pacífico central", "4722.9", "2801", "13.2", "5.2", "2.2", "4.9"], ["central", "8543.2", "3461", "29.6", "13.0", "7.0", "8.6"], ["brunca", "9294.5", "3809", "35.4", "18.6", "5.6", "12.2"]]}, "question": "Which region has the highest rainfall by volume (km³/year)?", "answer": "huetar atlántico"}
{"id": "2a55d8cdce801c0bc37d186b2036d200", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "us rank", "total s ton", "domestic s ton", "foreign total s ton", "foreign imports s ton", "foreign exports s ton"], "data": [["2006", "102", "2926536", "2306192", "620344", "464774", "155570"], ["2005", "94", "3527469", "2629553", "897916", "430396", "467520"], ["2004", "101", "3085753", "2323089", "762664", "284347", "478317"], ["2003", "96", "3178633", "2494261", "684372", "218233", "466139"], ["2002", "102", "2983137", "2318653", "664484", "251203", "413281"], ["2001", "108", "2861134", "2157496", "703638", "225281", "478357"], ["2000", "103", "3157247", "2416514", "740733", "382240", "358493"]]}, "question": "Based on the trend in total steel production from 2000 to 2006, forecast the likely total steel production in the United States for 2007.", "answer": "3209008"}
{"id": "c4a6de9e58baabab25d41f6e0767c85a", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["rank", "council area", "speakers", "population", "percentage (%)"], "data": [["1", "na h - eileanan siar", "15811", "26502", "59.7"], ["2", "highland", "12673", "208914", "6.1"], ["3", "city of glasgow", "5739", "577869", "1.0"], ["4", "argyll and bute", "4145", "91306", "4.5"], ["5", "city of edinburgh", "3120", "448624", "0.7"], ["6", "perth and kinross", "1434", "134949", "1.1"], ["7", "city of aberdeen", "1412", "212125", "0.7"], ["8", "fife", "1106", "349429", "0.3"], ["9", "south lanarkshire", "1079", "302216", "0.4"], ["10", "north lanarkshire", "1021", "321067", "0.3"], ["11", "renfrewshire", "988", "172867", "0.6"], ["12", "stirling", "939", "86212", "1.1"], ["13", "east dunbartonshire", "895", "108243", "0.8"], ["14", "aberdeenshire", "871", "226871", "0.4"], ["15", "city of dundee", "645", "145663", "0.4"], ["16", "east renfrewshire", "590", "89311", "0.7"], ["17", "west lothian", "571", "158714", "0.4"], ["18", "north ayrshire", "557", "135817", "0.4"], ["19", "falkirk", "529", "145191", "0.4"], ["20", "angus", "485", "108400", "0.4"], ["21", "moray", "459", "86940", "0.5"], ["22", "dumfries and galloway", "448", "147765", "0.3"], ["23", "west dunbartonshire", "437", "93378", "0.5"], ["24", "south ayrshire", "417", "112097", "0.4"], ["25", "inverclyde", "409", "84203", "0.5"], ["26", "scottish borders", "376", "106764", "0.4"], ["27", "east ayrshire", "368", "120235", "0.3"], ["28", "east lothian", "341", "90088", "0.4"], ["29", "clackmannanshire", "301", "48077", "0.6"], ["30", "midlothian", "244", "80941", "0.3"], ["31", "shetland", "97", "21988", "0.4"], ["32", "orkney", "92", "19245", "0.5"]]}, "question": "Can you describe the content of the table, and offer some basic insights about the distribution of speakers across different council areas?", "answer": "The table provides an overview of language speaker distribution across 32 council areas, ranked by speaker count. It details the total population of each area and the corresponding percentage of speakers. The data highlights significant variations in language speaker distribution, with \"na h - eileanan siar\" having the highest concentration of speakers."}
{"id": "e067a40ab6736ac5a004d9dc69f2d5c0", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["Row Header", "Position", "Age", "Air Group or Subsidiary Officer Since"], "data": [["Bradley D. Tilden", "Chairman and Chief Executive Officer of Alaska Air Group, Inc., Chairman of Alaska Airlines, Inc., Chairman of Horizon Air Industries, Inc.", "58", "1994"], ["Brandon S. Pedersen", "Executive Vice President/Finance and Chief Financial Officer of Alaska Air Group, Inc. and Alaska Airlines, Inc., and Treasurer of Alaska Air Group, Inc. and Alaska Airlines, Inc.", "52", "2003"], ["Kyle B. Levine", "Vice President Legal, General Counsel and Corporate Secretary of Alaska Air Group, Inc. and Alaska Airlines, Inc. and Chief Ethics and Compliance Officer of Alaska Air Group, Inc.", "47", "2016"], ["Benito Minicucci", "President and Chief Operating Officer of Alaska Airlines, Inc.", "52", "2004"], ["Gary L. Beck", "President and Chief Executive Officer of Horizon Air Industries, Inc.", "71", "2018"], ["Andrew R. Harrison", "Executive Vice President and Chief Commercial Officer of Alaska Airlines, Inc.", "49", "2008"], ["Shane R. Tackett", "Executive Vice President, Planning and Strategy of Alaska Airlines, Inc.", "40", "2011"], ["Andrea L. Schneider", "Vice President People of Alaska Airlines, Inc.", "53", "1998"], ["Diana Birkett-Rakow", "Vice President External Relations of Alaska Airlines, Inc.", "41", "2017"]]}, "question": "What is the average age of the executives listed in the table?", "answer": "51.44"}
{"id": "74a141ffb44ab78e143b3824b352eee2", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "class", "team", "points", "rank", "wins"], "data": [["1994", "125cc", "honda", "24", "20th", "0"], ["1995", "125cc", "honda", "102", "8th", "0"], ["1996", "125cc", "honda", "167", "3rd", "1"], ["1997", "125cc", "honda", "190", "3rd", "0"], ["1998", "125cc", "honda", "217", "2nd", "5"], ["1999", "250cc", "yamaha", "52", "15th", "0"]]}, "question": "What is the average annual increase in points from 1994 to 1998 for the 'honda' team in the '125cc' class?", "answer": "48.25"}
{"id": "b9d8ed898f79c1447bf6ca63051b60e9", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "total region", "biggenden", "eidsvold", "gayndah", "monto", "mundubbera", "perry"], "data": [["1933", "14322", "2476", "1475", "3760", "3514", "2302", "795"], ["1947", "13861", "2179", "1313", "3407", "4270", "2064", "628"], ["1954", "13917", "1974", "1311", "3352", "4458", "2326", "496"], ["1961", "13993", "1882", "1242", "3400", "4397", "2617", "455"], ["1966", "13715", "1722", "1702", "3182", "4155", "2580", "374"], ["1971", "12230", "1639", "1222", "3107", "3495", "2391", "376"], ["1976", "11504", "1532", "1231", "2814", "3228", "2395", "304"], ["1981", "11565", "1411", "1256", "2859", "3249", "2481", "309"], ["1986", "11583", "1553", "1212", "2887", "3266", "2355", "310"], ["1991", "11230", "1574", "1028", "2856", "3058", "2340", "374"], ["1996", "11243", "1570", "970", "2916", "2922", "2514", "351"], ["2001", "10782", "1486", "933", "2894", "2592", "2451", "426"]]}, "question": "Based on the historical population data from 1933 to 2001, what is the forecasted population for the 'biggenden' region in the year 2010?", "answer": "1213"}
{"id": "f3e0cdd74f999348fb59a6fabee6691e", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["year ended", "revenue (million)", "profit / (loss) before tax (m)", "net profit (m)", "earnings per share (p)"], "data": [["2011", "5110", "193.7", "123.8", "38.2"], ["2010", "4830", "225.2", "159.0", "49.1"], ["2009", "4649", "257.8", "148.9", "46.4"], ["2008", "4177", "206.9", "142.2", "44.5"], ["2007", "3582", "191.1", "130.1", "39.8"], ["2006", "3333", "189.7", "129.4", "37.8"], ["2005", "2924", "176.7", "124.2", "35.4"], ["2004", "2438", "158.2", "141.4", "30.7"], ["2004", "2438", "200.9", "127.4", "28.7"], ["2003", "2276", "194.6", "124.6", "27.4"]]}, "question": "Which is the main factor in the table, such as 'year ended' or 'revenue (million)', significantly influence the 'earnings per share (p)' values?", "answer": "revenue"}
{"id": "afa6ff00100fcbf8556766a96d5e12f7", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "theme", "artist", "mintage", "issue price"], "data": [["2002", "15th anniversary loonie", "dora de pãdery - hunt", "67672", "39.95"], ["2004", "jack miner bird sanctuary", "susan taylor", "46493", "39.95"], ["2005", "tufted puffin", "n / a", "39818", "39.95"], ["2006", "snowy owl", "glen loates", "39935", "44.95"], ["2007", "trumpeter swan", "kerri burnett", "40000", "45.95"], ["2008", "common eider", "mark hobson", "40000", "47.95"], ["2009", "great blue heron", "chris jordison", "40000", "47.95"], ["2010", "northern harrier", "arnold nogy", "35000", "49.95"], ["2011", "great gray owl", "arnold nogy", "35000", "49.95"], ["2012", "25th anniversary loonie", "arnold nogy", "35000", "49.95"]]}, "question": "What is the total mintage of coins issued in the first 4 years (2002-2006) of the provided data?", "answer": "193918"}
{"id": "4f1c9e36f683c1b7a8fa7a335db8f3c3", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["economy", "1980", "gap from thailand as of 1980 (times)", "1985", "1990", "1995", "2000", "2005", "2010", "2012", "gap from thailand as of 2012 (times)", "gdp as of 2012 after purchasing power parity (ppp) calculations (usd billions)", "gdp per capita as of 2012 (ppp)"], "data": [["china", "205", "0.29", "290", "341", "601", "945", "1726", "4422", "6076", "1.07", "12405.67", "9162"], ["hong kong", "5679", "8.16", "6442", "13330", "22939", "25128", "25748", "32429", "36667", "6.46", "369.38", "51494"], ["japan", "9309", "13.38", "11461", "25144", "42523", "37303", "35787", "42916", "46735", "8.23", "4627.89", "36265"], ["korea", "1689", "2.43", "2414", "6308", "11779", "11347", "17551", "20540", "23113", "4.07", "1613.92", "32272"], ["malaysia", "1812", "2.6", "2026", "2432", "4358", "4030", "5211", "8633", "10304", "1.81", "498.48", "100000"], ["singapore", "4756", "6.83", "6754", "12387", "23718", "22791", "28498", "44697", "51162", "9.01", "326.51", "60410"], ["taiwan", "2363", "3.4", "3271", "8086", "12865", "14641", "16023", "18488", "20328", "3.58", "903.47", "38749"], ["korea", "1689", "2.43", "2414", "6308", "11779", "11347", "17551", "20540", "23113", "4.07", "10.92", "32272"]]}, "question": "Which economies in the table have values that deviate significantly from the norm?", "answer": "The two anomalies are the implausibly high GDP per capita of Malaysia at 100,000 (potentially a data entry error or unusual economic spike) and the unusually low GDP of South Korea at 10.92 billion USD post-PPP adjustments"}
{"id": "b4eb57e9a160eb8d608dd549f1d97112", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["League", "League.1", "Position", "Teams", "Matches", "Win", "Lose"], "data": [["V.League", "7th (2000-01)", "4th", "10", "18", "9", "9"], ["V.League", "8th (2001-02)", "4th", "9", "16", "14", "2"], ["V.League", "9th (2002-03)", "3rd", "8", "21", "12", "9"], ["V.League", "10th (2003-04)", "Runner-up", "10", "18", "13", "5"], ["V.League", "11th (2004-05)", "8th", "10", "27", "11", "16"], ["V.League", "12th (2005-06)", "4th", "10", "27", "20", "7"], ["V・Premier", "2006-07", "6th", "10", "27", "11", "16"], ["V・Premier", "2007-08", "Champion", "10", "27", "23", "4"], ["V・Premier", "2008-09", "Champion", "10", "27", "20", "7"], ["V・Premier", "2009-10", "Champion", "8", "28", "21", "7"], ["V・Premier", "2010-11", "Runner-up", "8", "26", "19", "7"], ["V・Premier", "2011-12", "Champion", "8", "21", "18", "3"], ["V・Premier", "2012-13", "Runner-up", "8", "28", "20", "8"]]}, "chart_type": "bar", "question": "Please draw a stacked bar chart showing the number of wins and losses for this team in various matches", "answer": "y_references = [[9, 14, 12, 13, 11, 20, 11, 23, 20, 21, 19, 18, 20],[9, 2, 9, 5, 16, 7, 16, 4, 7, 7, 7, 3, 8]]"}
{"id": "186b646cdd698ceabbb2738e0e5e9e6b", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Painter", "Composition", "Drawing", "Color", "Expression"], "data": [["Andrea del Sarto", "12", "16", "9", "8"], ["Federico Barocci", "14", "15", "6", "10"], ["Jacopo Bassano", "6", "8", "17", "0"], ["Giovanni Bellini", "4", "6", "14", "O"], ["Sebastian Bourdon", "10", "8", "8", "4"], ["Charles Le Brun", "16", "16", "8", "16"], ["I Carracci", "15", "17", "13", "13"], ["Cavalier D'Arpino", "10", "10", "6", "2"], ["Correggio", "13", "13", "15", "12"], ["Daniele da Volterra", "12", "15", "5", "8"], ["Abraham van Diepenbeeck", "11", "10", "14", "6"], ["Il Domenichino", "15", "17", "9", "17"], ["Albrecht Dürer", "8", "10", "10", "8"], ["Giorgione", "8", "9", "18", "4"], ["Giovanni da Udine", "10", "8", "16", "3"], ["Giulio Romano", "15", "16", "4", "14"], ["Guercino", "18", "10", "10", "4"], ["Guido Reni", "x", "13", "9", "12"], ["Holbein", "9", "10", "16", "3"], ["Jacob Jordaens", "10", "8", "16", "6"], ["Lucas Jordaens", "13", "12", "9", "6"], ["Giovanni Lanfranco", "14", "13", "10", "5"], ["Leonardo da Vinci", "15", "16", "4", "14"], ["Lucas van Leyden", "8", "6", "6", "4"], ["Michelangelo", "8", "17", "4", "8"], ["Caravaggio", "6", "6", "16", "O"], ["Murillo", "6", "8", "15", "4"], ["Otho Venius", "13", "14", "10", "10"], ["Palma il Vecchio", "5", "6", "16", "0"], ["Palma il Giovane", "12", "9", "14", "6"], ["Il Parmigianino", "10", "15", "6", "6"], ["Gianfrancesco Penni", "O", "15", "8", "0"], ["Perin del Vaga", "15", "16", "7", "6"], ["Sebastiano del Piombo", "8", "13", "16", "7"], ["Primaticcio", "15", "14", "7", "10"], ["Raphael", "17", "18", "12", "18"], ["Rembrandt", "15", "6", "17", "12"], ["Rubens", "18", "13", "17", "17"], ["Francesco Salviati", "13", "15", "8", "8"], ["Eustache Le Sueur", "15", "15", "4", "15"], ["Teniers", "15", "12", "13", "6"], ["Pietro Testa", "11", "15", "0", "6"], ["Tintoretto", "15", "14", "16", "4"], ["Titian", "12", "15", "18", "6"], ["Van Dyck", "15", "10", "17", "13"], ["Vanius", "15", "15", "12", "13"], ["Veronese", "15", "10", "16", "3"], ["Taddeo Zuccari", "13", "14", "10", "9"], ["Federico Zuccari", "10", "10", "8", "8"]]}, "chart_type": "radar", "question": "Please draw a radar chart displaying the performance of the painter Guercino in various aspects.", "answer": "y_references = [18, 10, 10, 4]"}
{"id": "f336acf7ae0825191c3faa000c143abc", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["rank", "country", "year", "males", "females", "total"], "data": [["1", "south korea", "2011", "39.3", "19.7", "28.4"], ["2", "hungary", "2009", "33.8", "8.0", "19.8"], ["3", "japan", "2009", "29.2", "10.5", "19.7"], ["4", "finland", "2009", "26.0", "8.9", "17.3"], ["5", "slovenia", "2009", "28.2", "6.7", "17.2"], ["6", "estonia", "2009", "31.2", "4.8", "16.8"], ["7", "belgium", "2005", "24.6", "8.4", "16.2"], ["8", "switzerland", "2007", "20.6", "8.7", "14.3"], ["9", "france", "2008", "21.6", "6.8", "13.8"], ["10", "poland", "2008", "23.3", "3.5", "12.9"], ["11", "austria", "2009", "19.7", "5.2", "12.0"], ["12", "czech republic", "2009", "20.1", "3.4", "11.4"], ["13", "ireland", "2009", "18.0", "4.6", "11.3"], ["14", "new zealand", "2007", "17.8", "5.0", "11.2"], ["15", "sweden", "2008", "16.1", "6.0", "11.0"], ["16", "chile", "2007", "18.5", "4.1", "11.0"], ["17", "norway", "2009", "15.7", "6.2", "10.9"], ["18", "united states", "2007", "17.1", "4.3", "10.5"], ["19", "iceland", "2009", "16.6", "3.9", "10.3"], ["20", "canada", "2004", "15.7", "4.9", "10.2"], ["21", "denmark", "2006", "15.3", "5.3", "9.9"], ["22", "slovak republic", "2011", "17.4", "2.8", "9.9"], ["23", "germany", "2006", "14.5", "4.3", "9.1"], ["24", "netherlands", "2009", "11.2", "4.6", "7.8"], ["25", "luxembourg", "2008", "13.3", "2.7", "7.8"], ["26", "australia", "2006", "11.9", "3.3", "7.5"], ["27", "portugal", "2009", "12.5", "2.9", "7.3"], ["28", "united kingdom", "2009", "9.8", "2.6", "6.2"], ["29", "spain", "2008", "9.7", "2.6", "6.0"], ["30", "israel", "2008", "8.8", "1.6", "5.0"], ["31", "italy", "2007", "8.0", "2.1", "4.9"], ["32", "mexico", "2008", "7.5", "1.5", "4.4"], ["33", "turkey", "2008", "5.36", "2.5", "3.94"], ["34", "greece", "2009", "4.8", "0.8", "2.8"]]}, "question": "What is the average total value per year from 2005 to 2009?", "answer": "69.29"}
{"id": "993a7fd34ef053762ab118cd5ae0a3c0", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["year", "indians admitted", "pakistanis admitted", "sri lankans admitted", "bangladeshis admitted", "nepalis admitted"], "data": [["2000", "26122", "14201", "5849", "2715", "247"], ["2001", "27901", "15353", "5520", "3393", "273"], ["2002", "28838", "14173", "4968", "2615", "418"], ["2003", "24595", "12351", "4448", "1896", "440"], ["2004", "25573", "12793", "4134", "3374", "594"], ["2005", "22141", "13575", "4690", "3940", "714"], ["2006", "30746", "12329", "4490", "3838", "640"], ["2007", "26047", "9545", "3934", "2735", "564"], ["2008", "24548", "8051", "4508", "2716", "639"], ["2009", "26117", "6213", "4270", "4270", "627"], ["2010", "30252", "4986", "4181", "4364", "1502"], ["2011", "24965", "6073", "3104", "2449", "1249"], ["2012", "28943", "9931", "3152", "2449", "1311"], ["total", "346788", "139574", "57248", "35856", "9218"]]}, "question": "How many Indians were admitted in 2005?", "answer": "22141"}
{"id": "4c147e78894f919ff89514f23c84bc12", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["round", "pick", "overall", "name", "position", "college"], "data": [["1", "13", "13", "brian orakpo", "de", "texas"], ["3", "16", "80", "kevin barnes", "cb", "maryland"], ["5", "22", "158", "cody glenn", "lb", "nebraska"], ["6", "13", "186", "robert henson", "lb", "texas christian"], ["7", "12", "221", "eddie williams", "te", "idaho"], ["7", "34", "243", "marko mitchell", "wr", "nevada"]]}, "question": "Draft Position Improvement is defined as the difference between the overall pick number and the pick number in a specific round. What is the Draft Position Improvement for players drafted in the 7th round?", "answer": "209"}
{"id": "78ccc4fb07ce60975392c8b42aa454ea", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["Player", "Rec.", "Yards", "Avg.", "TD's", "Long"], "data": [["Charles Frederick", "115", "1385", "12", "27", "42"], ["Jerel Myers", "104", "1183", "11.4", "21", "38"], ["Anthony Hines", "58", "822", "14.2", "12", "39"], ["Boo Williams", "45", "474", "10.5", "17", "35"], ["Ira Gooch", "24", "339", "14.1", "6", "32"], ["Sam Simmons", "15", "197", "13.1", "2", "30"], ["Kevin Beard", "10", "87", "8.7", "0", "21"], ["Dawan Moss", "7", "39", "5.6", "1", "12"], ["Cyron Brown", "3", "17", "5.7", "1", "8"], ["Larrell Johnson", "3", "14", "4.7", "0", "6"], ["Jamarr Wood", "1", "13", "13", "0", "13"], ["Cecil Moore", "2", "9", "4.5", "2", "8"], ["Raymond Philyaw", "1", "−6", "−6", "0", "−6"]]}, "question": "Which player had an average of 11.4 yards per reception?", "answer": "Jerel Myers"}
{"id": "b31b52e170963e2b4d7fae9a6f59cc63", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["series", "season", "title", "written by", "original air date", "us viewers (millions)"], "data": [["36", "1", "the big bang", "kari lizer & jeff astrof", "february 4 , 2008", "9.43"], ["37", "2", "beauty is only spanx deep", "kari lizer", "february 11 , 2008", "9.89"], ["38", "3", "popular", "jennifer crittenden", "february 18 , 2008", "9.4"], ["39", "4", "traffic", "jeff astrof", "february 25 , 2008", "9.14"], ["40", "5", "between a rock and a hard place", "aaron shure", "march 3 , 2008", "8.35"], ["41", "6", "the new adventures of old christine", "frank pines", "march 10 , 2008", "7.38"], ["42", "7", "house", "katie palmer", "march 10 , 2008", "9.62"], ["43", "8", "burning down the house (part 1)", "aaron shure", "march 17 , 2008", "11.47"]]}, "question": "Can you calculate the average and standard deviation of 'us viewers (millions)' across all episodes?", "answer": "9.34, 1.18"}
{"id": "fc3045254e1c441b431664ecc434613d", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["Year", "-", "Year", "-", "Year", "-"], "data": [["1820", "8,385", "1885", "395,346", "1950", "249,187"], ["1825", "10,199", "1890", "455,302", "1955", "237,790"], ["1830", "23,322", "1895", "258,536", "1960", "265,398"], ["1835", "45,374", "1900", "448,572", "1965", "296,697"], ["1840", "84,066", "1905", "1,026,499", "1970", "373,326"], ["1845", "114,371", "1910", "1,041,570", "1975", "385,378"], ["1850", "369,980", "1915", "326,700", "1980", "524,295"], ["1855", "200,877", "1920", "430,001", "1985", "568,149"], ["1860", "153,640", "1925", "294,314", "1990", "1,535,872"], ["1865", "248,120", "1930", "241,700", "1995", "720,177"], ["1870", "387,203", "1935", "34,956", "2000", "841,002"], ["1875", "227,498", "1940", "70,756", "2005", "1,122,257"], ["1880", "457,257", "1945", "38,119", "2010", "1,042,625"]]}, "question": "Based on the growth pattern of the values from 1820 to 2010, forecast the likely value in the year 2020.", "answer": "1298789"}
{"id": "7ee09fe1d48c37e52e56c6ac5615fb80", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["rank", "building", "address", "height", "storeys", "completed"], "data": [["1", "la renaissance apartments", "424 spadina crescent e", "-", "24", "1983"], ["2", "hallmark place", "311 6th ave n", "-", "27", "1984"], ["3", "saskatoon square", "410 22nd st e", "-", "17", "1979"], ["4", "the terrace apartments", "315 5th ave n", "-", "22", "1980"], ["5", "radisson hotel", "405 20th st e", "-", "20", "1983"], ["6", "the view on fifth (formerly milroy apartments)", "320 5th ave n", "-", "22", "1968"], ["7", "the luther", "1223 temperance st", "-", "22", "1978"], ["8", "marquis towers", "241 5th ave n", "-", "21", "1966"], ["9", "carlton towers", "325 5th ave n", "-", "21", "1968"], ["10", "delta bessborough", "601 spadina crescent e", "-", "10", "1932"], ["11", "the tower at midtown (formerly cn tower)", "201 1st avenue south", "-", "12", "1970"], ["12", "saskatoon towers", "125 5th avenue north", "-", "19", "1972"], ["13", "avord towers", "606 spadina crescent east", "-", "14", "1964"]]}, "question": "What is the average number of storeys of the top 3 buildings by rank that were completed before 1980?", "answer": "20.3"}
{"id": "04b30155112a315590a58ffe5fcd4a0b", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["Rank", "Nation", "Gold", "Silver", "Bronze", "Total"], "data": [["1", "Venezuela", "9", "8", "6", "23"], ["2", "Guatemala", "6", "6", "6", "18"], ["3", "Peru", "5", "8", "9", "22"], ["4", "Chile", "4", "4", "1", "9"], ["5", "El Salvador", "4", "0", "2", "6"], ["6", "Ecuador", "2", "5", "1", "8"], ["7", "Bolivia", "2", "1", "2", "5"], ["8", "Dominican Republic", "1", "0", "2", "3"], ["9", "Colombia", "0", "1", "3", "4"], ["Total", "Total", "33", "33", "32", "98"]]}, "question": "Which nation has 4 gold medals and is ranked 4th in the table?", "answer": "Chile"}
{"id": "e1c02ab4252451db510a47d2d9f7f227", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["-", "Soviet Union", "Poland and Danzig", "Finland", "Estonia", "Latvia", "Lithuania"], "data": [["1934", "223.0", "78.1", "42.3", "8.2", "21.1", "15.1"], ["1935", "201.7", "75.5", "41.4", "13.0", "31.1", "2.0"], ["1936", "93.2", "74.0", "46.1", "13.8", "33.2", "9.1"], ["1937", "63.1", "80.7", "70.1", "23.7", "45.7", "17.2"], ["1938", "47.4", "109.4", "88.6", "24.0", "43.5", "27.6"], ["1939", "52.8", "140.8", "88.9", "24.3", "43.6", "27.8"], ["*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks", "*German Imports in millions of Reichsmarks"]]}, "question": "What is the total increase in German imports from Poland and Danzig between 1934 and 1939?", "answer": "62.7"}
{"id": "21f6f7538ed226cc54b563131618e08f", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Association", "Category", "Nominated work", "Result", "Ref."], "data": [["2008", "ASCAP Pop Music Awards", "ASCAP Vanguard Award", "Herself", "Won", "-"], ["2009", "Grammy Awards", "Song of the Year", "\"Love Song\"", "Nominated", "-"], ["2009", "Grammy Awards", "Best Female Pop Vocal Performance", "\"Love Song\"", "Nominated", "-"], ["2011", "Grammy Awards", "Best Female Pop Vocal Performance", "\"King of Anything\"", "Nominated", "-"], ["2011", "BDSCertified Spin Awards", "700,000 Spins", "\"Love Song\"", "Won", "-"], ["2012", "MVPA Awards", "Best Directional Debut", "\"Gonna Get Over You\"", "Nominated", "-"], ["2012", "MVPA Awards", "Best Choreography", "\"Gonna Get Over You\"", "Won", "-"], ["2014", "World Music Awards", "World's Best Song", "\"Brave\"", "Nominated", "-"], ["2014", "MTV Video Music Awards Japan", "Best Choreography", "\"Brave\"", "Nominated", "-"], ["2014", "Grammy Awards", "Best Pop Solo Performance", "\"Brave\"", "Nominated", "-"], ["2014", "Grammy Awards", "Album of the Year", "The Blessed Unrest", "Nominated", "-"], ["2014", "American Music Award", "Favorite Adult Contemporary Artist", "Herself", "Nominated", "-"], ["2016", "Tony Award", "Best Original Score", "Waitress", "Nominated", "-"], ["2016", "Drama Desk Award", "Outstanding Music", "Waitress", "Nominated", "-"], ["2016", "Drama Desk Award", "Outstanding Lyrics", "Waitress", "Nominated", "-"], ["2016", "Outer Critics Circle Award", "Outstanding New Score (Broadway or Off-Broadway)", "Waitress", "Nominated", "-"], ["2017", "Grammy Awards", "Best Musical Theater Album", "Waitress", "Nominated", "-"], ["2017", "Broadway.com Audience Awards", "Favorite Female Replacement", "Waitress", "Won", "-"], ["2017", "Hollywood Music in Media Awards", "Original Song - Featured Film", "\"If I Dare\"", "Nominated", "-"], ["2017", "Women's Entrepreneurship Day Pioneer Awards", "Music", "Herself", "Won", "-"], ["2018", "Tony Award", "Best Original Score", "SpongeBob SquarePants", "Nominated", "-"], ["2018", "Emmy Award", "Outstanding Supporting Actress in a Limited Series or Movie", "Jesus Christ Superstar Live in Concert", "Nominated", "-"], ["2019", "Grammy Award", "Best Musical Theater Album", "Jesus Christ Superstar Live in Concert", "Nominated", "-"]]}, "question": "In which year did the work win an award for a song that was nominated for Song of the Year at the Grammy Awards?", "answer": "2009"}
{"id": "79c7100e623e490d4aabd5361cd50c5b", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["brno", "name", "builder", "whenbuilt", "withdrawn"], "data": [["34071", "601 squadron", "brighton", "1948", "1967"], ["34072", "257 squadron", "brighton", "1948", "1964"], ["34073", "249 squadron", "brighton", "1948", "1964"], ["34074", "46 squadron", "brighton", "1948", "1963"], ["34075", "264 squadron", "brighton", "1948", "1964"], ["34076", "41 squadron", "brighton", "1948", "1966"], ["34077", "603 squadron", "brighton", "1948", "1967"], ["34078", "222 squadron", "brighton", "1948", "1964"], ["34079", "141 squadron", "brighton", "1948", "1966"], ["34080", "74 squadron", "brighton", "1948", "1964"], ["34081", "92 squadron", "brighton", "1948", "1964"], ["34082", "615 squadron", "brighton", "1948", "1966"], ["34083", "605 squadron", "brighton", "1948", "1964"], ["34084", "253 squadron", "brighton", "1948", "1965"], ["34085", "501 squadron", "eastleigh", "1948", "1965"], ["34086", "219 squadron", "brighton", "1948", "1966"], ["34087", "145 squadron", "eastleigh", "1948", "1967"], ["34088", "213 squadron", "brighton", "1948", "1967"], ["34089", "602 squadron", "eastleigh", "1948", "1967"], ["34090", "sir eustace missenden , southern railway", "brighton", "1949", "1967"], ["34091", "weymouth", "brighton", "1949", "1964"], ["34092", "city of wells", "brighton", "1949", "1964"], ["34093", "saunton", "brighton", "1949", "1967"], ["34094", "mortehoe", "brighton", "1949", "1964"], ["34095", "brentor", "eastleigh", "1949", "1967"], ["34096", "trevone", "brighton", "1949", "1964"], ["34097", "holsworthy", "brighton", "1949", "1967"], ["34098", "templecombe", "brighton", "1949", "1967"], ["34099", "lynmouth", "brighton", "1949", "1964"], ["34100", "appledore", "brighton", "1949", "1967"], ["34101", "hartland", "eastleigh", "1950", "1966"], ["34102", "lapford", "eastleigh", "1950", "1967"], ["34103", "calstock", "brighton", "1950", "1965"], ["34104", "bere alston", "eastleigh", "1950", "1967"], ["34105", "swanage", "brighton", "1950", "1964"], ["34106", "lydford", "brighton", "march 1950", "september 1964"], ["34107", "blandford forum", "brighton", "april 1950", "september 1964"], ["34108", "wincanton", "brighton", "april 1950", "june 1967"], ["34109", "sir trafford leigh - mallory", "brighton", "may 1950", "september 1964"], ["34110", "66 squadron", "brighton", "january 1951", "november 1963"]]}, "question": "How many squadrons were built by 'brighton' in 1948?", "answer": "16"}
{"id": "0c7c0eec637d1301f824d1e5069328d8", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["Painter", "Composition", "Drawing", "Color", "Expression"], "data": [["Andrea del Sarto", "12", "16", "9", "8"], ["Federico Barocci", "14", "15", "6", "10"], ["Jacopo Bassano", "6", "8", "17", "0"], ["Giovanni Bellini", "4", "6", "14", "O"], ["Sebastian Bourdon", "10", "8", "8", "4"], ["Charles Le Brun", "16", "16", "8", "16"], ["I Carracci", "15", "17", "13", "13"], ["Cavalier D'Arpino", "10", "10", "6", "2"], ["Correggio", "13", "13", "15", "12"], ["Daniele da Volterra", "12", "15", "5", "8"], ["Abraham van Diepenbeeck", "11", "10", "14", "6"], ["Il Domenichino", "15", "17", "9", "17"], ["Albrecht Dürer", "8", "10", "10", "8"], ["Giorgione", "8", "9", "18", "4"], ["Giovanni da Udine", "10", "8", "16", "3"], ["Giulio Romano", "15", "16", "4", "14"], ["Guercino", "18", "10", "10", "4"], ["Guido Reni", "x", "13", "9", "12"], ["Holbein", "9", "10", "16", "3"], ["Jacob Jordaens", "10", "8", "16", "6"], ["Lucas Jordaens", "13", "12", "9", "6"], ["Giovanni Lanfranco", "14", "13", "10", "5"], ["Leonardo da Vinci", "15", "16", "4", "14"], ["Lucas van Leyden", "8", "6", "6", "4"], ["Michelangelo", "8", "17", "4", "8"], ["Caravaggio", "6", "6", "16", "O"], ["Murillo", "6", "8", "15", "4"], ["Otho Venius", "13", "14", "10", "10"], ["Palma il Vecchio", "5", "6", "16", "0"], ["Palma il Giovane", "12", "9", "14", "6"], ["Il Parmigianino", "10", "15", "6", "6"], ["Gianfrancesco Penni", "O", "15", "8", "0"], ["Perin del Vaga", "15", "16", "7", "6"], ["Sebastiano del Piombo", "8", "13", "16", "7"], ["Primaticcio", "15", "14", "7", "10"], ["Raphael", "17", "18", "12", "18"], ["Rembrandt", "15", "6", "17", "12"], ["Rubens", "18", "13", "17", "17"], ["Francesco Salviati", "13", "15", "8", "8"], ["Eustache Le Sueur", "15", "15", "4", "15"], ["Teniers", "15", "12", "13", "6"], ["Pietro Testa", "11", "15", "0", "6"], ["Tintoretto", "15", "14", "16", "4"], ["Titian", "12", "15", "18", "6"], ["Van Dyck", "15", "10", "17", "13"], ["Vanius", "15", "15", "12", "13"], ["Veronese", "15", "10", "16", "3"], ["Taddeo Zuccari", "13", "14", "10", "9"], ["Federico Zuccari", "10", "10", "8", "8"]]}, "question": "Which painter has the highest Composition score among all the painters in the table?", "answer": "Guercino, Rubens"}
{"id": "e64c2ddce62c76ba41e5c576b72b1ac4", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["rank", "rank fortune 500", "name", "headquarters", "revenue (millions)", "profit (millions)", "employees", "industry"], "data": [["1", "17", "sinopec", "beijing", "131636.0", "3703.1", "681900", "oil"], ["2", "24", "china national petroleum", "beijing", "110520.2", "13265.3", "1086966", "oil"], ["3", "29", "state grid corporation", "beijing", "107185.5", "2237.7", "1504000", "utilities"], ["4", "170", "industrial and commercial bank of china", "beijing", "36832.9", "6179.2", "351448", "banking"], ["5", "180", "china mobile limited", "beijing", "35913.7", "6259.7", "130637", "telecommunications"], ["6", "192", "china life insurance", "beijing", "33711.5", "173.9", "77660", "insurance"], ["7", "215", "bank of china", "beijing", "30750.8", "5372.3", "232632", "banking"], ["8", "230", "china construction bank", "beijing", "28532.3", "5810.3", "297506", "banking"], ["9", "237", "china southern power grid", "guangzhou", "27966.1", "1074.1", "178053", "utilities"], ["10", "275", "china telecom", "beijing", "24791.3", "2279.7", "400299", "telecommunications"], ["11", "277", "agricultural bank of china", "beijing", "24475.5", "728.4", "452464", "banking"], ["12", "290", "hutchison whampoa", "hong kong", "23661.0", "2578.3", "220000", "various sectors"], ["13", "299", "sinochem corporation", "beijing", "23109.2", "344.7", "20343", "various sectors"], ["14", "307", "baosteel", "shanghai", "22663.4", "1622.2", "91308", "steel"], ["15", "342", "china railway engineering", "beijing", "20520.4", "142.6", "275866", "railway"], ["16", "384", "china railway construction", "beijing", "18735.7", "70.2", "245540", "railway"], ["17", "385", "first automotive works", "changchun", "18710.7", "70.0", "136010", "automobile"], ["18", "396", "china state construction", "beijing", "18163.2", "281.3", "294309", "construction"], ["19", "402", "saic motor", "shanghai", "18010.1", "89.7", "72416", "automobile"], ["20", "405", "cofco limited", "beijing", "17953.2", "281.0", "82481", "various sectors"], ["21", "435", "china minmetals", "beijing", "16902.2", "154.4", "32594", "metal trading"], ["22", "457", "jardine matheson", "hong kong / hamilton", "16281.0", "1348.0", "240000", "various sectors"], ["23", "469", "china national offshore oil", "beijing", "16038.9", "3007.1", "44000", "oil"], ["24", "488", "china ocean shipping", "beijing", "15413.5", "1092.9", "79616", "shipping"]]}, "question": "What is the total revenue of all companies in the oil industry?", "answer": "838478.3"}
{"id": "f23997671da61c46c93fdc184c8f06ef", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["Rank", "Magnitude", "Death toll", "Location", "Depth (km)", "MMI", "Date"], "data": [["1", "8.3", "0", "Russia Russia", "608.9", "V", "May 24"], ["2", "8.0", "13", "Solomon Islands Solomon Islands", "29", "VIII", "February 7"], ["3", "7.7", "35", "Iran Iran", "82", "VII", "April 16"], ["3", "7.7", "825", "Pakistan Pakistan", "20.0", "IX", "September 24"], ["3", "7.7", "0", "Antarctica Coronation Island, Antarctica", "10", "VII", "November 17"], ["6", "7.5", "0", "United States United States", "9.9", "VI", "January 5"], ["7", "7.4", "0", "Tonga Tonga", "171.4", "V", "May 23"], ["8", "7.3", "0", "Papua New Guinea Papua New Guinea", "386.3", "IV", "July 7"], ["8", "7.3", "0", "South Georgia and the South Sandwich Islands South Georgia and the South Sandwich Islands", "31.3", "VI", "July 15"], ["10", "7.2", "0", "Russia Russia", "123.3", "VII", "April 19"], ["11", "7.1", "0", "Solomon Islands Solomon Islands", "10.1", "VI", "February 6"], ["11", "7.1", "0", "Solomon Islands Santa Cruz Islands", "21", "VII", "February 8"], ["11", "7.1", "3", "Peru Peru", "40", "VIII", "September 25"], ["11", "7.1", "222", "Philippines Philippines", "20.0", "IX", "October 15"], ["11", "7.1", "0", "Japan Japan", "26.1", "III", "October 25"], ["16", "7.0", "0", "Solomon Islands Solomon Islands", "10.1", "VII", "February 6"], ["16", "7.0", "0", "Indonesia Indonesia", "66", "VI", "April 6"], ["16", "7.0", "0", "United States United States", "33.5", "VI", "August 30"], ["16", "7.0", "0", "Falkland Islands Falkland Islands", "10", "I", "November 25"]]}, "question": "In the context of seismology, the Magnitude of an earthquake is a measure of its size, with higher magnitudes indicating more powerful earthquakes. What is the average Magnitude of the top 5 earthquakes in the table, ranked by their Death toll?", "answer": "7.52"}
{"id": "51dbc10938c42844de86defc6426167b", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing Nigeria", "Representing Nigeria", "Representing Nigeria", "Representing Nigeria", "Representing Nigeria", "Representing Nigeria"], ["1995", "African Junior Championships", "Bouaké, Ivory Coast", "2nd", "100 m", "10.42"], ["1995", "African Junior Championships", "Bouaké, Ivory Coast", "2nd", "200 m", "20.98"], ["1996", "African Championships", "Yaoundé, Cameroon", "3rd", "100 m", "10.66"], ["1996", "World Junior Championships", "Sydney, Australia", "4th", "200 m", "21.11 (wind: -1.6 m/s)"], ["1997", "African Junior Championships", "Ibadan, Nigeria", "1st", "100 m", "10.55"], ["1997", "African Junior Championships", "Ibadan, Nigeria", "1st", "200 m", "21.12"], ["1998", "African Championships", "Dakar, Senegal", "2nd", "200 m", "20.45"], ["1999", "World Championships", "Seville, Spain", "49th (qf)", "200 m", "21.12"], ["1999", "All-Africa Games", "Johannesburg, South Africa", "5th", "200 m", "20.75"], ["2000", "Olympic Games", "Sydney, Australia", "14th (sf)", "100 m", "10.45"], ["2000", "Olympic Games", "Sydney, Australia", "8th (h)", "4 × 100 m relay", "38.97"], ["2001", "World Indoor Championships", "Edmonton, Canada", "52nd (h)", "60 m", "7.18"], ["2001", "World Championships", "Edmonton, Canada", "10th (h)", "4 × 100 m relay", "39.10"], ["2002", "Commonwealth Games", "Manchester, United Kingdom", "6th", "4 × 100 m relay", "39.01"], ["2002", "African Championships", "Radès, Tunisia", "1st", "4 × 100 m relay", "39.76"]]}, "question": "In which year did the athlete win a gold medal in the 100 m event at the African Junior Championships?", "answer": "1997"}
{"id": "f70dbcddfad0dc93b70e326d3001cb0c", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["rank", "peak", "country", "island", "elevation (m)", "prominence (m)", "col (m)"], "data": [["1", "finisterre range high point", "papua new guinea", "new guinea", "4175", "3734", "441"], ["2", "mount suckling", "papua new guinea", "new guinea", "3676", "2976", "700"], ["3", "mount wilhelm", "papua new guinea", "new guinea", "4509", "2969", "1540"], ["4", "mount victoria", "papua new guinea", "new guinea", "4038", "2738", "1300"], ["5", "mount balbi", "papua new guinea", "bougainville island", "2715", "2715", "0"], ["6", "mount oiautukekea", "papua new guinea", "goodenough island", "2536", "2536", "0"], ["7", "mount giluwe", "papua new guinea", "new guinea", "4367", "2507", "1860"], ["8", "new ireland high point", "papua new guinea", "new ireland", "2340", "2340", "0"], ["9", "mount ulawun", "papua new guinea", "new britain", "2334", "2334", "0"], ["10", "mount kabangama", "papua new guinea", "new guinea", "4104", "2284", "1820"], ["11", "nakanai mountains high point", "papua new guinea", "new britain", "2316", "2056", "260"], ["12", "mount kilkerran", "papua new guinea", "fergusson island", "1947", "1947", "0"], ["13", "mount piora", "papua new guinea", "new guinea", "3557", "1897", "1660"], ["14", "mount bosavi", "papua new guinea", "new guinea", "2507", "1887", "620"], ["15", "mount karoma", "papua new guinea", "new guinea", "3623", "1883", "1740"], ["16", "mount simpson", "papua new guinea", "new guinea", "2883", "1863", "1020"], ["17", "mount kunugui", "papua new guinea", "karkar island", "1833", "1833", "0"], ["18", "mount victory", "papua new guinea", "new guinea", "1891", "1831", "60"], ["19", "manam high point", "papua new guinea", "manam", "1807", "1807", "0"], ["20", "mount michael", "papua new guinea", "new guinea", "3647", "1787", "1860"], ["21", "mount talawe", "papua new guinea", "new britain", "1824", "1773", "51"], ["22", "barurumea ridge", "papua new guinea", "new britain", "2063", "1723", "340"], ["23", "mount sarawaget", "papua new guinea", "new guinea", "4121", "1701", "2420"], ["24", "bewani mountains high point", "papua new guinea", "new guinea", "1980", "1664", "316"], ["25", "mount bel", "papua new guinea", "umboi island", "1658", "1658", "0"], ["26", "unnamed summit", "papua new guinea", "new britain", "1951", "1651", "300"], ["27", "mount maybole", "papua new guinea", "fergusson island", "1665", "1597", "68"], ["28", "adelbert range high point", "papua new guinea", "new guinea", "1716", "1576", "140"], ["29", "sibium mountains high point", "papua new guinea", "new guinea", "2295", "1555", "740"], ["30", "mount shungol", "papua new guinea", "new guinea", "2752", "1518", "1234"], ["31", "mount taraka", "papua new guinea", "bougainville island", "2251", "1511", "740"]]}, "question": "What is the difference in elevation (in meters) between the highest peak and the lowest peak in the table?", "answer": "2851"}
{"id": "29ba53ce7ca43a979263ed36798f62a3", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["season", "tropical lows", "tropical cyclones", "severe tropical cyclones", "strongest storm"], "data": [["1990 - 91", "10", "10", "7", "marian"], ["1991 - 92", "11", "10", "9", "jane - irna"], ["1992 - 93", "6", "3", "1", "oliver"], ["1993 - 94", "12", "11", "7", "theodore"], ["1994 - 95", "19", "9", "6", "chloe"], ["1995 - 96", "19", "14", "9", "olivia"], ["1996 - 97", "15", "14", "3", "pancho"], ["1997 - 98", "10", "9", "3", "tiffany"], ["1998 - 99", "21", "14", "9", "gwenda"], ["1999 - 00", "13", "12", "5", "john / paul"]]}, "question": "What is the average number of tropical cyclones per season?", "answer": "10.6"}
{"id": "d8ac38ac5b42068555d9ce6ab619d048", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "show", "season", "episode title", "episode"], "data": [["2011", "treme", "2", "what is new orleans", "9"], ["2010", "treme", "1", "the foot of canal street", "4"], ["2010", "the pacific", "1", "part 3", "3"], ["2008", "the wire", "5", "late editions", "9"], ["2006", "the wire", "4", "that 's got his own", "12"], ["2004", "the wire", "3", "middle ground", "11"], ["2004", "the wire", "3", "slapstick", "9"], ["2004", "the wire", "3", "hamsterdam", "4"], ["2003", "the wire", "2", "bad dreams", "11"], ["2003", "the wire", "2", "duck and cover", "8"], ["2002", "the wire", "1", "cleaning up", "12"]]}, "question": "What is the average number of episodes per season for 'The Wire' between 2002 and 2004?", "answer": "18.33"}
{"id": "9ed3912eda93df5274890d914b69329e", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "indians admitted", "pakistanis admitted", "sri lankans admitted", "bangladeshis admitted", "nepalis admitted"], "data": [["2000", "26122", "14201", "5849", "2715", "247"], ["2001", "27901", "15353", "5520", "3393", "273"], ["2002", "28838", "14173", "4968", "2615", "418"], ["2003", "24595", "12351", "4448", "1896", "440"], ["2004", "25573", "12793", "4134", "3374", "594"], ["2005", "22141", "13575", "4690", "3940", "714"], ["2006", "30746", "12329", "4490", "3838", "640"], ["2007", "26047", "9545", "3934", "2735", "564"], ["2008", "24548", "8051", "4508", "2716", "639"], ["2009", "26117", "6213", "4270", "4270", "627"], ["2010", "30252", "4986", "4181", "4364", "1502"], ["2011", "24965", "6073", "3104", "2449", "1249"], ["2012", "28943", "9931", "3152", "2449", "1311"], ["total", "346788", "139574", "57248", "35856", "9218"]]}, "question": "What is the average number of Indians admitted per year from 2000 to 2010?", "answer": "26625.45"}
{"id": "9fdf969b7c66712da4f22c788a31c120", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["group", "half - life (s)", "decay constant (s 1 )", "yield , neutrons per fission", "fraction"], "data": [["1", "55.72", "0.0124", "0.00052", "0.000215"], ["2", "22.72", "0.0305", "0.00546", "0.001424"], ["3", "6.22", "0.111", "0.0031", "0.001274"], ["4", "2.3", "0.301", "0.00624", "0.002568"], ["5", "0.614", "1.14", "0.00182", "0.000748"], ["6", "0.23", "3.01", "0.00066", "0.000273"]]}, "question": "Which factors in the table, such as 'half-life (s)', 'decay constant (s-1)', or 'yield, neutrons per fission', significantly influence the 'fraction' values for each group? If none have an effect, please reply 'no clear impact'.", "answer": "yield, neutrons per fission"}
{"id": "03ef349b3920a798e7c9e3b44589d702", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["team", "wins", "losses", "ties", "win pct"], "data": [["arizona cardinals", "2", "1", "0", "0.667"], ["atlanta falcons", "3", "1", "1", "0.7"], ["baltimore ravens", "13", "9", "0", "0.591"], ["buffalo bills", "5", "2", "0", "0.714"], ["carolina panthers", "3", "1", "0", "0.75"], ["chicago bears", "3", "1", "0", "0.75"], ["cincinnati bengals", "21", "9", "0", "0.7"], ["cleveland browns", "19", "5", "0", "0.792"], ["dallas cowboys", "1", "2", "0", "0.333"], ["denver broncos", "1", "3", "0", "0.25"], ["detroit lions", "4", "1", "0", "0.8"], ["green bay packers", "2", "2", "0", "0.5"], ["houston texans", "1", "1", "0", "0.5"], ["indianapolis colts", "4", "1", "0", "0.8"], ["jacksonville jaguars", "8", "10", "0", "0.444"], ["kansas city chiefs", "5", "3", "0", "0.625"], ["miami dolphins", "5", "2", "0", "0.714"], ["minnesota vikings", "2", "2", "0", "0.5"], ["new england patriots", "4", "3", "0", "0.571"], ["new orleans saints", "2", "1", "0", "0.667"], ["new york giants", "2", "1", "0", "0.667"], ["new york jets", "4", "1", "0", "0.8"], ["oakland raiders", "5", "2", "0", "0.714"], ["philadelphia eagles", "2", "2", "0", "0.5"], ["st louis rams", "1", "2", "0", "0.333"], ["san diego chargers", "7", "2", "0", "0.778"], ["san francisco 49ers", "1", "3", "0", "0.25"], ["seattle seahawks", "2", "4", "0", "0.333"], ["tampa bay buccaneers", "3", "1", "0", "0.75"], ["tennessee titans", "11", "12", "0", "0.478"], ["washington redskins", "3", "0", "0", "1.0"], ["totals :", "149", "90", "1", "0.623"]]}, "question": "How many teams have a win percentage of 0.7 or higher?", "answer": "14"}
{"id": "b361d02410aacce63a84d3f811844411", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["no for season", "no for series", "episode", "airdate", "viewers (in millions)"], "data": [["1", "43", "coast to coast", "september 3 , 2013", "2.01"], ["2", "44", "alaskan adventure", "september 10 , 2013", "1.45"], ["3", "45", "off road racing", "september 17 , 2013", "1.54"], ["4", "46", "america 's biggest cars", "september 24 , 2013", "1.88"], ["5", "47", "sturgis", "october 22 , 2013", "1.73"], ["6", "48", "can cars float", "october 29 , 2013", "1.58"]]}, "question": "Based on the viewership trends from the episodes listed in the table, what might be the expected viewership for the next episode in the series?", "answer": "1.60"}
{"id": "4fbaad0b3bacf8c4a5741ff081c032c4", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["Tribunal", "Number of autos da fe", "Executions in persona", "Executions in effigie", "Penanced", "Total"], "data": [["Barcelona", "8", "1", "1", "15", "17"], ["Logroño", "1", "1", "0", "0?", "1?"], ["Palma de Mallorca", "3", "0", "0", "11", "11"], ["Saragossa", "1", "0", "0", "3", "3"], ["Valencia", "4", "2", "0", "49", "51"], ["Las Palmas", "0", "0", "0", "0", "0"], ["Córdoba", "13", "17", "19", "125", "161"], ["Cuenca", "7", "7", "10", "35", "52"], ["Santiago de Compostela", "4", "0", "0", "13", "13"], ["Granada", "15", "36", "47", "369", "452"], ["Llerena", "5", "1", "0", "45", "46"], ["Madrid", "4", "11", "13", "46", "70"], ["Murcia", "6", "4", "1", "106", "111"], ["Seville", "15", "16", "10", "220", "246"], ["Toledo", "33", "6", "14", "128", "148"], ["Valladolid", "10", "9", "2", "70", "81"], ["Total", "125", "111", "117", "1235", "1463"]]}, "question": "How much greater is the total number of executions (in persona and in effigie) in Córdoba compared to Valencia?", "answer": "34"}
{"id": "7c54c117f3afcf34f3f904f00ea42c62", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["Works no.", "Year built", "NGR no.", "SAR no.", "SAR Class"], "data": [["18829", "1909", "330", "1446", "3R"], ["18830", "1909", "331", "1447", "3R"], ["18831", "1909", "332", "1448", "3R"], ["18832", "1909", "333", "1449", "3R"], ["18833", "1909", "334", "1450", "3R"], ["19217", "1910", "345", "1451", "3R"], ["19218", "1910", "346", "1452", "3R"], ["19219", "1910", "347", "1453", "3R"], ["19220", "1910", "348", "1454", "3R"], ["19221", "1910", "349", "1455", "3R"], ["19222", "1910", "350", "1456", "3R"], ["19223", "1910", "351", "1457", "3"], ["19224", "1910", "352", "1458", "3R"], ["19225", "1910", "353", "1459", "3R"], ["19226", "1910", "354", "1460", "3R"], ["19227", "1910", "355", "1461", "3R"], ["19228", "1910", "356", "1462", "3R"], ["19229", "1910", "357", "1463", "3R"], ["19230", "1910", "358", "1464", "3R"], ["19231", "1910", "359", "1465", "3R"], ["19232", "1910", "360", "1466", "3R"], ["19233", "1910", "361", "1467", "3R"], ["19234", "1910", "362", "1468", "3R"], ["19235", "1910", "363", "1469", "3R"], ["19236", "1910", "364", "1470", "3R"], ["19237", "1910", "365", "1471", "3R"], ["19238", "1910", "366", "1472", "3R"], ["19239", "1910", "367", "1473", "3R"], ["19240", "1910", "368", "1474", "3R"], ["19241", "1910", "369", "1475", "3R"]]}, "question": "What is the mean and standard deviation of the Year built column?", "answer": "1909.67, 0.51"}
{"id": "20f1697077ffa4073a621235d1da13c6", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["name", "latitude", "longitude", "diameter (km)", "named after"], "data": [["caccini", "17.4", "170.4", "38.1", "francesca caccini , italian composer"], ["caitlin", "- 65.3", "12.0", "14.7", "irish first name"], ["caiwenji", "- 12.4", "287.6", "22.6", "cai wenji , chinese poet"], ["caldwell", "23.6", "112.4", "51.0", "taylor caldwell , american author"], ["callas", "2.4", "27.0", "33.8", "maria callas , american singer"], ["callirhoe", "21.2", "140.7", "33.8", "callirhoe , greek sculptor"], ["caroline", "6.9", "306.3", "18.0", "french first name"], ["carr", "- 24", "295.7", "31.9", "emily carr , canadian artist"], ["carreno", "- 3.9", "16.1", "57.0", "teresa carreño , n venezuela pianist"], ["carson", "- 24.2", "344.1", "38.8", "rachel carson , american biologist"], ["carter", "5.3", "67.3", "17.5", "maybelle carter , american singer"], ["castro", "3.4", "233.9", "22.9", "rosalía de castro , galician poet"], ["cather", "47.1", "107.0", "24.6", "willa cather , american novelist"], ["centlivre", "19.1", "290.4", "28.8", "susanna centlivre , english actress"], ["chapelle", "6.4", "103.8", "22.0", "georgette chapelle , american journalist"], ["chechek", "- 2.6", "272.3", "7.2", "tuvan first name"], ["chiyojo", "- 47.8", "95.7", "40.2", "chiyojo , japanese poet"], ["chloe", "- 7.4", "98.6", "18.6", "greek first name"], ["cholpon", "40", "290.0", "6.3", "kyrgyz first name"], ["christie", "28.3", "72.7", "23.3", "agatha christie , english author"], ["chubado", "45.3", "5.6", "7.0", "fulbe first name"], ["clara", "- 37.5", "235.3", "3.2", "latin first name"], ["clementina", "35.9", "208.6", "4.0", "portuguese form of clementine , french first name"], ["cleopatra", "65.8", "7.1", "105.0", "cleopatra , egyptian queen"], ["cline", "- 21.8", "317.1", "38.0", "patsy cline , american singer"], ["clio", "6.3", "333.5", "11.4", "greek first name"], ["cochran", "51.9", "143.4", "100.0", "jacqueline cochran , american aviator"], ["cohn", "- 33.3", "208.1", "18.3", "carola cohn , australian artist"], ["colleen", "- 60.8", "162.2", "13.5", "irish first name"], ["comnena", "1.2", "343.7", "19.5", "anna comnena , byzantine princess and writer"], ["conway", "48.3", "39.0", "49.3", "lady anne finch conway , english natural scientist"], ["cori", "25.4", "72.9", "56.1", "gerty cori , czech biochemist"], ["corinna", "22.9", "40.6", "19.2", "corinna , greek poet"], ["corpman", "0.3", "151.8", "46.0", "elizabeth koopman hevelius , astronomer"], ["cortese", "- 11.4", "218.4", "27.7", "isabella cortese , italian physician"], ["cotton", "70.8", "300.2", "48.1", "eugénie cotton , french physicist"], ["cunitz", "14.5", "350.9", "48.6", "maria cunitz , silesian astronomer"], ["cynthia", "- 16.7", "347.5", "15.9", "greek first name"]]}, "question": "Which crater has the largest diameter?", "answer": "cleopatra"}
{"id": "1d88ca6fdff3b3e0089571e8c933e316", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["Color", "Pin (Tip)", "Pin (Ring)", "Color.1"], "data": [["White/Blue", "26", "1", "Blue/White"], ["White/Orange", "27", "2", "Orange/White"], ["White/Green", "28", "3", "Green/White"], ["White/Brown", "29", "4", "Brown/White"], ["White/Slate", "30", "5", "Slate/White"], ["Red/Blue", "31", "6", "Blue/Red"], ["Red/Orange", "32", "7", "Orange/Red"], ["Red/Green", "33", "8", "Green/Red"], ["Red/Brown", "34", "9", "Brown/Red"], ["Red/Slate", "35", "10", "Slate/Red"], ["Black/Blue", "36", "11", "Blue/Black"], ["Black/Orange", "37", "12", "Orange/Black"], ["Black/Green", "38", "13", "Green/Black"], ["Black/Brown", "39", "14", "Brown/Black"], ["Black/Slate", "40", "15", "Slate/Black"], ["Yellow/Blue", "41", "16", "Blue/Yellow"], ["Yellow/Orange", "42", "17", "Orange/Yellow"], ["Yellow/Green", "43", "18", "Green/Yellow"], ["Yellow/Brown", "44", "19", "Brown/Yellow"], ["Yellow/Slate", "45", "20", "Slate/Yellow"], ["Violet/Blue", "46", "21", "Blue/Violet"], ["Violet/Orange", "47", "22", "Orange/Violet"], ["Violet/Green", "48", "23", "Green/Violet"], ["Violet/Brown", "49", "24", "Brown/Violet"], ["Violet/Slate", "50", "25", "Slate/Violet"]]}, "question": "Which color combination has a higher 'Pin (Tip)' value, White/Blue or Red/Blue?", "answer": "Red/Blue"}
{"id": "4d999e44a25d4d802cbb0ea178c847fe", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing Sweden", "Representing Sweden", "Representing Sweden", "Representing Sweden", "Representing Sweden", "Representing Sweden"], ["2008", "World Junior Championships", "Bydgoszcz, Poland", "11th (sf)", "400m", "54.12"], ["2009", "European Junior Championships", "Novi Sad, Serbia", "3rd", "400 m", "54.01"], ["2010", "European Championships", "Barcelona, Spain", "7th", "4 × 100 m relay", "43.75"], ["2011", "European U23 Championships", "Ostrava, Czech Republic", "2nd", "200 m", "23.24"], ["2011", "World Championships", "Daegu, South Korea", "24th (h)", "200 m", "23.31"], ["2011", "World Championships", "Daegu, South Korea", "18th (sf)", "400 m", "52.35"], ["2012", "World Indoor Championships", "Istanbul, Turkey", "6th (sf)", "400 m", "52.29"], ["2012", "European Championships", "Helsinki, Finland", "1st", "400 m", "51.13 (NR)"], ["2013", "World Championships", "Moscow, Russia", "31st (h)", "200 m", "23.33"], ["2013", "World Championships", "Moscow, Russia", "24th (h)", "400 m", "52.39"], ["2018", "European Championships", "Berlin, Germany", "9th (h)", "4 × 400 m relay", "3:32.61"]]}, "question": "In which year did the athlete achieve their personal best time in the 400m event?", "answer": "2012"}
{"id": "a85ef7b98eb51d7b368d260e0c29abc8", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["interval name", "size (steps)", "size (cents)", "just ratio", "just (cents)", "error", "audio"], "data": [["perfect fifth", "9", "720", "3:2", "701.96", "+ 18.04", "play category : articles with haudio microformats"], ["septimal tritone", "7", "560", "7:5", "582.51", "22.51", "play category : articles with haudio microformats"], ["11:8 wide fourth", "7", "560", "11:8", "551.32", "+ 8.68", "play category : articles with haudio microformats"], ["15:11 wide fourth", "7", "560", "15:11", "536.95", "+ 23.05", "play category : articles with haudio microformats"], ["perfect fourth", "6", "480", "4:3", "498.04", "18.04", "play category : articles with haudio microformats"], ["septimal major third", "5", "400", "9:7", "435.08", "35.08", "play category : articles with haudio microformats"], ["undecimal major third", "5", "400", "14:11", "417.51", "17.51", "play category : articles with haudio microformats"], ["major third", "5", "400", "5:4", "386.31", "+ 13.69", "play category : articles with haudio microformats"], ["minor third", "4", "320", "6:5", "315.64", "+ 4.36", "play category : articles with haudio microformats"], ["septimal minor third", "3", "240", "7:6", "266.87", "26.87", "play category : articles with haudio microformats"], ["septimal whole tone", "3", "240", "8:7", "231.17", "+ 8.83", "play category : articles with haudio microformats"], ["major tone", "3", "240", "9:8", "203.91", "+ 36.09", "play category : articles with haudio microformats"], ["minor tone", "2", "160", "10:9", "182.4", "22.40", "play category : articles with haudio microformats"], ["greater undecimal neutral second", "2", "160", "11:10", "165.0", "5.00", "play category : articles with haudio microformats"], ["lesser undecimal neutral second", "2", "160", "12:11", "150.63", "+ 9.36", "play category : articles with haudio microformats"], ["just diatonic semitone", "1", "80", "16:15", "111.73", "31.73", "play category : articles with haudio microformats"], ["septimal chromatic semitone", "1", "80", "21:20", "84.46", "4.47", "play category : articles with haudio microformats"]]}, "question": "What is the correlation between the 'size (cents)' and 'error' columns in the table? Provide the correlation coefficient as evidence.", "answer": "No correlation, 0.10"}
{"id": "f4d60e32414319753c3f708a4b9664ea", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Rank", "Title", "Studio", "Actors", "Gross"], "data": [["1.0", "Star Wars*", "Lucasfilm/20th Century Fox", "Mark Hamill, Harrison Ford, Carrie Fisher, Peter Cushing, Alec Guinness, David Prowse, James Earl Jones, Anthony Daniels, Kenny Baker and Peter Mayhew", "$460,998,007"], ["2.0", "Smokey and the Bandit", "Universal/Rastar", "Burt Reynolds, Sally Field, Jackie Gleason, Jerry Reed and Mike Henry", "$300,000,000"], ["3.0", "Close Encounters of the Third Kind*", "Columbia", "Richard Dreyfuss, Teri Garr, Melinda Dillon and François Truffaut", "$166,000,000"], ["4.0", "Saturday Night Fever", "Paramount", "John Travolta and Karen Lynn Gorney", "$139,486,124"], ["5.0", "The Goodbye Girl", "MGM/Warner Bros./Rastar", "Richard Dreyfuss, Marsha Mason and Quinn Cummings", "$102,000,000"], ["6.0", "The Rescuers*", "Disney", "voices of Eva Gabor, Bob Newhart and Geraldine Page", "$71,215,869"], ["7.0", "Oh, God!", "Warner Bros.", "George Burns, John Denver and Teri Garr", "$51,061,196"], ["8.0", "A Bridge Too Far", "United Artists", "Dirk Bogarde, James Caan, Sean Connery, Elliott Gould, Laurence Olivier, Ryan O'Neal, Robert Redford, Liv Ullmann, Michael Caine, Edward Fox, Anthony Hopkins, Gene Hackman, Hardy Krüger and Maximilian Schell", "$50,800,000"], ["9.0", "The Deep", "Columbia", "Robert Shaw, Nick Nolte and Jacqueline Bisset", "$50,681,884"], ["10.0", "The Spy Who Loved Me", "United Artists", "Roger Moore, Barbara Bach, Curd Jürgens and Richard Kiel", "$46,838,673"], ["11.0", "Annie Hall", "United Artists", "Woody Allen and Diane Keaton", "$38,251,425"], ["12.0", "Semi-Tough", "United Artists", "Burt Reynolds, Kris Kristofferson and Jill Clayburgh", "$37,187,139"], ["13.0", "Pete's Dragon", "Disney", "Helen Reddy, Mickey Rooney and Shelley Winters", "$36,000,000"], ["14.0", "The Gauntlet", "Warner Bros.", "Clint Eastwood and Sondra Locke", "$35,400,000"], ["15.0", "The Turning Point", "20th Century Fox", "Shirley MacLaine, Anne Bancroft, Tom Skerritt, Mikhail Baryshnikov and Leslie Browne", "$33,600,000"], ["16.0", "Heroes", "Universal", "Henry Winkler, Sally Field, and Harrison Ford", "$33,500,000"], ["17.0", "High Anxiety", "20th Century Fox", "Mel Brooks, Madeline Kahn, Cloris Leachman, Harvey Korman, Ron Carey, Howard Morris and Dick Van Patten", "$31,063,038"], ["18.0", "Exorcist II: The Heretic", "Warner Bros.", "Linda Blair, Richard Burton, Louise Fletcher, Max von Sydow and James Earl Jones", "$30,749,142"], ["19.0", "Airport '77", "Universal", "Jack Lemmon, Lee Grant and James Stewart", "$30,000,000"], ["20.0", "Herbie Goes to Monte Carlo", "Disney", "Dean Jones, Don Knotts and Julie Sommars", "$29,000,000"], ["21.0", "Slap Shot", "Universal", "Paul Newman and Strother Martin", "$28,000,000"], ["22.0", "The Other Side of Midnight", "20th Century Fox", "Marie-France Pisier, John Beck and Susan Sarandon", "$24,652,021"], ["23.0", "Looking for Mr. Goodbar", "Paramount", "Diane Keaton, Tuesday Weld and Richard Gere", "$22,512,655"], ["24.0", "For the Love of Benji", "Mulberry Square", "Benjean, Patsy Garrett and Ed Nelson", "$22,257,624"], ["25.0", "The World's Greatest Lover", "20th Century Fox", "Gene Wilder, Carol Kane and Dom DeLuise", "$21,000,000"], ["26.0", "Julia", "20th Century Fox", "Jane Fonda, Vanessa Redgrave, Jason Robards, Hal Holbrook, Rosemary Murphy and Maximilian Schell", "$20,714,400"]]}, "chart_type": "pie", "question": "Please help me draw a pie chart showing the box office earnings of the top ten ranked movies.", "answer": "y_references = [[460998007, 300000000, 166000000, 139486124, 102000000, 71215869, 51061196, 50800000, 50681884, 46838673]]"}
{"id": "6b1d72a62b98d5e4cc0ab30df170fe0d", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["rank", "airline / holding", "passenger fleet", "current destinations", "alliance / association"], "data": [["1", "lufthansa group", "627", "283", "star alliance"], ["2", "ryanair", "305", "176", "elfaa"], ["3", "air france - klm", "621", "246", "skyteam"], ["4", "international airlines group", "435", "207", "oneworld"], ["5", "easyjet", "194", "126", "elfaa"], ["6", "turkish airlines", "222", "245", "star alliance"], ["7", "air berlin group", "153", "145", "oneworld"], ["8", "aeroflot group", "239", "189", "skyteam"], ["9", "sas group", "173", "157", "star alliance"], ["10", "alitalia", "143", "101", "skyteam"], ["11", "norwegian air shuttle asa", "79", "120", "elfaa"], ["12", "pegasus airlines", "42", "70", "n / a"], ["13", "wizz air", "45", "83", "elfaa"], ["14", "transaero", "93", "113", "n / a"], ["15", "tap portugal", "71", "80", "star alliance"], ["16", "aer lingus", "46", "75", "n / a"], ["17", "finnair", "44", "65", "oneworld"], ["18", "s7", "52", "90", "oneworld"], ["19", "air europa", "40", "54", "skyteam"], ["20", "utair aviation", "108", "117", "n / a"], ["21", "sunexpress", "23", "48", "n / a"], ["22", "flybe", "68", "56", "elfaa"], ["23", "brussels airlines", "45", "67", "star alliance"], ["24", "aegean airlines", "29", "40", "star alliance"], ["25", "monarch airlines", "39", "30", "n / a"], ["26", "virgin atlantic", "41", "37", "n / a"], ["27", "atlasjet", "15", "15", "n / a"], ["28", "lot polish airlines", "40", "54", "star alliance"], ["29", "jet2.com", "49", "59", "elfaa"], ["30", "meridiana fly", "18", "40", "n / a"], ["31", "ural airlines", "29", "66", "n / a"], ["32", "czech airlines", "25", "49", "skyteam"], ["33", "airbaltic", "28", "60", "n / a"], ["34", "onur air", "29", "21", "n / a"], ["35", "ukraine international airlines", "40", "54", "n / a"], ["36", "olympic air", "16", "37", "n / a"], ["37", "tarom", "23", "48", "skyteam"], ["38", "icelandair", "27", "36", "n / a"], ["39", "croatia airlines", "13", "40", "star alliance"], ["40", "air serbia", "13", "34", "n / a"], ["41", "belavia", "23", "40", "n / a"], ["42", "cyprus airways", "9", "18", "n / a"], ["43", "bulgaria air", "11", "22", "n / a"], ["44", "adria airways", "12", "37", "star alliance"]]}, "question": "Fleet Utilization Rate is defined as the number of current destinations served by an airline divided by its passenger fleet size. According to this definition of Fleet Utilization Rate, which airline has the highest fleet utilization rate?", "answer": "adria airways"}
{"id": "ebfb7f83b151375b826603749c259ed6", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["lga name", "area (km 2 )", "census 2006 population", "administrative capital", "postal code"], "data": [["port harcourt", "109", "541115", "port harcourt", "500"], ["obio - akpor", "260", "464789", "rumuodumaya", "500"], ["okrika", "222", "222026", "okrika", "500"], ["ogu / bolo", "89", "74683", "ogu", "500"], ["eleme", "138", "190884", "eleme", "501"], ["tai", "159", "117797", "sakpenwa", "501"], ["gokana", "126", "228828", "kpor", "501"], ["khana", "560", "294217", "bori", "502"], ["oyigbo", "248", "122687", "afam", "502"], ["opobo / nkoro", "130", "151511", "opobo town", "503"], ["andoni", "233", "211009", "ngo", "503"], ["bonny", "642", "215358", "bonny", "503"], ["degema", "1011", "249773", "degema", "504"], ["asari - toru", "113", "220100", "buguma", "504"], ["akuku - toru", "1443", "156006", "abonnema", "504"], ["abua / odual", "704", "282988", "abua", "510"], ["ahoada west", "403", "249425", "akinima", "510"], ["ahoada east", "341", "166747", "ahoada", "510"], ["ogba / egbema / ndoni", "969", "284010", "omuku", "510"], ["emohua", "831", "201901", "emohua", "511"], ["ikwerre", "655", "189726", "isiokpo", "511"], ["etche", "805", "249454", "okehi", "512"]]}, "question": "Which factors in the table, such as 'area (km 2 )', 'administrative capital', or 'postal code', significantly influence the 'census 2006 population' for each LGA? If none have an effect, please reply 'no clear impact'.", "answer": "No clear impact"}
{"id": "3b8eafab24cc0cc641819cebe50d9764", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["Canal", "Length (miles)", "Locks", "Max length (ft)", "Width (ft)", "Year opened", "Year abandoned", "Year restored"], "data": [["Aberdeenshire Canal", "18.0", "18.0", null, null, "1805.0", "1854.0", null], ["Buchan Canal", null, null, null, null, null, null, null], ["Caledonian Canal", "62.0", "29.0", "150.0", "35.0", "1822.0", null, null], ["Crinan Canal", "9.0", "15.0", "86.75", "19.65", "1817.0", null, null], ["Dingwall Canal", "1.1", "0.0", null, null, "1816.0", "1840.0", null], ["Forth and Clyde Canal", "35.0", "38.0", "68.58", "19.75", "1790.0", "1963.0", "2002.0"], ["Glasgow, Paisley and Johnstone Canal", "11.0", "0.0", null, null, "1811.0", "1881.0", null], ["Monkland Canal", "12.25", "18.0", "71.0", "14.0", "1794.0", "1942.0", null], ["Stevenston Canal", "2.25", "0.0", null, "13.0", "1772.0", "1830.0", null], ["Union Canal", "31.5", "3.0", "63.0", "12.5", "1822.0", "1930.0", "2000.0"]]}, "question": "Canal Lock Density is calculated as the total number of locks divided by the length of the canal in miles. Based on this definition, which canal has the highest lock density?", "answer": "Crinan Canal"}
{"id": "55f3914d42075dcde9c9c77774156a6c", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "starts", "wins", "top 5", "top 10", "poles", "avg start", "avg finish", "winnings", "position", "team (s)"], "data": [["1985", "1", "0", "0", "0", "0", "16.0", "14.0", "2925", "101st", "07 bob johnson racing"], ["1986", "1", "0", "0", "0", "0", "20.0", "29.0", "1815", "107th", "07 bob johnson racing"], ["1988", "1", "0", "0", "0", "0", "29.0", "37.0", "1460", "97th", "74 wawak racing"], ["1989", "1", "0", "0", "0", "0", "32.0", "28.0", "2725", "83rd", "63 linro motorsports"], ["1990", "2", "0", "0", "0", "0", "33.0", "35.5", "6675", "73rd", "13 linro motorsports"], ["1994", "3", "0", "0", "0", "0", "20.3", "19.7", "30565", "48th", "20 moroso racing 02 tw taylor"], ["1995", "14", "0", "0", "0", "0", "29.4", "27.4", "281945", "40th", "22 bill davis racing 40 brooks / sabco racing"], ["1998", "9", "0", "1", "3", "0", "25.3", "26.2", "336905", "49th", "50 hendrick motorsports"], ["1999", "2", "0", "0", "0", "0", "19.0", "38.5", "71200", "61st", "14 no fear racing"], ["2004", "3", "0", "0", "0", "0", "41.0", "40.3", "160261", "68th", "80 hover motorsports 98 mach 1 motorsports"]]}, "question": "Considering the historical data on the number of starts and average finish positions from 1985 to 2004, what could be the forecasted average finish position for a driver in the year 2005 if they participate in a similar number of races as in 2004?**", "answer": "37.25"}
{"id": "e067a40ab6736ac5a004d9dc69f2d5c0", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["Row Header", "Position", "Age", "Air Group or Subsidiary Officer Since"], "data": [["Bradley D. Tilden", "Chairman and Chief Executive Officer of Alaska Air Group, Inc., Chairman of Alaska Airlines, Inc., Chairman of Horizon Air Industries, Inc.", "58", "1994"], ["Brandon S. Pedersen", "Executive Vice President/Finance and Chief Financial Officer of Alaska Air Group, Inc. and Alaska Airlines, Inc., and Treasurer of Alaska Air Group, Inc. and Alaska Airlines, Inc.", "52", "2003"], ["Kyle B. Levine", "Vice President Legal, General Counsel and Corporate Secretary of Alaska Air Group, Inc. and Alaska Airlines, Inc. and Chief Ethics and Compliance Officer of Alaska Air Group, Inc.", "47", "2016"], ["Benito Minicucci", "President and Chief Operating Officer of Alaska Airlines, Inc.", "52", "2004"], ["Gary L. Beck", "President and Chief Executive Officer of Horizon Air Industries, Inc.", "71", "2018"], ["Andrew R. Harrison", "Executive Vice President and Chief Commercial Officer of Alaska Airlines, Inc.", "49", "2008"], ["Shane R. Tackett", "Executive Vice President, Planning and Strategy of Alaska Airlines, Inc.", "40", "2011"], ["Andrea L. Schneider", "Vice President People of Alaska Airlines, Inc.", "53", "1998"], ["Diana Birkett-Rakow", "Vice President External Relations of Alaska Airlines, Inc.", "41", "2017"]]}, "question": "What is the average age of the executives listed in the table?", "answer": "51.44"}
{"id": "531afa6132809309425cb9afae455a06", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["draw", "song", "performer", "televotes", "rank"], "data": [["1", "preku moreto", "tanja carovska", "1339", "12"], ["2", "ne zaboravaj", "kaliopi", "3834", "9"], ["3", "son", "monika sokolovska", "862", "15"], ["4", "ostani do kraj", "toše proeski & megatim plus", "4210", "8"], ["5", "daj mi pricina da se razbudam", "tanja , lidija & zorica pancic", "2459", "11"], ["6", "samovilska svadba", "sašo gigov - giš", "34774", "2"], ["7", "ne baraj me", "iskra trpeva & granit", "681", "20"], ["8", "ne veruvam", "risto samardziev", "8866", "5"], ["9", "daj mi šansa", "dule & koki", "23615", "3"], ["10", "koj si ti", "biljana dodeva", "828", "16"], ["11", "te sakam beskrajno", "pece ognenov and adrijana janevska", "1100", "13"], ["12", "bez tebe", "duo maratov", "764", "17"], ["13", "ljubovta nema granici", "intervali", "694", "19"], ["14", "kameleon", "maja grozdanovska & bumerang", "3319", "10"], ["15", "andrea", "marjan necak", "725", "18"], ["16", "opomena", "suzana spasovska", "5441", "6"], ["17", "broj do deset", "maja vukicevic", "908", "14"], ["18", "ne zori , zoro", "vlado janevski", "38642", "1"], ["19", "ukradeni nokji", "karolina gočeva", "10454", "4"], ["20", "pari pari", "mico atanasiu", "4453", "7"]]}, "question": "What is the median number of televotes received by performers with rank higher than 10 (including 10)?", "answer": "7153.5"}
{"id": "dc21011c28cb6d8b786c04c5a531dbfb", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["episode no", "airdate", "total viewers", "share", "bbc one weekly ranking"], "data": [["1", "8 april 2010", "6700000", "24.8%", "6"], ["2", "15 april 2010", "5820000", "20.8%", "11"], ["3", "22 april 2010", "6367000", "23.7%", "7"], ["4", "6 may 2010", "5901000", "22.6%", "10"], ["5", "13 may 2010", "6751000", "26.6%", "7"], ["6", "20 may 2010", "6507000", "26.2%", "7"]]}, "question": "Which factors in the table, such as 'airdate', 'total viewers' or 'share', significantly influence the 'bbc one weekly ranking'? If none have an effect, please reply 'no clear impact'.", "answer": "total viewers, share"}
{"id": "0bd23a6e9608ac496e063d9e2bde3ced", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["epoch (utc)", "periselene (km)", "aposelene (km)", "eccentricity", "inclination (deg) (to moon equator)", "period (h)"], "data": [["november 15 , 2004 , 17:47:12.1", "6700.72", "53215.151", "0.776329", "81.085", "129.247777"], ["december 4 , 2004 10:37:47.3", "5454.925", "20713.095", "0.583085", "83.035", "37.304959"], ["january 1 , 2005 , 00:00:00.0", "10000.0", "100000.0", "0.99999", "150.0", "100.0"], ["january 9 , 2005 , 15:24:55.0", "2751.511", "6941.359", "0.432261", "87.892", "8.409861"], ["february 28 , 2005 , 05:18:39.9", "2208.659", "4618.22", "0.352952", "90.063603", "4.970998"], ["march 15 , 2005 , 12:00:00.0", "10.0", "100.0", "0.5", "80.0", "10.0"], ["april 25 , 2005 , 08:19:05.4", "2283.738", "4523.111", "0.328988", "90.141407", "4.949137"], ["may 16 , 2005 , 09:08:52.9", "2291.25", "4515.857", "0.326807", "89.734929", "4.949919"], ["june 20 , 2005 , 10:21:37.1", "2256.09", "4549.196", "0.33696", "90.232619", "4.947432"]]}, "question": "Which data points in the table exhibit unusual patterns in terms of orbital characteristics, such as eccentricity, inclination, periselene, and aposelene?", "answer": "The two anomalies are the extremely high eccentricity (0.99999), inclination (150.0), periselene, and aposelene values in the first row, and the extremely low periselene (10.0) and aposelene (100.0) alongside an unusually high period (10.0) in the second row."}
{"id": "48c12564a70819def0e4e80ce8e55649", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["producer", "product", "samples taken", "samples failed", "melamine content (mg / kg)"], "data": [["shijiazhuang sanlu group", "三鹿牌嬰幼兒配方乳粉", "11", "11", "2563.0"], ["shanghai panda dairy", "熊貓可寶牌嬰幼兒配方乳粉", "5", "3", "619.0"], ["qingdao shengyuan dairy", "聖元牌嬰幼兒配方乳粉", "17", "8", "150.0"], ["shanxi gu cheng dairy", "古城牌嬰幼兒配方乳粉", "13", "4", "141.6"], ["jiangxi guangming yingxiong dairy", "英雄牌嬰幼兒配方乳粉", "2", "2", "98.6"], ["baoji huimin dairy", "惠民牌嬰幼兒配方乳粉", "1", "1", "79.17"], ["inner mongolia mengniu dairy", "蒙牛牌嬰幼兒配方乳粉", "28", "3", "68.2"], ["torador dairy industry (tianjin)", "可淇牌嬰幼兒配方乳粉", "1", "1", "67.94"], ["guangdong yashili group", "雅士利牌嬰幼兒配方乳粉", "30", "8", "53.4"], ["hunan peiyi dairy", "南山倍益牌嬰幼兒配方乳粉", "3", "1", "53.4"], ["heilongjiang qilin dairy", "嬰幼兒配方乳粉2段基粉", "1", "1", "31.74"], ["shanxi yashili dairy", "雅士利牌嬰幼兒配方乳粉", "4", "2", "26.3"], ["shenzhen jinbishi milk", "金必氏牌嬰幼兒配方乳粉", "2", "2", "18.0"], ["scient (guangzhou) infant nutrition", "施恩牌嬰幼兒配方乳粉", "20", "14", "17.0"], ["guangzhou jinding dairy products factory", "金鼎牌嬰幼兒配方乳粉", "3", "1", "16.2"], ["inner mongolia yili industrial group", "伊利牌兒童配方乳粉", "35", "1", "12.0"], ["yantai ausmeadow nutriment", "澳美多牌嬰幼兒配方乳粉", "16", "6", "10.7"], ["qingdao suncare nutritional technology", "愛可丁牌嬰幼兒配方乳粉", "3", "1", "4.8"], ["xi'an baiyue dairy", "御寶牌嬰幼兒配方乳粉", "3", "1", "3.73"], ["yantai leilei dairy", "磊磊牌嬰幼兒配方乳粉", "3", "3", "1.2"], ["shanghai baoanli dairy", "寶安力牌嬰幼兒配方乳粉", "1", "1", "0.21"], ["fuding chenguan dairy", "聰爾壯牌嬰幼兒配方乳粉", "1", "1", "0.09"]]}, "question": "What is the average melamine content (mg / kg) of the dairy products that had at least 5 samples taken?", "answer": "403.88"}
{"id": "2b3b7a5385423b924d7fda58d40a95e6", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["year", "us rank", "total s ton", "domestic s ton", "foreign total s ton", "foreign imports s ton", "foreign exports s ton"], "data": [["2006", "102", "2926536", "2306192", "620344", "464774", "155570"], ["2005", "94", "3527469", "2629553", "897916", "430396", "467520"], ["2004", "101", "3085753", "2323089", "762664", "284347", "478317"], ["2003", "96", "3178633", "2494261", "684372", "218233", "466139"], ["2002", "102", "2983137", "2318653", "664484", "251203", "413281"], ["2001", "108", "2861134", "2157496", "703638", "225281", "478357"], ["2000", "103", "3157247", "2416514", "740733", "382240", "358493"]]}, "question": "In which year did the US experience a higher percentage increase in domestic steel tonnage compared to foreign total steel tonnage?", "answer": "2002, 2003"}
{"id": "aca822dccfa5b7a04abe4dd08ba88e50", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["#", "Name", "Birth and death", "Office started", "Office ended"], "data": [["1", "Geir Vídalín", "27 October 1761\n–\n20 September 1823", "1801", "1823"], ["2", "Steingrímur Jónsson", null, "1824", "1845"], ["3", "Helgi Thordersen", "8 April 1794\n–\n4 December 1867", "1846", "1866"], ["4", "'Pétur Pétursson", "3 October 1808\n–\n15 May 1891", "1866", "1889"], ["5", "Hallgrímur Sveinsson", "5 April 1841\n–\n16 December 1909", "1889", "1908"], ["6", "Þórhallur Bjarnarson", "2 December 1855\n–\n15 December 1916", "1908", "1916"], ["7", "Jón Helgason", "1866\n–\n1942", "1917", "1939"], ["8", "Sigurgeir Sigurðsson", "3 August 1890\n-\n13 October 1953", "1939", "1953"], ["9", "Ásmundur Guðmundsson", "6 October 1888\nReykholt\n–\n29 May 1969\nReykjavík", "1954", "1989"], ["10", "Sigurbjörn Einarsson", "30 June 1911\nVestur-Skaftafellssýsla\n–\n28 August 2008\nReykjavík", "1959", "1981"], ["11", "Pétur Sigurgeirsson\n(son of Sigurgeir Sigurðsson, 8th Bishop of Iceland)", "2 June 1919\n–\n3 June 2010", "1981", "1989"], ["12", "Ólafur Skúlason", "29 December 1929\n–\n9 June 2008", "1989", "1997"], ["13", "Karl Sigurbjörnsson\n(son of Sigurbjörn Einarsson, 10th Bishop of Iceland)", "5 February 1947\nReykjavík", "1998", "2012"], ["14", "Agnes Sigurðardóttir", "19 October 1954\nÍsafjörður", "24 June 2012", "Incumbent"]]}, "question": "Which bishop had the longest tenure in office, and how is the difference compared to the average tenure of all the bishops?", "answer": "Ásmundur Guðmundsson, 16.85"}
{"id": "4571da3300307735b9cf00e6c8061715", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["component 1", "bp comp 1 (˚c)", "component 2", "bp comp 2 (˚c)", "bp azeo (˚c)", "% wt comp 1", "% wt comp 2"], "data": [["acetaldehyde", "21.0", "diethyl ether", "34.6", "20.5", "76.0", "24.0"], ["acetaldehyde", "21.0", "n - butane", "- 0.5", "- 7.0", "16.0", "84.0"], ["acetamide", "222.0", "benzaldehyde", "179.5", "178.6", "6.5", "93.5"], ["acetamide", "222.0", "nitrobenzene", "210.9", "202.0", "24.0", "76.0"], ["acetamide", "222.0", "o - xylene", "144.1", "142.6", "11.0", "89.0"], ["acetonitrile", "82.0", "ethyl acetate", "77.15", "74.8", "23.0", "77.0"], ["acetonitrile", "82.0", "toluene", "110.6", "81.1", "25.0", "75.0"], ["acetylene", "- 86.6", "ethane", "- 88.3", "- 94.5", "40.7", "59.3"], ["aniline", "184.4", "o - cresol", "191.5", "191.3", "8.0", "92.0"], ["carbon disulfide", "46.2", "diethyl ether", "34.6", "34.4", "1.0", "99.0"], ["carbon disulfide", "46.2", "1 , 1 - dichloroethane", "57.2", "46.0", "94.0", "6.0"], ["carbon disulfide", "46.2", "methyl ethyl ketone", "79.6", "45.9", "84.7", "15.3"], ["carbon disulfide", "46.2", "ethyl acetate", "77.1", "46.1", "97.0", "3.0"], ["carbon disulfide", "46.2", "methyl acetate", "57.0", "40.2", "73.0", "27.0"], ["chloroform", "61.2", "methyl ethyl ketone", "79.6", "79.9", "17.0", "83.0"], ["chloroform", "61.2", "n - hexane", "68.7", "60.0", "72.0", "28.0"], ["carbon tetrachloride", "76.8", "methyl ethyl ketone", "79.9", "73.8", "71.0", "29.0"], ["carbon tetrachloride", "76.8", "ethylene dichloride", "84.0", "75.3", "78.0", "22.0"], ["carbon tetrachloride", "76.8", "ethyl acetate", "77.1", "74.8", "57.0", "43.0"], ["cyclohexane", "81.4", "ethyl acetate", "77.15", "72.8", "46.0", "54.0"], ["cyclohexane", "81.4", "ethyl nitrate", "88.7", "74.5", "64.0", "36.0"], ["diethyl ether", "34.6", "methyl formate", "31.50", "28.2", "44.0", "56.0"], ["diethyl ether", "34.6", "methylene chloride", "40", "40.8", "30.0", "70.0"], ["nitromethane", "101.0", "toluene", "110.8", "96.5", "55.0", "45.0"], ["tetrahydrofuran", "65.6", "chloroform", "61.2", "72.5", "34.5", "65.5"], ["tetrahydrofuran", "65.6", "n - hexane", "69", "63.0", "46.5", "53.5"], ["toluene", "110.63", "pyridine", "115.3", "110.2", "78.0", "22.0"], ["propylene glycol", "188.2", "aniline", "184.4", "179.5", "43.0", "57.0"], ["propylene glycol", "188.2", "o - xylene", "144.4", "135.8", "10.0", "90.0"], ["propylene glycol", "188.2", "toluene", "110.6", "110.5", "1.5", "98.5"]]}, "question": "What is the correlation between the 'bp comp 1 (˚C)' and '% wt comp 1' columns in the table? Provide the correlation coefficient as evidence.", "answer": "Weak negative correlation, -0.45"}
{"id": "a8f29aa7448ca2c774592e7a2078cadc", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["country", "number of troops", "% of total troops", "troops per one million population", "troops per 1 billion ( usd ) gdp"], "data": [["united states", "74400", "68.216%", "291.3", "6.06"], ["united kingdom", "9500", "7.201%", "153.5", "4.21"], ["germany", "4318", "3.721%", "59.8", "1.44"], ["italy", "4000", "3.016%", "63.5", "1.81"], ["france", "2453", "2.892%", "61.4", "1.49"], ["poland", "2432", "1.915%", "66.5", "5.41"], ["romania", "1808", "1.308%", "81.4", "10.52"], ["georgia", "1561", "1.218%", "219.0", "85.95"], ["australia", "1550", "1.175%", "72.1", "1.35"], ["spain", "1500", "1.136%", "33.1", "1.02"], ["turkey", "1271", "1.364%", "23.8", "2.76"], ["canada", "950", "2.198%", "27.7", "1.85"], ["denmark", "624", "0.565%", "136.4", "2.35"], ["bulgaria", "563", "0.584%", "81.1", "12.66"], ["norway", "538", "0.313%", "85.0", "1.01"], ["belgium", "520", "0.400%", "49.3", "1.13"], ["netherlands", "500", "0.149%", "11.8", "0.24"], ["sweden", "500", "0.671%", "53.8", "1.14"], ["czech republic", "423", "0.351%", "44.5", "2.35"], ["hungary", "563", "0.584%", "48.4", "3.57"], ["republic of korea", "350", "0.323%", "8.8", "0.47"], ["slovakia", "343", "0.224%", "54.7", "3.01"], ["croatia", "320", "0.227%", "67.8", "4.66"], ["lithuania", "241", "0.142%", "57.7", "4.99"], ["albania", "211", "0.195%", "81.1", "19.59"], ["finland", "181", "0.125%", "30.8", "0.71"], ["latvia", "180", "0.103%", "60.7", "5.38"], ["macedonia", "177", "0.124%", "79.9", "17.12"], ["estonia", "154", "0.120%", "117.8", "8.21"], ["new zealand", "152", "0.179%", "54.9", "2.00"], ["portugal", "137", "0.086%", "10.7", "0.49"], ["armenia", "127", "0.030%", "42.8", "3.36"], ["mongolia", "101", "0.047%", "23.0", "11.79"], ["azerbaijan", "94", "0.071%", "10.5", "2.04"], ["slovenia", "80", "0.060%", "38.9", "1.60"], ["bosnia and herzegovina", "59", "0.034%", "12.0", "2.45"], ["tonga", "55", "0.047%", "528.8", "183.70"], ["malaysia", "42", "0.023%", "1.1", "0.16"], ["montenegro", "41", "0.027%", "57.5", "7.47"], ["united arab emirates", "35", "0.027%", "7.4", "0.12"], ["ukraine", "24", "0.015%", "0.4", "0.17"], ["greece", "12", "0.100%", "11.8", "0.40"], ["luxembourg", "10", "0.007%", "18.3", "0.17"], ["ireland", "6", "0.005%", "1.5", "0.03"], ["austria", "3", "0.002%", "0.4", "0.01"], ["iceland", "3", "0.002%", "6.1", "0.17"], ["isaf exact total", "112579", "100.000%", "117.1 (average)", "3.49 (average)"]]}, "question": "Which country has the highest troops per one million population, and what is the percentage difference between this country and the country with the next highest troops per one million population?", "answer": "Tonga, 81.53%"}
{"id": "2e09024d7ebcee21c3bd33ae5f07e020", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["administrative region", "population (2002 census data)", "surface km 2", "main rivers", "average annual rainfall (mm)", "average annual runoff (mm)", "per capita average annual renewable water resources m 3"], "data": [["i - tarapacá", "428594", "58698", "azapa river , vítor river and camarones river", "93.6", "7.1", "972"], ["ii - antofagasta", "493984", "126444", "loa river", "44.5", "0.2", "51"], ["iii - atacama", "254336", "75573", "salado river", "82.4", "0.7", "208"], ["iv - coquimbo", "603210", "40656", "elqui river , choapa river and limarí river", "222.0", "18.0", "1213"], ["v - valparaíso", "1539852", "16396", "petorca river , la ligua river and aconcagua river", "434.0", "84.0", "894"], ["metro region (mr) - santiago metropolitan", "7003122", "15349", "maipo river", "650.0", "200.0", "438"], ["vii - maule", "908097", "30325", "mataquito river and maule river", "1377.0", "784.0", "26181"], ["viii - biobío", "1861562", "36929", "itata river , biobío river and laja river", "1766.0", "1173.0", "23270"]]}, "question": "How much greater is the average annual rainfall in the 'v - valparaíso' region compared to the 'ii - antofagasta' region?", "answer": "389.5"}
{"id": "f99e2389f5d1f9e1ea48c27d37ec0ec2", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["election", "of candidates nominated", "of seats won", "of total votes", "% of popular vote"], "data": [["1945", "203", "65", "1448744", "27.62%"], ["1949", "249", "41", "1734261", "29.62%"], ["1953", "248", "50", "1749579", "31.01%"], ["1957", "256", "109", "2564732", "38.81%"], ["1958", "265", "208", "3908633", "53.56%"], ["1962", "265", "114", "2865542", "37.22%"], ["1963", "265", "93", "2582322", "32.72%"], ["1965", "265", "95", "2500113", "32.41%"], ["1968", "262", "72", "2548949", "31.36%"], ["1972", "265", "107", "3388980", "35.02%"], ["1974", "264", "95", "3371319", "35.46%"], ["1979", "282", "136", "4111606", "35.89%"], ["1980", "282", "103", "3552994", "32.49%"], ["1984", "282", "211", "6278818", "50.03%"], ["1988", "295", "169", "5667543", "43.02%"], ["1993", "295", "2", "2178303", "16.04%"], ["1997", "301", "20", "2446705", "18.84%"], ["2000", "291", "12", "1566994", "12.19%"]]}, "question": "In which election year did the party achieve the highest percentage of popular vote?", "answer": "1958"}
{"id": "bf75201c615c8bb7f27ad1b146d5c447", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Year", "Total\npassengers", "Passenger\nChange", "Domestic", "International\n(total)", "International\n(non-CIS)", "CIS", "Aircraft\nLandings", "Cargo\n(tonnes)"], "data": [["2000", "930 251", "+2%", "698 957", "231 294", "155 898", "75 396", "8 619", "18 344"], ["2001", "1 028 295", "+10,5%", "733 022", "295 273", "186 861", "108 412", "9 062", "22 178"], ["2002", "1 182 815", "+15,0%", "793 295", "389 520", "239 461", "150 059", "10 162", "20 153"], ["2003", "1 335 757", "+12,9%", "879 665", "456 092", "297 421", "158 671", "10 092", "18 054"], ["2004", "1 553 628", "+16,3%", "972 287", "581 341", "429 049", "152 292", "11 816", "20 457"], ["2005", "1 566 792", "+0,8%", "1 006 422", "560 370", "429 790", "130 580", "11 877", "11 545"], ["2006", "1 764 948", "+12,7%", "1 128 489", "636 459", "488 954", "147 505", "13 289", "15 519"], ["2007", "2 345 097", "+32,9%", "1 486 888", "858 209", "683 092", "175 117", "16 767", "16 965"], ["2008", "2 529 395", "+7,8%", "1 523 102", "1 006 293", "815 124", "191 169", "16 407", "17 142"], ["2009", "2 169 136", "−14,2%", "1 290 639", "878 497", "727 718", "150 779", "13 798", "13 585"], ["2010", "2 748 919", "+26,7%", "1 529 245", "1 219 674", "1 017 509", "202 165", "15 989", "22 946"], ["2011", "3 355 883", "+22,1%", "1 856 948", "1 498 935", "1 184 771", "314 164", "20 142", "24 890"], ["2012", "3 783 069", "+12.7%", "1 934 016", "1 849 053", "1 448 765", "439 668", "21 728", "25 866"], ["2013", "4 293 002", "+13.5%", "2 180 227", "2 112 775", null, null, "25 728", "27 800"]]}, "chart_type": "line", "question": "Please help me draw a stacked bar chart showing the trends in domestic flight passenger count, international flight non-CIS passenger count, and CIS passenger count from 2000 to 2013.", "answer": "y_references = [[ 698957, 733022, 793295, 879665, 972287, 1006422, 1128489, 1486888, 1523102, 1290639, 1529245, 1856948, 1934016, 2180227 ],[ 155898, 186861, 239461, 297421, 429049, 429790, 488954, 683092, 815124, 727718, 1017509, 1184771, 1448765, 2112775 ],[ 75396, 108412, 150059, 158671, 152292, 130580, 147505, 175117, 191169, 150779, 202165, 314164, 439668, 0 ]]"}
{"id": "9bd2405b2c4d9af26013351147098518", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["rank", "city", "state", "gdp in id b", "population m (luz)", "gdp per capita id k", "eurozone"], "data": [["1", "paris", "france", "731", "11.5", "62.4", "y"], ["2", "london", "united kingdom", "565", "11.9", "49.4", "n"], ["3", "moscow", "russia", "321", "10.5", "30.6", "n"], ["4", "madrid", "spain", "230", "5.8", "39.7", "y"], ["5", "istanbul", "turkey", "187", "13.2", "14.2", "n"], ["6", "barcelona", "spain", "177", "4.97", "35.6", "y"], ["7", "rome", "italy", "144", "3.46", "41.6", "y"], ["8", "milan", "italy", "136", "3.08", "44.2", "y"], ["9", "vienna", "austria", "122", "2.18", "56.0", "y"], ["10", "lisbon", "portugal", "98", "2.44", "40.2", "y"], ["11", "athens", "greece", "96", "4.01", "23.9", "y"], ["12", "berlin", "germany", "95", "4.97", "19.1", "y"]]}, "question": "Which city has the lowest GDP per capita in thousands of ID?", "answer": "istanbul"}
{"id": "b6ce5ae3244350599a7403a76a1f1c69", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["rank", "name", "city", "region", "height (m)", "height (ft)", "floors", "estimated completion"], "data": [["1", "leadenhall building", "london", "greater london", "225", "737", "48", "2014"], ["2", "52 - 54 lime street", "london", "greater london", "190", "623", "38", "2017"], ["3", "100 bishopsgate", "london", "greater london", "172", "564", "40", "2015"], ["4", "1 blackfriars", "london", "greater london", "163", "535", "52", "2018"], ["5", "20 fenchurch street", "london", "greater london", "160", "525", "36", "2014"], ["6", "baltimore tower", "london", "greater london", "150", "495", "45", "2016"], ["7", "providence tower", "london", "greater london", "136", "446", "44", "2015"], ["8", "one the elephant", "london", "greater london", "133", "436", "37", "2016"], ["9", "25 churchill place", "london", "greater london", "130", "427", "23", "2014"], ["10", "lots road tower 1", "london", "greater london", "122", "400", "37", "2015"], ["11", "lexicon tower", "london", "greater london", "115", "377", "35", "2016"]]}, "question": "What is the average height (in meters) of the top 5 tallest buildings in the table?", "answer": "182"}
{"id": "e22a374e087942766de36d3bd733f72a", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["Year", "Order", "Quantity", "GER Nos."], "data": [["1893", "N31", "1", "999"], ["1893", "H33", "10", "979–988"], ["1894", "L33", "10", "989–998"], ["1894", "E34", "10", "969–978"], ["1896", "N37", "10", "959–968"], ["1897", "H40", "10", "949–958"], ["1897", "O41", "10", "602–608, 946–948"], ["1898", "G42", "10", "542–551"], ["1898", "K43", "10", "562–571"]]}, "question": "What is the total quantity of orders placed in the earliest two years represented in the table?", "answer": "31"}
{"id": "7905cb93ae282659886179a302c00327", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["SR No.", "BR No.", "Date Built", "Date Withdrawn"], "data": [["950", "30950", "March 1929", "October 1962"], ["951", "30951", "April 1929", "November 1962"], ["952", "30952", "May 1929", "November 1962"], ["953", "30953", "May 1929", "December 1962"], ["954", "30954", "June 1929", "December 1962"], ["955", "30955", "July 1929", "December 1962"], ["956", "30956", "August 1929", "December 1962"], ["957", "30957", "September 1929", "November 1962"]]}, "question": "What is the difference in months between the earliest and latest 'Date Built' for the locomotives with consecutive 'SR No.'?", "answer": "6"}
{"id": "6ba14be153d5a11f0caeebe3e441125d", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "builder", "motors", "trailers", "control trailers"], "data": [["1923", "brcw", "-", "1", "-"], ["1923", "clco", "-", "1", "-"], ["1923", "grcw", "-", "1", "1"], ["1923", "leeds", "-", "1", "-"], ["1923", "mcwf", "-", "1", "-"], ["1923", "brcw", "-", "35", "-"], ["1923", "clco", "41", "40", "-"], ["1923", "mcwf", "40", "-", "35"], ["1924", "brcw", "-", "50", "-"], ["1924", "clco", "-", "-", "25"], ["1924", "mcwf", "52", "-", "-"], ["1925", "clco", "48", "-", "-"], ["1925", "mcwf", "-", "5", "67"], ["1926", "mcwf", "64", "48", "-"], ["1927", "mcwf", "110", "160", "36"], ["1927", "ucc", "77", "37", "68"], ["1929", "ucc", "18", "17", "18"], ["1930", "mccw", "22", "20", "20"], ["1930", "ucc", "2", "4", "-"], ["1931", "brcw", "-", "90", "-"], ["1931", "grcw", "-", "40", "-"], ["1931", "mccw", "145", "-", "-"], ["1934", "mccw", "26", "-", "-"], ["totals", "1466", "645", "551", "270"]]}, "question": "What is the percentage increase in the total number of trailers from 1923 to 1927?", "answer": "146.25%"}
{"id": "ef53d3b9a97d7e762349294a5271f2b8", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["Club", "Season", "League", "League", "Cup", "Cup", "Continental", "Continental", "Total", "Total"], "data": [["Club", "Season", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals"], ["River Plate", "1945", "1", "0", "0", "0", "0", "0", "1", "0"], ["Huracán (loan)", "1946", "25", "10", "2", "0", "0", "0", "27", "10"], ["Huracán (loan)", "Total", "25", "10", "2", "0", "0", "0", "27", "10"], ["River Plate", "1947", "30", "27", "0", "0", "2", "1", "32", "28"], ["River Plate", "1948", "23", "13", "1", "1", "6", "4", "30", "18"], ["River Plate", "1949", "12", "9", "0", "0", "0", "0", "12", "9"], ["River Plate", "Total", "66", "49", "1", "1", "8", "5", "75", "55"], ["Millonarios", "1949", "14", "16", "0", "0", "0", "0", "14", "16"], ["Millonarios", "1950", "29", "23", "2", "1", "0", "0", "31", "24"], ["Millonarios", "1951", "34", "32", "4?", "4?", "0", "0", "38?", "36?"], ["Millonarios", "1952", "24", "19", "4?", "5?", "0", "0", "28?", "24?"], ["Millonarios", "Total", "101", "90", "10", "10", "0", "0", "111", "100"], ["Real Madrid", "1953-54", "28", "100", "0", "0", "0", "0", "28", "100"], ["Real Madrid", "1954-55", "30", "25", "0", "0", "2", "0", "32", "25"], ["Real Madrid", "1955-56", "30", "24", "0", "0", "7", "5", "37", "29"], ["Real Madrid", "1956-57", "30", "31", "3", "3", "10", "9", "43", "43"], ["Real Madrid", "1957-58", "30", "19", "7", "7", "7", "10", "44", "36"], ["Real Madrid", "1958-59", "28", "23", "8", "5", "7", "6", "43", "34"], ["Real Madrid", "1959-60", "23", "12", "5", "3", "6", "8", "34", "23"], ["Real Madrid", "1960-61", "23", "21", "9", "8", "4", "1", "36", "30"], ["Real Madrid", "1961-62", "23", "11", "8", "4", "10", "7", "41", "22"], ["Real Madrid", "1962-63", "13", "12", "9", "9", "2", "1", "24", "22"], ["Real Madrid", "1963-64", "24", "11", "1", "1", "9", "5", "34", "17"], ["Real Madrid", "Total", "282", "216", "50", "40", "64", "52", "396", "308"], ["Espanyol", "1964-65", "-10", "7", "3", "2", "0", "0", "-7", "9"], ["Espanyol", "1965-66", "23", "4", "4", "1", "6", "0", "33", "5"], ["Espanyol", "Total", "47", "11", "7", "3", "6", "0", "60", "14"], ["Career totals", "Career totals", "521", "376", "70", "54", "78", "57", "669", "487"]]}, "question": "Identify the anomaly in the football player's career statistics that may indicate an error in data entry or an unusual circumstance.", "answer": "The two anomalies include the implausible 100 goals in a season, and the -10 appearances."}
{"id": "910bce6a7c6d7346fb3efa20a9469d9f", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["Particulars", "Total", "Male", "Female"], "data": [["Total No. of Houses", "122", "-", "-"], ["Population", "524", "261", "263"], ["Child (0-6)", "95", "46", "49"], ["Schedule Caste", "275", "137", "138"], ["Schedule Tribe", "0", "0", "0"], ["Literacy", "60.14 %", "65.12 %", "55.14 %"], ["Total Workers", "194", "143", "51"], ["Main Worker", "194", "0", "0"], ["Marginal Worker", "0", "0", "0"]]}, "question": "Which demographic category has the highest percentage value of male in the given table?", "answer": "Total Workers"}
{"id": "0e1001d55ac9d8f38aa594007e13070e", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["0", "pōlô", "Unnamed: 2", "Unnamed: 3", "Unnamed: 4", "Unnamed: 5", "Unnamed: 6", "Unnamed: 7", "Unnamed: 8", "Unnamed: 9", "Unnamed: 10", "Unnamed: 11", "Unnamed: 12", "Unnamed: 13", "Unnamed: 14", "Unnamed: 15", "Unnamed: 16", "Unnamed: 17", "Unnamed: 18", "Unnamed: 19"], "data": [["1", "əsad", "11", "samsad", "21", "darwamsad", "31", "tolomsad", "41", "pamsad", "51", "limamsad", "61", "nəmsad", "71", "pitomsad", "81", "walomsad", "91", "yamsad"], ["2", "darwā", "12", "samdarwā", "22", "darwamdarwā", "32", "tolomdarwā", "42", "pamdarwā", "52", "limamdarwā", "62", "nəmdarwā", "72", "pitomdarwā", "82", "walomdarwā", "92", "yamdarwā"], ["3", "tolō", "13", "samtolō", "23", "darwamtolō", "33", "tolomtolō", "43", "pamtolō", "53", "limamtolō", "63", "nəmtolō", "73", "pitomtolō", "83", "walomtolō", "93", "yamtolō"], ["4", "əpat", "14", "sampat", "24", "darwampat", "34", "tolompat", "44", "pampat", "54", "limampat", "64", "nəmpat", "74", "pitompat", "84", "walompat", "94", "yampat"], ["5", "limā", "15", "samlimā", "25", "darwamlimā", "35", "tolomlimā", "45", "pamlimā", "55", "limamlimā", "65", "nəmlimā", "75", "pitomlimā", "85", "walomlimā", "95", "yamlimā"], ["6", "ənəm", "16", "samnəm", "26", "darwamnəm", "36", "tolomnəm", "46", "pamnəm", "56", "limamnəm", "66", "nəmnəm", "76", "pitomnəm", "86", "walomnəm", "96", "yamnəm"], ["7", "pitō", "17", "sampitō", "27", "darwampitō", "37", "tolompitō", "47", "pampitō", "57", "limampitō", "67", "nəmpitō", "77", "pitompitō", "87", "walompitō", "97", "yampitō"], ["8", "walō", "18", "samwalō", "28", "darwamwalō", "38", "tolomwalō", "48", "pamwalō", "58", "limamwalō", "68", "nəmwalō", "78", "pitomwalō", "88", "walomwalō", "98", "yamwalō"], ["9", "siyam", "19", "samsiyam", "29", "darwamsiyam", "39", "tolomsiyam", "49", "pamsiyam", "59", "limamsiyam", "69", "nəmsiyam", "79", "pitomsiyam", "89", "walomsiyam", "99", "yamsiyam"], ["10", "sampōlô", "20", "darwampōlô", "30", "tolompōlô", "40", "pampōlô", "50", "limampōlô", "60", "nəmpōlô", "70", "pitompōlô", "80", "walompōlô", "90", "yampōlô", "100", "saŋgatos"]]}, "question": "How many rows have a value in the 'pōlô' column that starts with the letter 'ə'?", "answer": "3"}
{"id": "82e094eabf0ec04f7bda6f1782715c7f", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["member state", "population millions", "meps", "inhabitants per mep", "influence"], "data": [["austria", "8.27", "17", "486235", "1.71"], ["belgium", "10.51", "22", "477773", "1.74"], ["bulgaria", "7.72", "17", "454059", "1.83"], ["cyprus", "0.77", "6", "127667", "6.52"], ["czech republic", "10.25", "22", "465955", "1.79"], ["denmark", "5.43", "13", "417538", "1.99"], ["estonia", "1.34", "6", "224000", "3.72"], ["finland", "5.26", "13", "404308", "2.06"], ["france", "62.89", "72", "873417", "0.95"], ["germany", "82.43", "99", "832606", "1.0"], ["greece", "11.13", "22", "505682", "1.65"], ["hungary", "10.08", "22", "458045", "1.82"], ["ireland", "4.21", "12", "350750", "2.37"], ["italy", "58.75", "72", "816000", "1.02"], ["latvia", "2.3", "8", "286875", "2.9"], ["lithuania", "3.4", "12", "283583", "2.94"], ["luxembourg", "0.46", "6", "76667", "10.86"], ["malta", "0.4", "5", "80800", "10.3"], ["netherlands", "16.33", "25", "653360", "1.27"], ["poland", "38.16", "50", "763140", "1.09"], ["portugal", "10.57", "22", "480455", "1.73"], ["romania", "21.61", "33", "654848", "1.27"], ["slovakia", "5.39", "13", "414538", "2.01"], ["slovenia", "2.0", "7", "286143", "2.91"], ["spain", "43.76", "50", "875160", "0.95"], ["sweden", "9.05", "18", "502667", "1.66"], ["united kingdom", "60.64", "72", "839194", "0.99"]]}, "question": "Can you describe the main contents of the table, explain the significance of each column, and provide some initial insights based on the data presented?", "answer": "The table presents data on the representation of various member states in a legislative body, detailing each state's population, number of MEPs, inhabitants per MEP, and a calculated influence score. It highlights the balance of representation and influence among member states, showing that smaller states have fewer inhabitants per MEP, potentially increasing their per capita influence in legislative decisions."}
{"id": "1b9e6880bae6250d652f23b7ae3c9102", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["Unnamed: 0", "1948", "1952", "1956", "1960", "1964"], "data": [["all voters", "50.0", "45.0", "42", "50", "61"], ["White", "50.0", "43.0", "41", "49", "59"], ["Black", "50.0", "79.0", "61", "68", "94"], ["College educated", "22.0", "34.0", "31", "39", "52"], ["High School educated", "51.0", "45.0", "42", "52", "62"], ["Grade School educated", "64.0", "52.0", "50", "55", "66"], ["Professional & Business", "19.0", "36.0", "32", "42", "54"], ["White Collar", "47.0", "40.0", "37", "48", "57"], ["Manual worker", "66.0", "55.0", "50", "60", "71"], ["Farmer", "60.0", "33.0", "46", "48", "53"], ["Union member", "76.0", null, "51", "62", "77"], ["Not union", "42.0", null, "35", "44", "56"], ["Protestant", "43.0", "37.0", "37", "38", "55"], ["Catholic", "62.0", "56.0", "51", "78", "76"], ["Republican", null, "8.0", "4", "5", "20"], ["Independent", null, "35.0", "30", "43", "56"], ["Democrat", null, "77.0", "85", "84", "87"], ["East", "48.0", "45.0", "40", "53", "68"], ["Midwest", "50.0", "42.0", "41", "48", "61"], ["West", "49.0", "42.0", "43", "49", "60"], ["South", "53.0", "51.0", "49", "51", "52"]]}, "question": "What percentage of Black voters voted in 1960?", "answer": "68"}
{"id": "0e6bfa743fe904ddbfc8db43b39bfb3d", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "Measure unit", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)", "POPULATION (by age group in 2002)"], "data": [["SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "SPECIFICATION", "Measure unit", "TOTAL", "0–9", "10–19", "20–29", "30–39", "40–49", "50–59", "60–69", "70–79", "80 +"], ["I.", "TOTAL", "TOTAL", "TOTAL", "TOTAL", "person", "156", "21", "38", "17", "17", "22", "15", "10", "10", "6"], ["I.", "—", "of which in", "of which in", "of which in", "%", "100", "13.5", "24.4", "10.9", "10.9", "14.1", "9.6", "6.4", "6.4", "3.8"], ["I.", "1.", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX", "BY SEX"], ["I.", "1.", "A.", "Males", "Males", "person", "74", "13", "16", "10", "8", "10", "9", "4", "3", "1"], ["I.", "1.", "A.", "—", "of which in", "%", "47.4", "8.3", "10.3", "6.4", "5.1", "6.4", "5.8", "2.6", "1.9", "0.6"], ["I.", "1.", "B.", "Females", "Females", "person", "82", "8", "22", "7", "9", "12", "6", "6", "7", "5"], ["I.", "1.", "B.", "—", "of which in", "%", "52.6", "5.1", "14.1", "4.5", "5.8", "7.7", "3.8", "3.8", "4.5", "3.2"]]}, "question": "What is the total number of males in the 20-29 age group and the 30-39 age group?", "answer": "18"}
{"id": "ef758cb602e41211846652763d99176e", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["rank", "nation", "gold", "silver", "bronze", "total"], "data": [["1", "australia", "3", "3", "5", "11"], ["2", "russia", "3", "3", "2", "8"], ["3", "italy", "3", "1", "2", "6"], ["4", "united states", "2", "0", "0", "2"], ["5", "germany", "1", "2", "3", "6"], ["6", "netherlands", "1", "2", "0", "3"], ["7", "belgium", "1", "0", "0", "1"], ["7", "spain", "1", "0", "0", "1"], ["7", "lithuania", "1", "0", "0", "1"], ["7", "norway", "1", "0", "0", "1"], ["7", "slovenia", "1", "0", "0", "1"], ["12", "france", "0", "2", "2", "4"], ["13", "denmark", "0", "1", "1", "2"], ["13", "portugal", "0", "1", "1", "2"], ["15", "hungary", "0", "1", "0", "1"], ["15", "switzerland", "0", "1", "0", "1"], ["15", "ukraine", "0", "1", "0", "1"], ["18", "new zealand", "0", "0", "1", "1"], ["18", "south africa", "0", "0", "1", "1"]]}, "question": "Which nation has a higher total medal count, Australia or Russia?", "answer": "Australia"}
{"id": "2f5bd1470c21eed07d5d123b6aaa1c04", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["language", "sorata municipality", "guanay municipality", "tacacoma municipality", "quiabaya municipality", "combaya municipality", "tipuani municipality", "mapiri municipality", "teoponte municipality"], "data": [["quechua", "363.0", "1.653", "1.058", "33.0", "20.0", "1.587", "3.649", "756.0"], ["aymara", "16.029", "3.405", "4.389", "2.269", "2.522", "2.534", "1.767", "2.837"], ["guaranã­", "7.0", "5.0", "1.0", "0.0", "0.0", "20.0", "6.0", "6.0"], ["another native", "8.0", "94.0", "17.0", "2.0", "1.0", "18.0", "7.0", "22.0"], ["spanish", "11.223", "10.064", "4.321", "1.391", "1.214", "8.594", "8.567", "6.211"], ["foreign", "70.0", "86.0", "6.0", "6.0", "1.0", "61.0", "17.0", "33.0"], ["only native", "6.68", "737.0", "1.599", "1.023", "1.363", "190.0", "363.0", "472.0"], ["native and spanish", "9.54", "4.123", "3.389", "1.256", "1.162", "3.499", "4.653", "2.925"]]}, "question": "Which municipality has the highest number of people speaking foreign, and how is the difference compared to the municipality with the lowest number of people speaking foreign?", "answer": "guanay municipality, 85"}
{"id": "72b56e932806834c1fa4b6e1287b7997", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["component 1", "bp comp 1 (˚c)", "component 2", "bp comp 2 (˚c)", "bp azeo (˚c)", "% wt comp 1", "% wt comp 2"], "data": [["acetaldehyde", "21.0", "diethyl ether", "34.6", "20.5", "76.0", "24.0"], ["acetaldehyde", "21.0", "n - butane", "- 0.5", "- 7.0", "16.0", "84.0"], ["acetamide", "222.0", "benzaldehyde", "179.5", "178.6", "6.5", "93.5"], ["acetamide", "222.0", "nitrobenzene", "210.9", "202.0", "24.0", "76.0"], ["acetamide", "222.0", "o - xylene", "144.1", "142.6", "11.0", "89.0"], ["acetonitrile", "82.0", "ethyl acetate", "77.15", "74.8", "23.0", "77.0"], ["acetonitrile", "82.0", "toluene", "110.6", "81.1", "25.0", "75.0"], ["acetylene", "- 86.6", "ethane", "- 88.3", "- 94.5", "40.7", "59.3"], ["aniline", "184.4", "o - cresol", "191.5", "191.3", "8.0", "92.0"], ["carbon disulfide", "46.2", "diethyl ether", "34.6", "34.4", "1.0", "99.0"], ["carbon disulfide", "46.2", "1 , 1 - dichloroethane", "57.2", "46.0", "94.0", "6.0"], ["carbon disulfide", "46.2", "methyl ethyl ketone", "79.6", "45.9", "84.7", "15.3"], ["carbon disulfide", "46.2", "ethyl acetate", "77.1", "46.1", "97.0", "3.0"], ["carbon disulfide", "46.2", "methyl acetate", "57.0", "40.2", "73.0", "27.0"], ["chloroform", "61.2", "methyl ethyl ketone", "79.6", "79.9", "17.0", "83.0"], ["chloroform", "61.2", "n - hexane", "68.7", "60.0", "72.0", "28.0"], ["carbon tetrachloride", "76.8", "methyl ethyl ketone", "79.9", "73.8", "71.0", "29.0"], ["carbon tetrachloride", "76.8", "ethylene dichloride", "84.0", "75.3", "78.0", "22.0"], ["carbon tetrachloride", "76.8", "ethyl acetate", "77.1", "74.8", "57.0", "43.0"], ["cyclohexane", "81.4", "ethyl acetate", "77.15", "72.8", "46.0", "54.0"], ["cyclohexane", "81.4", "ethyl nitrate", "88.7", "74.5", "64.0", "36.0"], ["diethyl ether", "34.6", "methyl formate", "31.50", "28.2", "44.0", "56.0"], ["diethyl ether", "34.6", "methylene chloride", "40", "40.8", "30.0", "70.0"], ["nitromethane", "101.0", "toluene", "110.8", "96.5", "55.0", "45.0"], ["tetrahydrofuran", "65.6", "chloroform", "61.2", "72.5", "34.5", "65.5"], ["tetrahydrofuran", "65.6", "n - hexane", "69", "63.0", "46.5", "53.5"], ["toluene", "110.63", "pyridine", "115.3", "110.2", "78.0", "22.0"], ["propylene glycol", "188.2", "aniline", "184.4", "179.5", "43.0", "57.0"], ["propylene glycol", "188.2", "o - xylene", "144.4", "135.8", "10.0", "90.0"], ["propylene glycol", "188.2", "toluene", "110.6", "110.5", "1.5", "98.5"]]}, "question": "What is the difference in boiling points (in ˚C) between the component with the highest 'bp comp 1 (˚c)' value and the component with the lowest 'bp comp 1 (˚c)' value?", "answer": "308.6"}
{"id": "45b24b0e99ab185c00da6b0361acb5e2", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["contestant", "starting weight (kg)", "final weight (kg)", "weight lost (kg)", "percentage lost", "position (out of eliminated contestants)"], "data": [["rick", "172.6", "97.2", "75.4", "43.68%", "1st"], ["david", "165.6", "99.2", "66.4", "40.10%", "2nd"], ["teneale", "97.4", "58.8", "38.6", "39.63%", "3rd"], ["phil", "146.9", "93.0", "53.9", "36.69%", "4th"], ["jarna", "118.8", "75.5", "43.3", "36.45%", "5th"], ["elise", "104.6", "66.7", "37.9", "36.23%", "6th"], ["jenni", "130.6", "84.3", "46.3", "35.45%", "7th"], ["phoebe", "116.0", "76.9", "39.1", "33.71%", "8th"], ["caitlin", "179.4", "124.8", "54.6", "30.43%", "9th"], ["geoff", "161.6", "117.8", "43.8", "27.10%", "10th"], ["daina", "105.2", "77.8", "27.4", "26.05%", "11th"], ["chris", "128.9", "104.2", "24.7", "19.16%", "12th"], ["allan", "155.8", "131.5", "24.3", "15.60%", "13th"]]}, "question": "What is the correlation between the 'starting weight' and 'percentage of weight lost' in the dataset? Provide the correlation coefficient as evidence.", "answer": "No correlation, -0.03"}
{"id": "3d4a4379fab0e72179a4a20199c27a18", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["member state", "population in millions", "population % of eu", "area km 2", "area % of eu", "pop density people / km 2"], "data": [["european union", "494.8", "100%", "4422773", "100%", "112.0"], ["austria", "8.3", "1.7%", "83858", "1.9%", "99.0"], ["belgium", "10.5", "2.1%", "30510", "0.7%", "344.0"], ["bulgaria", "7.7", "1.6%", "110912", "2.5%", "70.0"], ["croatia", "4.3", "0.9%", "56594", "1.3%", "75.8"], ["cyprus", "0.8", "0.2%", "9250", "0.2%", "84.0"], ["czech republic", "10.3", "2.1%", "78866", "1.8%", "131.0"], ["denmark", "5.4", "1.1%", "43094", "1.0%", "126.0"], ["estonia", "1.4", "0.3%", "45226", "1.0%", "29.0"], ["finland", "5.3", "1.1%", "337030", "7.6%", "16.0"], ["france", "65.03", "13.%", "643548", "14.6%", "111.0"], ["germany", "80.4", "16.6%", "357021", "8.1%", "225.0"], ["greece", "11.1", "2.2%", "131940", "3.0%", "84.0"], ["hungary", "10.1", "2.0%", "93030", "2.1%", "108.0"], ["ireland", "4.2", "0.8%", "70280", "1.6%", "60.0"], ["italy", "58.8", "11.9%", "301320", "6.8%", "195.0"], ["latvia", "2.3", "0.5%", "64589", "1.5%", "35.0"], ["lithuania", "3.4", "0.7%", "65200", "1.5%", "52.0"], ["luxembourg", "0.5", "0.1%", "2586", "0.1%", "181.0"], ["malta", "0.4", "0.1%", "316", "0.0%", "1261.0"], ["netherlands", "16.4", "3.3%", "41526", "0.9%", "394.0"], ["poland", "38.1", "7.7%", "312685", "7.1%", "122.0"], ["portugal", "10.6", "2.1%", "92931", "2.1%", "114.0"], ["romania", "21.6", "4.4%", "238391", "5.4%", "91.0"], ["spain", "44.7", "9.0%", "504782", "11.4%", "87.0"], ["slovakia", "5.4", "1.1%", "48845", "1.1%", "111.0"], ["slovenia", "2.0", "0.4%", "20253", "0.5%", "99.0"], ["sweden", "9.1", "1.8%", "449964", "10.2%", "20.0"]]}, "question": "Which factors in the table, such as 'area km 2', 'area % of eu', or 'pop density people / km 2', significantly influence the 'population % of eu' for each member state? If none have an effect, please reply 'no clear impact'.", "answer": "area km 2, area % of eu"}
{"id": "3bba76d2d41024fde7d3061dc3e4c230", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Year", "Team", "GP", "Att", "Yds", "Avg", "Long", "Rush TD", "Rec", "Yds.1", "Avg.1", "Long.1", "Rec TD"], "data": [["1981", "San Diego Chargers", "14", "109", "525", "4.8", "28", "3", "46", "329", "7.2", "29", "3"], ["1982", "San Digeo Chargers", "9", "87", "430", "4.9", "48", "6", "13", "66", "5.1", "12", "0"], ["1983", "San Diego Chargers", "15", "127", "516", "4.1", "61", "3", "25", "215", "8.6", "36", "0"], ["1984", "Cincinnati Bengals", "15", "103", "396", "3.8", "33", "2", "34", "268", "7.9", "27", "2"], ["1985", "Cincinnati Bengals", "16", "192", "929", "4.8", "39", "7", "55", "576", "10.5", "57", "5"], ["1986", "Cincinnati Bengals", "16", "205", "1,087", "5.3", "56", "5", "54", "686", "12.7", "54", "4"], ["1987", "Cincinnati Bengals", "9", "94", "280", "3.1", "18", "1", "22", "272", "12.4", "46", "2"], ["1988", "Cincinnati Bengals", "15", "182", "931", "5.1", "51", "8", "29", "287", "9.9", "28", "6"], ["1989", "Cincinnati Bengals", "16", "221", "1,239", "5.6", "65", "7", "37", "306", "8.3", "25", "2"], ["1990", "Cincinnati Bengals", "16", "195", "1,004", "5.1", "56", "5", "26", "269", "10.3", "35", "4"], ["1991", "Cincinnati Bengals", "15", "152", "571", "3.8", "25", "2", "40", "348", "8.7", "40", "2"], ["1992", "Tampa Bay Buccaneers", "2", "5", "6", "1.2", "4", "0", "0", "0", "0.0", "0", "0"], ["1992", "Cleveland Browns", "4", "13", "38", "2.9", "13", "0", "2", "-1", "-0.5", "4", "0"], ["Career Totals", null, "162", "1,685", "7,962", "4.7", "65", "49", "383", "3,621", "9.5", "57", "30"]]}, "chart_type": "line", "question": "Please help me draw a line chart showing the trend in the athlete's number of attacks", "answer": "y_references = [[ 109, 87, 127, 103, 192, 205, 94, 182, 221, 195, 152, 5, 13 ]]"}
{"id": "3ca51f974a30120a84a22b6e72b818ba", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["draw", "singer", "song", "points", "place"], "data": [["1", "manjola nallbani", "kjo botë merr frymë nga dashuria", "27", "7"], ["2", "produkt 28", "30 sekonda", "3", "15"], ["3", "eneida tarifa", "e para letër", "11", "10"], ["4", "mariza ikonomi", "mall i tretur", "20", "9"], ["5", "greta koçi", "natën të kërkova", "35", "6"], ["6", "flaka krelani & doruntina disha", "jeta kërkon dashuri", "57", "2"], ["7", "mira konçi & redon makashi", "nën një qiell", "37", "5"], ["8", "kthjellu", "dhoma", "9", "11"], ["9", "kozma dushi", "tatuazh në kujtesë", "1", "16"], ["10", "devis xherahu", "endacaku", "0", "17"], ["11", "teuta kurti", "qyteti i dashurisë", "3", "14"], ["12", "samanta karavello", "pse u harrua dashuria", "23", "8"], ["13", "juliana pasha", "një qiell të ri", "54", "3"], ["14", "agim poshka", "kujt i them të dua", "8", "12"], ["15", "jonida maliqi", "s'ka fajtor në dashuri", "36", "4"], ["16", "olta boka", "zemrën e lamë peng", "67", "1"], ["17", "rosela gjylbegu", "po lind një yll", "8", "13"]]}, "question": "What is the average number of points received by the singers in this competition?", "answer": "23.47"}
{"id": "5e944dc7f377ad045ac3d686bda63f5a", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["School", "Location", "Outright Titles", "Shared Titles", "Runners-Up", "Total Finals", "Last Title", "Last Final"], "data": [["Methodist College Belfast", "Belfast", "35", "2", "25", "62", "2014.0", "2014"], ["Royal Belfast Academical Institution", "Belfast", "29", "4", "21", "54", "2007.0", "2013"], ["Campbell College", "Belfast", "23", "4", "12", "39", "2011.0", "2011"], ["Coleraine Academical Institution", "Coleraine", "9", "0", "24", "33", "1992.0", "1998"], ["The Royal School, Armagh", "Armagh", "9", "0", "3", "12", "2004.0", "2004"], ["Portora Royal School", "Enniskillen", "6", "1", "5", "12", "1942.0", "1942"], ["Bangor Grammar School", "Bangor", "5", "0", "4", "9", "1988.0", "1995"], ["Ballymena Academy", "Ballymena", "3", "0", "6", "9", "2010.0", "2010"], ["Rainey Endowed School", "Magherafelt", "2", "1", "2", "5", "1982.0", "1982"], ["Foyle College", "Londonderry", "2", "0", "4", "6", "1915.0", "1915"], ["Belfast Royal Academy", "Belfast", "1", "3", "5", "9", "1997.0", "2010"], ["Regent House Grammar School", "Newtownards", "1", "1", "2", "4", "1996.0", "2008"], ["Royal School Dungannon", "Dungannon", "1", "0", "4", "5", "1907.0", "1975"], ["Annadale Grammar School (now Wellington College)", "Belfast", "1", "0", "1", "2", "1958.0", "1978"], ["Ballyclare High School", "Ballyclare", "1", "0", "1", "2", "1973.0", "2012"], ["Belfast Boys' Model School", "Belfast", "1", "0", "0", "1", "1971.0", "1971"], ["Grosvenor High School", "Belfast", "1", "0", "0", "1", "1983.0", "1983"], ["Wallace High School", "Lisburn", "0", "0", "4", "4", null, "2007"], ["Derry Academy", "Derry", "0", "0", "2", "2", null, "1896"], ["Dalriada School", "Ballymoney", "0", "0", "1", "1", null, "1993"], ["Galway Grammar School", "Galway", "0", "0", "1", "1", null, "1887"], ["Lurgan College", "Lurgan", "0", "0", "1", "1", null, "1934"], ["Omagh Academy", "Omagh", "0", "0", "1", "1", null, "1985"], ["Sullivan Upper School", "Holywood", "0", "0", "1", "1", null, "2014"]]}, "question": "According to the table, which school in Belfast has won the most outright titles?", "answer": "Methodist College Belfast"}
{"id": "ef1ef44158bf1967bb2671216a01b4a9", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["year", "number of tropical storms", "number of hurricanes", "number of major hurricanes", "deaths", "strongest storm"], "data": [["1860", "1", "5", "1", "60 +", "one"], ["1861", "2", "6", "0", "22 +", "one and three"], ["1862", "3", "3", "0", "3", "two and three"], ["1863", "4", "5", "0", "90", "one , two , three & four"], ["1864", "2", "3", "0", "none", "one , three & five"], ["1865", "4", "3", "0", "326", "four & seven"], ["1866", "1", "5", "1", "383", "six"], ["1867", "2", "6", "0", "811", "'san narciso'"], ["1868", "1", "3", "0", "2", "one , two & four"]]}, "question": "In the context of tropical storms, the Hurricane Severity Index (HSI) is calculated as the sum of the number of hurricanes and major hurricanes, divided by the total number of tropical storms, indicating the severity of the storm season. What year had the highest Hurricane Severity Index?", "answer": "1860, 1866"}
{"id": "86f27f6eb9945a8e587457aca56b0309", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["Club", "Season", "League", "League", "League", "FA Cup", "FA Cup", "League Cup", "League Cup", "Other", "Other", "Total", "Total"], "data": [["Club", "Season", "Division", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals"], ["Stoke City", "1998–99", "Second Division", "4", "0", "0", "0", "0", "0", "1", "0", "5", "0"], ["Stoke City", "1999–2000", "Second Division", "42", "5", "1", "0", "3", "0", "9", "3", "55", "8"], ["Stoke City", "2000–01", "Second Division", "44", "8", "1", "0", "5", "2", "4", "0", "54", "10"], ["Stoke City", "2001–02", "Second Division", "43", "2", "4", "0", "0", "0", "3", "1", "50", "3"], ["Stoke City", "2002–03", "First Division", "43", "0", "3", "0", "1", "0", "0", "0", "47", "0"], ["Stoke City", "Total", "Total", "176", "16", "9", "0", "9", "2", "17", "4", "211", "22"], ["West Bromwich Albion", "2003–04", "First Division", "30", "0", "1", "0", "5", "0", "0", "0", "36", "0"], ["West Bromwich Albion", "2004–05", "Premier League", "0", "0", "1", "0", "1", "0", "0", "0", "2", "0"], ["West Bromwich Albion", "Total", "Total", "30", "0", "2", "0", "6", "0", "0", "0", "38", "0"], ["Burnley", "2004–05", "Championship", "21", "2", "1", "0", "1", "0", "0", "0", "23", "2"], ["Burnley", "2005–06", "Championship", "45", "3", "1", "0", "3", "0", "0", "0", "49", "3"], ["Burnley", "2006–07", "Championship", "42", "3", "1", "0", "1", "0", "0", "0", "44", "3"], ["Burnley", "2007–08", "Championship", "29", "3", "1", "0", "3", "0", "0", "0", "33", "3"], ["Burnley", "Total", "Total", "137", "11", "4", "0", "8", "0", "0", "0", "149", "11"], ["Sheffield Wednesday", "2008–09", "Championship", "41", "0", "1", "0", "1", "0", "0", "0", "43", "0"], ["Sheffield Wednesday", "2009–10", "Championship", "44", "3", "1", "0", "1", "0", "0", "0", "46", "3"], ["Sheffield Wednesday", "2010–11", "League One", "36", "2", "4", "0", "2", "0", "3", "2", "45", "4"], ["Sheffield Wednesday", "2011–12", "League One", "18", "1", "1", "0", "2", "0", "1", "0", "22", "1"], ["Sheffield Wednesday", "Total", "Total", "139", "6", "7", "0", "6", "0", "4", "2", "156", "8"], ["Career Total", "Career Total", "Career Total", "482", "33", "22", "0", "29", "2", "21", "6", "554", "41"]]}, "question": "Goal-to-Game Ratio is defined as a player's total goals scored divided by the total number of games played. According to this definition of Goal-to-Game Ratio, which club has the highest goal-to-game ratio in their career?", "answer": "Stoke City"}
{"id": "bbda9a858ef116f491529e0fe820e1a9", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["specimen weight / size", "calculated activity ( bq )", "calculated activity ( ci )", "estimated activity gr (api)", "estimated exposure ( mrem ) / hr"], "data": [["1000 g / 8.79 cm", "183355", "4.9610 6", "8449.31", "2.78"], ["100 g / 4.08 cm", "18336", "4.9610 7", "844.93", "0.28"], ["10 g / 1.89 cm", "1834", "4.9610 8", "84.49", "0.03"], ["1 g / 8.79 mm", "183", "4.9610 9", "8.45", "0.0"], ["0.1 g / 4.08 mm", "18", "4.9610 10", "0.84", "0.0"], ["0.01 g / 1.89 mm", "2", "4.9610 11", "0.08", "0.0"]]}, "chart_type": "scatter", "question": "Can you create a scatter plot to display the relationship between specimen weight/size and estimated exposure (mrem/hr)?", "answer": "y_references = [[2.78, 0.28, 0.03, 0.0, 0.0, 0.0]]"}
{"id": "3e91d53f7b003e5d9ec55fdb87ee40fb", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "total support and revenue", "total expenses", "increase in net assets", "net assets at end of year"], "data": [["2003 / 2004", "80129", "23463", "56666", "56666"], ["2004 / 2005", "379088", "177670", "211418", "268084"], ["2005 / 2006", "1508039", "791907", "736132", "1004216"], ["2006 / 2007", "2734909", "2077843", "654066", "1658282"], ["2007 / 2008", "5032981", "3540724", "3519886", "5178168"], ["2008 / 2009", "8658006", "5617236", "3053599", "8231767"], ["2009 / 2010", "17979312", "10266793", "6310964", "14542731"], ["2010 / 2011", "24785092", "17889794", "9649413", "24192144"], ["2011 / 2012", "38479665", "29260652", "10736914", "34929058"]]}, "question": "What is the projected net asset value at the end of 2012/2013 based on the historical trend?", "answer": "30416145.92"}
{"id": "54131542c72ca53ecd13c8e0753afc7b", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["rank", "team name", "basic elements", "tumbling", "stunts", "tosses / pyramids", "deductions", "total"], "data": [["1", "school of saint anthony ssa seagulls", "61.5", "66.5", "67.5", "69.5", "(13)", "252.0"], ["2", "school of the holy spirit shs pep squad", "64.5", "63.0", "66.0", "64.5", "(15)", "243.0"], ["5", "pcc pep squad", "55.0", "49.0", "65.0", "64.0", "(26)", "207.0"], ["6", "assumption college ac hardcourt", "59.0", "53.0", "62.0", "48.5", "(37)", "185.5"], ["8", "the cmic fighting vanguards", "47.0", "36.5", "57.5", "56.5", "(35)", "162.5"], ["9", "de la salle zobel dlsz pep squad and cheerdancers", "46.5", "44.5", "54.0", "44.0", "(27)", "162.0"]]}, "question": "What is the mean score for the 'tumbling' category across all teams?", "answer": "51"}
{"id": "01a470ad358cd77b8f7c3bce8f34e501", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["year", "design", "issue", "artist", "mintage", "issue price"], "data": [["2003", "niagara falls", "hologram", "gary corcoran", "29967", "79.95"], ["2003", "rocky mountains", "colorized", "josé osio", "28793", "69.95"], ["2004", "iceberg", "hologram", "josé osio", "24879", "69.95"], ["2004", "northern lights", "double image hologram", "gary corcoran", "34135", "79.95"], ["2004", "hopewell rocks", "selectively gold plated", "josé osio", "16918", "69.95"], ["2005", "diamonds", "double image hologram", "josé osio", "35000", "69.95"]]}, "question": "Could you describe the main features of the table, explain the role of each column, and highlight any initial observations or trends that emerge from the data?", "answer": "The table presents data on collectible items issued from 2003 to 2005, detailing their year of issue, design, special features, artist, production quantity, and issue price. It highlights the recurring involvement of artist José Osio and shows a variation in mintage and pricing based on the complexity of the design features."}
{"id": "80ec47226c5b0cbb341420c031fe2f81", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["no in series", "no in season", "title", "directed by", "written by", "original air date", "prod no", "viewers (millions)"], "data": [["25", "1", "human traffic", "james whitmore , jr", "shane brennan", "september 21 , 2010", "201", "15.76"], ["26", "2", "black widow", "kate woods", "dave kalstein", "september 21 , 2010", "202", "13.6"], ["27", "3", "borderline", "terrence o'hara", "r scott gemmill", "september 28 , 2010", "203", "16.51"], ["28", "4", "special delivery", "tony wharmby", "gil grant", "october 5 , 2010", "204", "16.15"], ["29", "5", "little angels", "steven depaul", "frank military", "october 12 , 2010", "205", "16.05"], ["30", "6", "standoff", "dennis smith", "joseph c wilson", "october 19 , 2010", "206", "16.0"], ["31", "7", "anonymous", "norberto barba", "christina m kim", "october 26 , 2010", "207", "15.99"], ["32", "8", "bounty", "felix alcala", "dave kalstein", "november 9 , 2010", "208", "15.61"], ["33", "9", "absolution", "steven depaul", "r scott gemmill", "november 16 , 2010", "209", "15.81"], ["34", "10", "deliverance", "tony wharmby", "frank military and shane brennan", "november 23 , 2010", "210", "14.96"], ["35", "11", "disorder", "jonathan frakes", "gil grant and david kalstien", "december 14 , 2010", "211", "16.82"], ["36", "12", "overwatch", "karen gaviola", "lindsay jewett sturman", "january 11 , 2011", "212", "18.13"], ["37", "13", "archangel", "tony wharmby", "r scott gemmill and shane brennan", "january 18 , 2011", "213", "17.29"], ["38", "14", "lockup", "jan eliasberg", "christina m kim and frank military", "february 1 , 2011", "214", "17.7"], ["39", "15", "tin soldiers", "terrence o'hara", "r scott gemmill", "february 8 , 2011", "215", "17.16"], ["40", "16", "empty quiver", "james whitmore", "dave kalstein", "february 15 , 2011", "216", "16.8"], ["41", "17", "personal", "kate woods", "joseph c wilson", "february 22 , 2011", "217", "18.69"], ["42", "18", "harm 's way", "tony wharmby", "shane brennan", "march 1 , 2011", "218", "15.67"], ["43", "19", "enemy within", "steven depaul", "lindsay jewett sturman", "march 22 , 2011", "219", "16.56"], ["44", "20", "the job", "terrence o'hara", "frank military and christina m kim", "march 29 , 2011", "220", "15.34"], ["45", "21", "rocket man", "dennis smith", "roger director", "april 12 , 2011", "221", "15.46"], ["46", "22", "plan b", "james whitmore , jr", "dave kalstein and joseph c wilson", "may 3 , 2011", "222", "14.16"], ["47", "23", "imposters", "john p kousakis", "r scott gemmill", "may 10 , 2011", "223", "14.74"]]}, "question": "What is the average number of viewers (in millions) for the first 5 episodes of the series?", "answer": "15.61"}
{"id": "7e2005500f8d07f6945773c1f893a2ec", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["Crime", "Reported offenses", "Killeen rate", "Texas rate", "U.S. rate"], "data": [["Murder", "10", "8.6", "5.6", "5.6"], ["Rape", "66", "56.9", "32.9", "29.4"], ["Robbery", "216", "186.4", "155.2", "154.0"], ["Aggravated assault", "593", "511.6", "314.4", "281.6"], ["Violent crime", "885", "763.5", "508.2", "470.6"], ["Burglary", "1,711", "1,476.2", "946.5", "743.4"], ["Larceny – theft", "2,877", "2,482.2", "2,688.9", "2,200.1"], ["Motor vehicle theft", "169", "145.8", "351.1", "330.5"], ["Non-violent crime", "4,757", "4,104.2", "3,986.6", "3,274.0"]]}, "question": "Could you describe the main components of the crime statistics table, and highlight any notable differences or trends?", "answer": "The table displays crime statistics for Killeen, Texas, in comparison to the U.S., encompassing various categories such as Murder, Rape, Robbery, and others. It details the number of reported offenses and the rates per 100,000 inhabitants. Notably, Killeen exhibits higher crime rates across most categories compared to both Texas and the U.S. averages, with a notable exception in Motor vehicle theft, where Killeen's rate is lower."}
{"id": "07ff0047fb0924e84ec62261007e0902", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["rating", "share", "rating / share (18 - 49)", "viewers (millions)", "rank (overall)"], "data": [["5.4", "9", "2.6 / 7", "9.2", "27"], ["4.4", "7", "2.6 / 6", "7.81", "34"], ["6.6", "11", "3.0 / 8", "10.6", "18"], ["6.1", "10", "3.1 / 8", "10.13", "28"], ["5.9", "10", "3.1 / 8", "10.33", "26"], ["7.2", "11", "3.2 / 8", "12.42", "15"], ["7.1", "11", "3.8 / 10", "11.97", "12"], ["6.2", "10", "2.9 / 8", "10.58", "18"], ["6.1", "10", "n / a", "10.31", "20"], ["6.0", "10", "n / a", "10.27", "17"], ["6.8", "9", "2.0 / 7", "10.84", "20"], ["7.6", "10", "n / a", "12.49", "19"]]}, "question": "If the viewership of a particular show increases by 10% from 9.2 million, what would be the new viewership in millions?", "answer": "10.12"}
{"id": "17e82bd1b98d9b57f10c9dfa4b93ead8", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["frequency (hz)", "r (î / km)", "l (mh / km)", "g (î¼s / km)", "c (nf / km)"], "data": [["1", "172.24", "0.6129", "0.0", "51.57"], ["1k", "172.28", "0.6125", "0.072", "51.57"], ["10k", "172.7", "0.6099", "0.531", "51.57"], ["100k", "191.63", "0.5807", "3.327", "51.57"], ["1 m", "463.59", "0.5062", "29.111", "51.57"], ["2 m", "643.14", "0.4862", "53.205", "51.57"]]}, "question": "What is the percentage change in the value of 'l (mh / km)' when the 'frequency (hz)' increases from 1 to 100k, assuming the ratio of 'g (î¼s / km)' to 'c (nf / km)' remains constant?", "answer": "5.25"}
{"id": "3f6993cc9f6540e04eaba1d69d6d69b6", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "matches", "wins", "losses", "no result", "tied", "success rate"], "data": [["2008", "16", "9", "7", "0", "0", "56.25%"], ["2009", "15", "8", "6", "1", "0", "53.33%"], ["2010", "16", "9", "7", "0", "0", "56.25%"], ["2011", "16", "11", "5", "0", "0", "68.75%"], ["2012", "19", "19", "11", "8", "0", "52.63%"], ["2013", "18", "12", "6", "0", "0", "66.67%"], ["total", "99", "59", "39", "1", "0", "60.2%"]]}, "question": "In which year did the team experience the largest increase in the number of wins compared to the previous year?", "answer": "2012"}
{"id": "f3e8910d05ad5055c1c42a079952b8da", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["incident no", "date", "place", "killed", "injured"], "data": [["1", "february", "tumkur , karnataka", "6", "0"], ["2", "august", "dantewada , chattisgarh", "350", "0"], ["3", "17 august", "andhra pradesh", "0", "0"], ["4", "11 november", "giridih , jharkhand", "0", "0"], ["5", "11 november", "giridih , jharkhand", "5", "16"], ["6", "13 november", "jehanabad , bihar", "4", "5"], ["7", "30 december", "dantewada , chhattisgarh", "2", "0"], ["total", "total", "total", "367", "21"]]}, "question": "Which place has the highest number of people killed?", "answer": "dantewada , chattisgarh"}
{"id": "7a156d05e2c0428c864472f280530e0e", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["draw", "artist", "song", "points", "place"], "data": [["1", "niamh kavanagh", "in your eyes", "118", "1"], ["2", "suzanne bushnell", "long gone", "54", "7"], ["3", "patricia roe", "if you changed your mind", "75", "3"], ["4", "róisín ní haodha", "mo mhúirnín óg", "34", "8"], ["5", "champ", "2nd time around", "79", "2"], ["6", "off the record", "hold out", "61", "6"], ["7", "dav mcnamara", "stay", "67", "4"], ["8", "perfect timing", "why aren't we talking anyway", "62", "5"]]}, "question": "What is the difference in points between the artist with the highest points and the average points of the top 3 artists?", "answer": "27.33"}
{"id": "5269641b7bf357e871bba95905bcca7b", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["draw", "singer", "song", "points", "place"], "data": [["1", "manjola nallbani", "kjo botë merr frymë nga dashuria", "27", "7"], ["2", "produkt 28", "30 sekonda", "3", "15"], ["3", "eneida tarifa", "e para letër", "11", "10"], ["4", "mariza ikonomi", "mall i tretur", "20", "9"], ["5", "greta koçi", "natën të kërkova", "35", "6"], ["6", "flaka krelani & doruntina disha", "jeta kërkon dashuri", "57", "2"], ["7", "mira konçi & redon makashi", "nën një qiell", "37", "5"], ["8", "kthjellu", "dhoma", "9", "11"], ["9", "kozma dushi", "tatuazh në kujtesë", "1", "16"], ["10", "devis xherahu", "endacaku", "0", "17"], ["11", "teuta kurti", "qyteti i dashurisë", "3", "14"], ["12", "samanta karavello", "pse u harrua dashuria", "23", "8"], ["13", "juliana pasha", "një qiell të ri", "54", "3"], ["14", "agim poshka", "kujt i them të dua", "8", "12"], ["15", "jonida maliqi", "s'ka fajtor në dashuri", "36", "4"], ["16", "olta boka", "zemrën e lamë peng", "67", "1"], ["17", "rosela gjylbegu", "po lind një yll", "8", "13"]]}, "question": "How many more points did the singer with the highest 'points' score receive than the singer with the 5th highest 'points' score?", "answer": "31"}
{"id": "42761f0622ad3513894ab3472e8982bf", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["Season", "Episodes", "Season Premiere", "Season Finale"], "data": [["1", "20", "March 4, 2006", "May 13, 2006"], ["2", "52", "October 7, 2006", "July 16, 2007"], ["3", "44", "October 15, 2007", "June 2, 2008"], ["4", "48", "October 13, 2008", "May 11, 2009"], ["5", "40", "October 12, 2009", "June 14, 2010"], ["6", "20", "September 6, 2010", "December 6, 2010"], ["7", "8", "October 29, 2013", "December 17, 2013"]]}, "question": "What is the average number of episodes per season for seasons that have at least 40 episodes, and premiered between October and December?", "answer": "46"}
{"id": "fe23487e044cd65a27ea90fd0b13abb9", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], "data": [["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], ["oakdale rfc", "22", "2", "0", "614", "226", "88", "23", "13", "0", "97"], ["blaenavon rfc", "22", "1", "5", "444", "271", "61", "33", "5", "2", "73"], ["brynithel rfc", "22", "3", "4", "398", "292", "41", "24", "4", "1", "71"], ["caldicot rfc", "22", "0", "8", "500", "330", "69", "44", "8", "3", "67"], ["usk rfc", "22", "2", "8", "484", "431", "71", "58", "11", "1", "64"], ["hartridge rfc", "22", "1", "11", "424", "345", "52", "45", "5", "5", "52"], ["bettws rfc", "22", "3", "11", "476", "438", "59", "53", "6", "7", "51"], ["rtb (ebbw vale) rfc", "22", "3", "12", "317", "371", "38", "50", "5", "4", "43"], ["ynysddu rfc", "22", "1", "14", "315", "376", "35", "44", "3", "9", "42"], ["llanhilleth rfc", "22", "3", "13", "357", "475", "42", "61", "3", "4", "37"], ["trinant rfc", "22", "1", "15", "261", "487", "29", "65", "1", "4", "31"], ["pontllanfraith rfc", "22", "0", "21", "160", "708", "17", "102", "2", "1", "7"]]}, "question": "Which top3 factors in the table, such as 'played', 'drawn', 'lost', 'points for', 'points against', 'tries for', 'tries against', 'try bonus', and 'losing bonus', significantly contribute to the 'points' total for each club?", "answer": "lost, points for, points against"}
{"id": "85dfad6e90b2120415fcd9464cb2517c", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["military deaths", "civilian deaths", "total deaths (not including foreigners)", "military and / or civilian wounded", "total casualties"], "data": [["4000", "2400", "6373", "15000", "21400"], ["unknown", "unknown", "400 - 967", "900 - 1300", "13002267"], ["unknown", "unknown", "178", "1574 +", "1752 +"], ["unknown", "unknown", "567", "unknown", "unknown"], ["231", "none", "231", "899", "1130"], ["1", "0", "1", "10", "11"], ["776", "none", "776", "4517", "5293"], ["1424", "127", "1551", "2700", "4251 +"], ["100000", "50000", "150000", "500000", "650000"], ["unknown", "unknown", "unknown", "unknown", "unknown"], ["2656", "none", "2656", "9000", "11656"], ["675", "50", "725", "6500", "7225"], ["256", "90", "636", "1200", "1836"], ["60", "100", "160", "500", "660"], ["170", "99", "269", "400", "669"], ["332", "731", "1063", "8800", "9863"], ["0.1", "0.01", "0.11", "1", "1.11"], ["16", "7", "23", "19", "42"], ["121", "44", "165", "2067", "2237"], ["10 (4 by friendly fire )", "3", "13", "518", "531"], ["13", "33", "46", "312", "358 +"], ["1", "0", "1", "1", "2"]]}, "question": "What are the anomalies in the data that may indicate errors or unusual patterns?", "answer": "The two anomalies are row 9 with military, civilian, and total casualties all over 100,000, exceptionally higher than the typical thousands range, and row 14 with all these values under 1, strikingly lower than the usual tens or hundreds."}
{"id": "eb3b923b7d75d87f77af0ef35d41e189", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["metropolitan ring", "localities", "total", "jews and others 1", "thereof : jews", "arabs", "population density (per km square)", "annual population growth rate"], "data": [["core 2", "1", "264800", "237800", "214200", "27100", "3838.2", "0.0%"], ["inner ring 3", "30", "271200", "241700", "224500", "29500", "1046.8", "0.5%"], ["northern section", "3", "112400", "112300", "101900", "100", "5591.7", "- 0.2%"], ["eastern section", "16", "84000", "80100", "76000", "4000", "1014.9", "1.0%"], ["southern section", "11", "74800", "49300", "46700", "25500", "481.4", "1.0%"], ["outer ring 4", "98", "484900", "240100", "223000", "244900", "678.8", "1.8%"], ["northern section", "57", "362800", "147300", "134500", "215600", "948.1", "1.6%"], ["eastern section", "23", "82300", "64300", "60800", "18000", "534.5", "1.7%"], ["southern section", "18", "39800", "28500", "27800", "11300", "224.0", "3.7%"]]}, "question": "What is the correlation between the 'total population' and 'population density (per km square)' across different metropolitan rings? Provide the correlation coefficient as evidence.", "answer": "No correlation, -0.03"}
{"id": "983b4784553034f42c2522596fb40b67", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["sno", "power plant", "state", "commissioned capacity (mw)", "year of commission"], "data": [["1", "baira siul", "himachal pradesh", "180", "1981"], ["2", "loktak", "manipur", "105", "1983"], ["3", "salal - i", "jammu & kashmir", "345", "1987"], ["4", "tanakpur", "uttarakhand", "120", "1992"], ["5", "chamera - i", "himachal pradesh", "540", "1994"], ["6", "salal - ii", "jammu & kashmir", "345", "1996"], ["7", "uri - i", "jammu & kashmir", "480", "1997"], ["8", "rangit", "sikkim", "60", "1999"], ["9", "chamera - ii", "himachal pradesh", "300", "2004"], ["10", "indira sagar", "madhya pradesh", "1000", "2005"], ["11", "dhauliganga - i", "uttarakhand", "280", "2005"], ["12", "dul hasti", "jammu & kashmir", "390", "2007"], ["13", "omkareshwar", "madhya pradesh", "520", "2007"], ["14", "teesta - v", "sikkim", "510", "2008"], ["15", "sewa - ii", "jammu & kashmir", "120", "2010"], ["16", "chamera - iii", "himachal pradesh", "231", "2012"]]}, "question": "Can you provide a descriptive explanation of the table, including the main columns and some basic insights?**", "answer": "The table enumerates hydroelectric power plants in India, specifying their names, locations, commissioned capacities in megawatts, and commissioning years. It encompasses data on 16 plants distributed across multiple states, with capacities ranging from 60 MW to 1000 MW and commissioning years spanning from 1981 to 2012, reflecting the development of hydroelectric power in India over these years."}
{"id": "8cf0ed38375271dc4e7e1b2c750a206f", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["rank by average", "place", "couple", "total points", "number of dances", "average"], "data": [["1", "1", "brooke & derek", "433", "16", "27.1"], ["2", "2", "warren & kym", "397", "16", "24.8"], ["3", "3", "lance & lacey", "392", "16", "24.5"], ["4", "5", "maurice & cheryl", "252", "11", "22.9"], ["5", "4", "cody & julianne", "292", "13", "22.5"], ["6", "8", "toni b & alec", "134", "6", "22.3"], ["7", "6", "susan & tony d", "192", "9", "21.3"], ["8", "10", "misty & maksim", "63", "3", "21.0"], ["9", "12", "ted & inna", "37", "2", "18.5"], ["10", "11", "kim k & mark", "54", "3", "18.0"], ["11", "9", "rocco & karina", "89", "5", "17.8"], ["12", "7", "cloris & corky", "121", "7", "17.3"]]}, "question": "In the context of dance competitions, the \"Dance Efficiency\" metric is defined as the total points earned by a couple divided by the number of dances they performed. Based on this definition, which couple has the highest Dance Efficiency in this competition?", "answer": "brooke & derek"}
{"id": "f3896f2053fc99a564da0fda0eff4561", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["rank", "province", "population", "area", "density"], "data": [["1", "san juan", "232333", "3363.8", "69.07"], ["2", "la altagracia", "273210", "2998.4", "91.12"], ["3", "santiago", "963422", "2806.3", "343.31"], ["4", "azua", "214311", "2682.5", "79.89"], ["5", "monte plata", "185956", "2601.6", "71.48"], ["6", "la vega", "394205", "2292.5", "171.95"], ["7", "pedernales", "31587", "2080.5", "15.18"], ["8", "independencia", "52589", "2007.4", "26.2"], ["9", "monte cristi", "109607", "1885.8", "58.12"], ["10", "puerto plata", "321597", "1805.6", "178.11"], ["11", "el seibo", "87680", "1788.4", "49.03"], ["12", "barahona", "187105", "1660.2", "112.7"], ["13", "duarte", "289574", "1649.5", "175.55"], ["14", "elías piña", "63029", "1395.5", "45.17"], ["15", "hato mayor", "85017", "1319.3", "64.44"], ["16", "santo domingo", "2374370", "1302.2", "1823.35"], ["17", "baoruco", "97313", "1284.9", "75.74"], ["18", "san pedro de macorís", "290458", "1254.3", "231.57"], ["19", "san cristóbal", "569930", "1240.6", "459.4"], ["20", "maría trinidad sánchez", "140925", "1206.5", "116.8"], ["21", "sánchez ramírez", "151392", "1185.8", "127.67"], ["22", "santiago rodríguez", "57476", "1147.5", "50.09"], ["23", "dajabón", "63955", "1021.3", "62.62"], ["24", "monseñor nouel", "165224", "992.0", "166.56"], ["25", "samaná", "101494", "862.8", "117.63"], ["26", "san josé de ocoa", "59544", "853.4", "69.77"], ["27", "espaillat", "231938", "843.0", "275.13"], ["28", "valverde", "163030", "823.0", "198.09"], ["29", "peravia", "184344", "785.2", "234.77"], ["30", "la romana", "245433", "652.1", "376.37"], ["31", "hermanas mirabal", "92193", "427.4", "215.71"], ["32", "distrito nacional", "965040", "91.6", "10535.37"]]}, "question": "What is the correlation between the 'population' and 'density' columns in the table? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.43"}
{"id": "f613a13c80d7b38191513c4bbbb12399", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["STAPLE:", "Maize / Corn", "Rice", "Wheat", "Potato", "Cassava", "Soybean (Green)", "Sweet potato", "Sorghum", "Yam", "Plantain"], "data": [["Component (per 100g portion)", "Amount", "Amount", "Amount", "Amount", "Amount", "Amount", "Amount", "Amount", "Amount", "Amount"], ["Water (g)", "10", "12", "13", "79", "60", "68", "77", "9", "70", "65"], ["Energy (kJ)", "1528", "1528", "1369", "322", "670", "615", "360", "1419", "494", "511"], ["Protein (g)", "9.4", "7.1", "12.6", "2.0", "1.4", "13.0", "1.6", "11.3", "1.5", "1.3"], ["Fat (g)", "4.74", "0.66", "1.54", "0.09", "0.28", "6.8", "0.05", "3.3", "0.17", "0.37"], ["Carbohydrates (g)", "74", "80", "71", "17", "38", "11", "20", "75", "28", "32"], ["Fiber (g)", "7.3", "1.3", "12.2", "2.2", "1.8", "4.2", "3", "6.3", "4.1", "2.3"], ["Sugar (g)", "0.64", "0.12", "0.41", "0.78", "1.7", "0", "4.18", "0", "0.5", "15"], ["Calcium (mg)", "7", "28", "29", "12", "16", "197", "30", "28", "17", "3"], ["Iron (mg)", "2.71", "0.8", "3.19", "0.78", "0.27", "3.55", "0.61", "4.4", "0.54", "0.6"], ["Magnesium (mg)", "127", "25", "126", "23", "21", "65", "25", "0", "21", "37"], ["Phosphorus (mg)", "210", "115", "288", "57", "27", "194", "47", "287", "55", "34"], ["Potassium (mg)", "287", "115", "363", "421", "271", "620", "337", "350", "816", "499"], ["Sodium (mg)", "35", "5", "2", "6", "14", "15", "55", "6", "9", "4"], ["Zinc (mg)", "2.21", "1.09", "2.65", "0.29", "0.34", "0.99", "0.3", "0", "0.24", "0.14"], ["Copper (mg)", "0.31", "0.22", "0.43", "0.11", "0.10", "0.13", "0.15", "-", "0.18", "0.08"], ["Manganese (mg)", "0.49", "1.09", "3.99", "0.15", "0.38", "0.55", "0.26", "-", "0.40", "-"], ["Selenium (μg)", "15.5", "15.1", "70.7", "0.3", "0.7", "1.5", "0.6", "0", "0.7", "1.5"], ["Vitamin C (mg)", "0", "0", "0", "19.7", "20.6", "29", "2.4", "0", "17.1", "18.4"], ["Thiamin (mg)", "0.39", "0.07", "0.30", "0.08", "0.09", "0.44", "0.08", "0.24", "0.11", "0.05"], ["Riboflavin (mg)", "0.20", "0.05", "0.12", "0.03", "0.05", "0.18", "0.06", "0.14", "0.03", "0.05"], ["Niacin (mg)", "3.63", "1.6", "5.46", "1.05", "0.85", "1.65", "0.56", "2.93", "0.55", "0.69"], ["Pantothenic acid (mg)", "0.42", "1.01", "0.95", "0.30", "0.11", "0.15", "0.80", "-", "0.31", "0.26"], ["Vitamin B6 (mg)", "0.62", "0.16", "0.3", "0.30", "0.09", "0.07", "0.21", "-", "0.29", "0.30"], ["Folate Total (μg)", "19", "8", "38", "16", "27", "165", "11", "0", "23", "22"], ["Vitamin A (IU)", "214", "0", "9", "2", "13", "180", "14187", "0", "138", "1127"], ["Vitamin E, alpha-tocopherol (mg)", "0.49", "0.11", "1.01", "0.01", "0.19", "0", "0.26", "0", "0.39", "0.14"], ["Vitamin K1 (μg)", "0.3", "0.1", "1.9", "1.9", "1.9", "0", "1.8", "0", "2.6", "0.7"], ["Beta-carotene (μg)", "97", "0", "5", "1", "8", "0", "8509", "0", "83", "457"], ["Lutein+zeaxanthin (μg)", "1355", "0", "220", "8", "0", "0", "0", "0", "0", "30"], ["Saturated fatty acids (g)", "0.67", "0.18", "0.26", "0.03", "0.07", "0.79", "0.02", "0.46", "0.04", "0.14"], ["Monounsaturated fatty acids (g)", "1.25", "0.21", "0.2", "0.00", "0.08", "1.28", "0.00", "0.99", "0.01", "0.03"], ["Polyunsaturated fatty acids (g)", "2.16", "0.18", "0.63", "0.04", "0.05", "3.20", "0.01", "1.37", "0.08", "0.07"]]}, "question": "What causes a significant increase in the energy content of staple foods, is it more closely related to the amount of carbohydrates, fat, or protein?", "answer": "Energy content in staple foods is most significantly influenced by carbohydrates (correlation coefficient of 0.96), compared to protein (correlation coefficient of 0.69)."}
{"id": "ad90ad414ff991cd2c1aed8154091536", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing Algeria", "Representing Algeria", "Representing Algeria", "Representing Algeria", "Representing Algeria", "Representing Algeria"], ["2001", "World Youth Championships", "Debrecen, Hungary", "26th", "10,000 m walk", "48:40.35"], ["2004", "World Race Walking Cup", "Naumburg, Germany", "–", "20 km walk", "DQ"], ["2006", "African Championships", "Bambous, Mauritius", "6th", "20 km walk", "1:29:34"], ["2007", "All-Africa Games", "Algiers, Algeria", "3rd", "20 km walk", "1:25:12"], ["2007", "Pan Arab Games", "Cairo, Egypt", "3rd", "20,000 m walk", "1:43:35.8"], ["2008", "African Championships", "Addis Ababa, Ethiopia", "1st", "20 km walk", "1:22:55 (CR)"], ["2008", "Olympic Games", "Beijing, China", "48th", "20 km walk", "1:32:21"], ["2009", "Universiade", "Belgrade, Serbia", "15th", "20 km walk", "1:26:21"], ["2010", "African Championships", "Nairobi, Kenya", "5th", "20 km walk", "1:24:53"], ["2012", "African Championships", "Addis Ababa, Ethiopia", "3rd", "20 km walk", "?"], ["2014", "African Championships", "Marrakech, Morocco", "3rd", "20 km walk", "1:27:48"], ["2015", "African Games", "Brazzaville, Republic of the Congo", "–", "20 km walk", "DNF"], ["2016", "African Championships", "Durban, South Africa", "7th", "20 km walk", "1:26:17"], ["2018", "African Championships", "Asaba, Nigeria", "6th", "20 km walk", "1:28.38"]]}, "question": "In which year did the athlete achieve their personal best time in the 20 km walk event at the African Championships?", "answer": "2008"}
{"id": "b32c2c1e4f5251447219723a5e32228a", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["rank", "city", "population", "area (km 2 )", "density (inhabitants / km 2 )", "altitude (mslm)"], "data": [["1st", "alessandria", "94191", "203.97", "461.8", "95"], ["2nd", "casale monferrato", "36039", "86.32", "417.5", "116"], ["3rd", "novi ligure", "28581", "54.22", "527.1", "197"], ["4th", "tortona", "27476", "99.29", "276.7", "122"], ["5th", "acqui terme", "20426", "33.42", "611.2", "156"], ["6th", "valenza", "20282", "50.05", "405.2", "125"], ["7th", "ovada", "11912", "35.33", "337.2", "186"], ["8th", "serravalle scrivia", "6445", "16.02", "402.3", "225"], ["9th", "arquata scrivia", "6260", "30.36", "206.2", "248"], ["10th", "castelnuovo scrivia", "5473", "45.42", "120.5", "85"]]}, "question": "Which city has the highest 'density (inhabitants / km 2 )', and how is the difference compared to the city with the lowest?", "answer": "acqui terme, 490.7"}
{"id": "8dcdb337eb9607dcb80c77dae5ac6e20", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "bötzow", "schwante", "vehlefanz", "neu - vehlefanz", "marwitz", "bärenklau", "eichstädt"], "data": [["2004", "2.785", "1.983", "1.771", "340", "1.407", "1.291", "942"], ["2005", "2.904", "1.997", "1.777", "348", "1.4", "1.294", "939"], ["2006", "2.973", "2.061", "1.814", "391", "1.432", "1.33", "926"], ["2007", "2.947", "2.061", "1.821", "379", "1.435", "1.313", "929"], ["2008", "2.937", "2.043", "1.8", "355", "1.398", "1.294", "876"], ["2009", "2.967", "2.039", "1.759", "365", "1.417", "1.27", "848"], ["2010", "2.981", "2.089", "1.765", "385", "1.429", "1.288", "850"]]}, "question": "In which year did the value in `bötzow` increase the most compared to the previous year?", "answer": "2005"}
{"id": "e617dfb780c7add548b0874e476724e6", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["year of marriage", "name", "her age", "his age", "of children"], "data": [["1835", "louisa maria tanner", "17", "22", "8"], ["1843", "diontha walker", "27", "30", "0"], ["1844", "caroline partridge", "17", "31", "6"], ["1846", "eliza maria partridge", "23", "33", "5"], ["1846", "paulina eliza phelps", "19", "33", "7"], ["1846", "priscilla turley", "17", "33", "6"], ["1846", "cornelia leavitt", "21", "33", "2"], ["1853", "lydia partridge", "23", "40", "4"]]}, "question": "Does the age of the wife at the time of marriage have a significant impact on the number of children she has?", "answer": "No, the age of the wife at the time of marriage, with a correlation coefficient of -0.31, suggests a negligible inverse relationship."}
{"id": "8784e31776b33c2a8c9988602a50dabc", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], "data": [["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], ["maesteg rfc", "22", "2", "1", "615", "271", "78", "24", "12", "0", "92"], ["waunarlwydd rfc", "22", "1", "7", "594", "359", "73", "38", "10", "5", "73"], ["bp llandarcy rfc", "22", "1", "7", "376", "320", "43", "36", "3", "5", "66"], ["kidwelly rfc", "22", "0", "9", "558", "393", "68", "39", "6", "6", "64"], ["aberavon quins rfc", "22", "0", "9", "449", "424", "56", "45", "6", "3", "61"], ["ammanford rfc", "22", "1", "10", "409", "348", "45", "33", "4", "8", "58"], ["loughor rfc", "22", "1", "11", "427", "479", "47", "60", "5", "4", "51"], ["aberystwyth rfc", "22", "0", "12", "390", "509", "46", "71", "5", "4", "49"], ["pontyberem rfc", "22", "0", "12", "353", "520", "35", "67", "4", "3", "47"], ["mumbles rfc", "22", "1", "14", "372", "471", "51", "55", "5", "4", "39"], ["pencoed rfc", "22", "0", "19", "321", "505", "34", "62", "0", "10", "22"], ["dunvant rfc", "22", "1", "17", "324", "589", "33", "79", "0", "2", "20"]]}, "question": "How many games did Maesteg RFC play in the season?", "answer": "22"}
{"id": "73a06f4dbbb1534fa4a19027c6802804", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["cost", "2400 kwh / kwp y", "2200 kwh / kwp y", "2000 kwh / kwp y", "1800 kwh / kwp y", "1600 kwh / kwp y", "1400 kwh / kwp y", "1200 kwh / kwp y", "1000 kwh / kwp y", "800 kwh / kwp y"], "data": [["200 / kwp", "0.8", "0.9", "1.0", "1.1", "1.3", "1.4", "1.7", "2.0", "2.5"], ["600 / kwp", "2.5", "2.7", "3.0", "3.3", "3.8", "4.3", "5.0", "6.0", "7.5"], ["1000 / kwp", "4.2", "4.5", "5.0", "5.6", "6.3", "7.1", "8.3", "10.0", "12.5"], ["1400 / kwp", "5.8", "6.4", "7.0", "7.8", "8.8", "10.0", "11.7", "14.0", "17.5"], ["1800 / kwp", "7.5", "8.2", "9.0", "10.0", "11.3", "12.9", "15.0", "18.0", "22.5"], ["2200 / kwp", "9.2", "10.0", "11.0", "12.2", "13.8", "15.7", "18.3", "22.0", "27.5"], ["2600 / kwp", "10.8", "11.8", "13.0", "14.4", "16.3", "18.6", "21.7", "26.0", "32.5"], ["3000 / kwp", "12.5", "13.6", "15.0", "16.7", "18.8", "21.4", "25.0", "30.0", "37.5"], ["3400 / kwp", "14.2", "15.5", "17.0", "18.9", "21.3", "24.3", "28.3", "34.0", "42.5"], ["3800 / kwp", "15.8", "17.3", "19.0", "21.1", "23.8", "27.1", "31.7", "38.0", "47.5"], ["4200 / kwp", "17.5", "19.1", "21.0", "23.3", "26.3", "30.0", "35.0", "42.0", "52.5"], ["4600 / kwp", "19.2", "20.9", "23.0", "25.6", "28.8", "32.9", "38.3", "46.0", "57.5"]]}, "question": "What is the total cost for systems that produce at least 2000 kwh/kwp/year, and have a cost per kwp of at most $1400?", "answer": "43.8"}
{"id": "5c7122c5e930420e97932e966e52ae05", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["country with flag", "area (km square)", "population (1 july 2005 est)", "population density (per km square)", "capital"], "data": [["cuba", "110860", "11346670", "102.4", "havana"], ["cayman islands (uk)", "264", "54878", "207.9", "george town"], ["dominican republic", "48730", "8950034", "183.7", "santo domingo"], ["haiti", "27750", "8121622", "292.7", "port - au - prince"], ["jamaica", "10991", "2731832", "248.6", "kingston"], ["puerto rico (usa)", "9104", "3916632", "430.2", "san juan"]]}, "question": "Can you provide a descriptive explanation of the table, highlighting the main columns and offering some basic insights about the countries or territories listed?**", "answer": "The table provides comprehensive data on various Caribbean countries and territories, encompassing their area, population estimates as of July 2005, population density, and capitals. This dataset includes both independent nations and territories governed by other countries, with significant variations in size, population, and density across the regions listed."}
{"id": "bf5aa174142f7c00d027c71cde38f669", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Notes", "-"], "data": [["Representing Ireland", "Representing Ireland", "Representing Ireland", "Representing Ireland", "Representing Ireland", "-"], ["1978", "Irish National Marathon Championship", "Tullamore", "Gold", "2:23:19", "18 June 1978"], ["1978", "1978 European Championships in Athletics", "Prague", "29th", "2:21:01", "3 September 1978"], ["1980", "Dublin Marathon", "Dublin", "Gold", "2:16:14", "26 October 1980"], ["1980", "Irish National Marathon Championship", "Tullamore", "Gold", "2:16:27", "8 July 1980"], ["1980", "Moscow Olympics", "Moscow", "38th place", "2:23:53", "1 August 1980"], ["1981", "Irish National Marathon Championship", "Cork", "Gold", "2:15:37", "7 June 1981"], ["1982", "Irish National Marathon Championship", "Limerick", "Gold", "2:12:56", "6 June 1982"], ["1982", "1982 European Championships in Athletics – Men's Marathon", "Athens", "11th place", "2:20:51", "12 September 1982"], ["1984", "Irish National Marathon Championship", "Cork", "Gold", "2:14:39", "23 April 1984"], ["1984", "Los Angeles Olympics", "Los Angeles", "51st place", "2:24:41", "12 August 1984"], ["1985", "Dublin Marathon", "Dublin", "Gold", "2:13:48", "27 October 1985"], ["1986", "Dublin Marathon", "Dublin", "Gold", "2:18:10", "26 October 1986"], ["1986", "1986 European Athletics Championships – Men's marathon", "Stuttgart", "16th place", "2:17.45", "30 August 1986"], ["1987", "1987 Dublin Marathon", "Dublin", "Bronze", "2:14:36", "25 October 1987"], ["1988", "Irish National Marathon Championship", "Wexford", "Silver", "2:12:19 PB", "24 April 1988"], ["1988", "Seoul Olympics", "Seoul", "24th place", "2:17:16", "2 October 1988"], ["1990", "1990 European Championships in Athletics – Men's Marathon", "Split", "23rd place", "2:32.36", "1 September 1990"], ["1998", "Irish National Marathon Championship", "Killenaule", "Gold", "(2:22:08)", "19 April 1998"], ["1998", "New York City Marathon", "New York City", "34th place", "2:22:46", "1 November 1998"]]}, "question": "In which year did the athlete achieve a personal best (PB) time in the Irish National Marathon Championship, and what was the venue for that event?", "answer": "1982,Limerick"}
{"id": "ba50a0e8eb4cb8d333a99027ae817059", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["Animal", "Sex", "Metabolic rate", "Mean", "Difference from mean", "Squared difference from mean"], "data": [["1", "Female", "727.7", "1285.5", "557.8", "311140.84"], ["2", "Female", "1086.5", "1285.5", "199.0", "39601.00"], ["3", "Female", "1091.0", "1285.5", "194.5", "37830.25"], ["4", "Female", "1361.3", "1285.5", "75.8", "5745.64"], ["5", "Female", "1490.5", "1285.5", "205.0", "42025.00"], ["6", "Female", "1956.1", "1285.5", "670.6", "449704.36"], ["-", "-", "-", "-", "-", "-"], ["Mean of metabolic rates", "Mean of metabolic rates", "Mean of metabolic rates", "1285.5", "Sum of squared differences", "886047.09"]]}, "question": "What is the difference between the highest and lowest metabolic rates among the female animals?", "answer": "1228.4."}
{"id": "6423fac749dc4e40ed398068f69b433d", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["drug", "mean", "pleasure", "psychological dependence", "physical dependence"], "data": [["heroin", "3.0", "3.0", "3.0", "3.0"], ["cocaine", "2.37", "3.0", "2.8", "1.3"], ["alcohol", "1.93", "2.3", "1.9", "1.6"], ["barbiturates", "2.01", "2.0", "2.2", "1.8"], ["benzodiazepines", "1.83", "1.7", "2.1", "1.8"], ["amphetamine", "1.67", "2.0", "1.9", "1.1"], ["cannabis", "1.51", "1.9", "1.7", "0.8"], ["ecstasy", "1.13", "1.5", "1.2", "0.7"], ["lsd", "0.9", "1.3", "1.1", "0.3"]]}, "question": "When the pleasure rating of a drug increases, does it have a greater impact on psychological or physical dependence on that drug?", "answer": "Pleasure rating increases have a stronger correlation with psychological dependence (0.92) than with physical dependence (0.69) on the drug."}
{"id": "d7b545735f844944b02b05fe0343cb44", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], "data": [["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], ["llandeilo rfc", "22", "1", "0", "917", "119", "136", "14", "19", "0", "105"], ["brynamman rfc", "22", "1", "2", "821", "210", "116", "27", "16", "2", "96"], ["tenby united rfc", "22", "0", "8", "562", "461", "78", "61", "10", "1", "67"], ["pembroke dock harlequins rfc", "22", "0", "8", "423", "351", "56", "40", "7", "3", "66"], ["pontarddulais rfc", "22", "1", "9", "550", "503", "79", "68", "11", "5", "66"], ["betws rfc", "22", "1", "9", "528", "440", "72", "63", "9", "0", "59"], ["trimsaran rfc", "22", "0", "12", "471", "540", "68", "77", "7", "1", "48"], ["pembroke rfc", "22", "0", "13", "467", "500", "69", "66", "8", "4", "48"], ["burry port rfc", "22", "1", "14", "373", "688", "47", "99", "3", "2", "31"], ["hendy rfc", "22", "0", "17", "292", "707", "38", "109", "1", "6", "27"], ["tycroes rfc", "22", "0", "18", "267", "645", "35", "89", "3", "3", "18"], ["cwmgors rfc", "22", "1", "19", "211", "718", "28", "109", "2", "3", "15"]]}, "question": "According to the table, how many tries did Llandeilo RFC score in the season?", "answer": "136"}
{"id": "75161f5694422778f9358fe477854946", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing New Caledonia", "Representing New Caledonia", "Representing New Caledonia", "Representing New Caledonia", "Representing New Caledonia", "Representing New Caledonia"], ["1966", "South Pacific Games", "Nouméa, New Caledonia", "1st", "Shot put", "15.82 m"], ["1966", "South Pacific Games", "Nouméa, New Caledonia", "2nd", "Discus throw", "44.68 m"], ["1969", "South Pacific Games", "Port Moresby, Papua New Guinea", "1st", "Shot put", "17.89 m"], ["1969", "South Pacific Games", "Port Moresby, Papua New Guinea", "1st", "Discus throw", "50.22 m"], ["1969", "South Pacific Games", "Port Moresby, Papua New Guinea", "3rd", "Hammer throw", "41.84 m"], ["1971", "South Pacific Games", "Pirae, French Polynesia", "1st", "Shot put", "18.07 m"], ["1971", "South Pacific Games", "Pirae, French Polynesia", "1st", "Discus throw", "49.98 m"], ["1971", "South Pacific Games", "Pirae, French Polynesia", "3rd", "Hammer throw", "44.00 m"], ["1975", "South Pacific Games", "Tumon, Guam", "1st", "Shot put", "18.07 m"], ["1975", "South Pacific Games", "Tumon, Guam", "1st", "Discus throw", "48.30 m"], ["1975", "South Pacific Games", "Tumon, Guam", "2nd", "Hammer throw", "43.66 m"], ["1983", "South Pacific Games", "Apia, Western Samoa", "1st", "Shot put", "16.97 m"], ["1983", "South Pacific Games", "Apia, Western Samoa", "2nd", "Discus throw", "48.70 m"]]}, "question": "In which year did the athlete win the most gold medals in a single South Pacific Games?", "answer": "1969, 1971, 1975"}
{"id": "6c43b934f20ce71710bfb837d0fbc556", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["Year", "Injuries (US $000)", "Deaths (age <15)", "CPSC toy safety funding\n(US$ Millions)", "Toy sales\n(US $ Billions)"], "data": [["1994", "154", null, null, null], ["1995", "139", null, null, null], ["1996", "130", null, null, null], ["1997", "141", null, null, null], ["1998", "153", "14.0", null, null], ["1999", "152", "16.0", "13.6", null], ["2000", "191", "17.0", "12.0", null], ["2001", "255", "25.0", "12.4", null], ["2002", "212", "13.0", "12.2", "21.3"], ["2003", "206", "11.0", "12.8", "20.7"], ["2004", "210", "16.0", "11.5", "22.4"], ["2005", "202 (estimate)", "20.0", "11.0", "22.2"], ["2006", "no data", "22.0", "no data†", "22.3"], ["2007", "no data", "22.0", "no data", null], ["2008", "no data", "19.0", "no data", null], ["2009", "no data", "12.0", "no data", null]]}, "question": "Which year had the highest number of injuries (in thousands of US dollars) according to the provided data?", "answer": "2001"}
{"id": "3b35d95ee257a5d59d6b3eb9d15c73ae", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["chambering", "p1 diameter (mm)", "a external (cm 2 )", "p max ( bar )", "f bolt ( kgf )", "f bolt"], "data": [["5.45x39 mm", "10.0", "0.7854", "3800", "2985", "n ( lbf )"], [".223 remington", "9.58", "0.7208", "4300", "3099", "n (lbf)"], ["7.62x39 mm", "11.35", "1.0118", "3550", "3592", "n (lbf)"], [".308 winchester", "11.96", "1.1234", "4150", "4662", "n (lbf)"], [".300 winchester magnum", "13.03", "1.3335", "4300", "5734", "n (lbf)"], [".300 wsm", "14.12", "1.5659", "4450", "6968", "n (lbf)"], [".300 remington ultra magnum", "13.97", "1.5328", "4480", "6876", "n (lbf)"], [".338 lapua magnum", "14.91", "1.746", "4200", "7333", "n (lbf)"], [".300 lapua magnum", "14.91", "1.746", "4700", "8339", "n (lbf)"], [".50 bmg", "20.42", "3.2749", "3700", "12117", "n (lbf)"]]}, "question": "How does the maximum pressure (p max) of the ammunition change with increasing projectile diameter (p1 diameter)?", "answer": "The maximum pressure (p max) of the ammunition exhibits no causal effect (-0.01) with increasing projectile diameter (p1 diameter)."}
{"id": "21ed2d8a7cbb07a4ae880fc3fdbe5cbb", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["ship name", "year", "length", "width", "passengers", "vessels", "speed"], "data": [["mytilene", "1973", "138 , 3 m", "22 , 4 m", "1.73", "225", "20"], ["european express", "1974", "159 , 5 m", "21 , 5 m", "1.0", "350", "23"], ["ionian sky", "1974", "164 m", "24 m", "1.09", "600", "22"], ["theofilos", "1975", "149 , 4 m", "23 , 5 m", "1.66", "433", "18"], ["taxiarchis", "1976", "135 , 8 m", "20 , 6 m", "591.0", "392", "18"], ["aqua jewel", "2002", "108 m", "16 , 6 m", "1.675", "175", "18 , 5"], ["aqua maria", "1975", "101 , 3 m", "18 m", "592.0", "230", "17"], ["aqua spirit", "2000", "75 m", "15 m", "400.0", "60", "17"]]}, "question": "In which year did the average speed of the vessels increase the most compared to the previous year?", "answer": "1974"}
{"id": "b878f1ad3f7646fcd7ede1bc02533f33", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["Election year", "# of\nconstituency votes", "% of\nconstituency votes", "+/–", "# of\nparty list votes", "% of\nparty list votes", "+/–.1", "# of\noverall seats won"], "data": [["1965", "587,216", "1.8", "1.8", "664,193", "2.0", "2.0", "0 / 518"], ["1969", "1,189,375", "3.6", "1.8", "1,422,010", "4.3", "2.3", "0 / 518"], ["1972", "194,389", "0.5", "3.1", "207,465", "0.6", "3.7", "0 / 518"], ["1976", "136.023", "0.4", "0.1", "122,661", "0.3", "0.3", "0 / 518"], ["1980", null, null, null, "68,096", "0.2", "0.1", "0 / 497"], ["1983", "57,112", "0.1", "0.3", "91,095", "0.2", "0.0", "0 / 498"], ["1987", "182,880", "0.5", "0.4", "227,054", "0.6", "0.4", "0 / 497"], ["1990", "190,105", "0.4", "0.1", "145,776", "0.3", "0.3", "0 / 662"], ["1998", "45,043", "0.1", "0.3", "126,571", "0.3", "0.0", "0 / 669"], ["2002", "103,209", "0.1", "0.1", "215,232", "0.4", "0.1", "0 / 603"], ["2005", "857.777", "1.8", "1.6", "748,568", "1.6", "1.2", "0 / 614"], ["2009", "768,442", "1.8", "0.0", "635,525", "1.5", "0.1", "0 / 620"], ["2013", "634,842", "1.5", "0.3", "560,660", "1.3", "0.2", "0 / 630"]]}, "question": "Which election year had the highest percentage of party list votes?", "answer": "1969"}
{"id": "e81397add1c0790a16461b55739e62f0", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["chambering", "p1 diameter (mm)", "a external (cm 2 )", "p max ( bar )", "f bolt ( kgf )", "f bolt"], "data": [[".22 long rifle", "5.74", "0.2587", "1650", "435", "n (lbf)"], ["9x19 mm parabellum", "9.93", "0.7744", "2350", "1820", "n ( lbf )"], [".357 sig", "10.77", "0.911", "3050", "2779", "n (lbf)"], [".380 acp", "9.7", "0.739", "1500", "1130", "n (lbf)"], [".40 s&w", "10.77", "0.911", "2250", "2050", "n (lbf)"], ["10 mm auto", "10.81", "0.9178", "2300", "2111", "n (lbf)"], [".45 acp", "12.09", "1.1671", "1300", "1517", "n (lbf)"], [".454 casull", "12.13", "1.1556", "3900", "4507", "n (lbf)"]]}, "question": "What is the correlation between the 'p1 diameter (mm)' and 'p max (bar)' columns in the table? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.40"}
{"id": "8869ae21589a1ab50a40faf5d85d8eaf", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["member countries", "population", "area (km square)", "gdp (billion us)", "gdp per capita (us)"], "data": [["belgium", "9052707", "30528", "58.316", "46878"], ["france", "44788852", "674843", "312.966", "40690"], ["west germany", "54292038", "248717", "400.554", "41168"], ["italy", "49476000", "301336", "265.192", "30116"], ["luxembourg", "310291", "2586", "2.938", "113533"], ["netherlands", "11186847", "41526", "83.351", "50355"], ["ec6 (1958)", "169106736", "1299536", "1123.317", "6643"]]}, "question": "What is the minimum increase in GDP per capita required for West Germany to surpass the GDP per capita of France, assuming the population of both countries remains the same?", "answer": "0"}
{"id": "6bf0106b75631feb6f504e4d48bb895c", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["No.", "SWV", "Title", "English", "Source", "Details"], "data": [["1", "257", "Paratum cor meum, Deus", "My heart is ready, O God", "Psalms 108:1–3", "257"], ["2", "258", "Exultavit cor meum in Domino", "My heart rejoiceth in the Lord", "1 Samuel 2:1–2", "258"], ["3", "259", "In te, Domine, speravi", "I will extol thee, O Lord", "Psalms 30:1–2,1", "259"], ["4", "260", "Cantabo domino in vita mea", "I will sing unto the Lord as long as I live", "Psalms 104:33", "260"], ["5", "261", "Venite ad me omnes qui laboratis", "Come unto me, all ye that labour", "Matthew 11:28–30", "261"], ["6", "262", "Jubilate Deo omnis terra", "Make a joyful noise unto the Lord", "Psalms 100", "262"], ["7", "263", "Anima mea liquefacta est", "My soul melted when my beloved spoke", "Song of Solomon 5:6; 2:14; 5:13; 5:8", "263"], ["8", "264", "Adjuro vos, filiae Jerusalem", "I adjure you, daughters of Jerusalem", "Song of Solomon 5:6; 2:14; 5:13; 5:8", "264"], ["9", "265", "O quam tu pulchra es, amica mea", "How beautiful you are, my love", "Song of Solomon 4:1-5,8", "265"], ["10", "266", "Veni de Libano, veni, amica mea", "Advance from Lebanon, my spouse", "Song of Solomon 4:1-5,8", "266"], ["11", "267", "Benedicam Dominum in omni tempore", "I will bless the Lord at all times", "Psalms 34:1–2", "267"], ["12", "268", "Exquisivi Dominum et exaudivit me", "I sought the Lord, and he heard me", "Psalms 34:4–6", "268"], ["13", "269", "Fili mi, Absalon", "My son, Absalon", "2 Samuel 18:32", "269"], ["14", "270", "Attendite, popule meus", "Give ear, O my people", "Psalms 78:1–3", "270"], ["15", "271", "Domine, labia mea aperies", "O Lord, open thou my lips", "Psalms 51:15", "271"], ["16", "272", "In lectulo per noctes", "On my bed, throughout the night", "Song of Solomon 3:1-2,4", "272"], ["17", "273", "Invenerunt me costudes civitatis", "The watchers who guard the city found me", "Song of Solomon 3:1-2,4", "273"], ["18", "274", "Veni, dilecte mi, in hortum meum", "May my beloved enter into his garden", "Song of Solomon 5:1", "274"], ["19", "275", "Buccinate in neomenia tuba", "Blow the trumpet when the moon is new", "Psalms 81:3,1; 98:6", "275"], ["20", "276", "Jubilate Deo in chordis", "Let us rejoice in God with strings and organ", "Psalms 150:4; Psalms 98:4", "276"]]}, "question": "How many songs or hymns in the table have a source from the book of Psalms?", "answer": "10"}
{"id": "77ad2ce9d250a32132f06e3679f8fc49", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["Particulars", "Total", "Male", "Female"], "data": [["Total No. of Houses", "14", "-", "-"], ["Population", "55", "25", "30"], ["Child (0-6)", "7", "3", "4"], ["Schedule Caste", "1000", "500", "500"], ["Schedule Tribe", "0", "0", "0"], ["Literacy", "79.17 %", "86.36 %", "73.08 %"], ["Total Workers", "15", "12", "3"], ["Main Worker", "-10", "-5", "-5"], ["Marginal Worker", "0", "0", "0"]]}, "question": "What anomaly can be detected in the demographic data of a rural town, and what could be the possible explanations for these anomalies??", "answer": "The two anomalies include an unusually high Schedule Caste count of 1000, possibly due to a data entry error or unique demographic trait, and a negative Main Worker value (-10)."}
{"id": "502645e77ad3f4a83adb38da6c6168b7", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["rank", "airport", "total passengers", "% change 2007 / 2008", "international passengers", "domestic passengers", "transit passengers", "aircraft movements", "freight ( metric tonnes )"], "data": [["1", "london heathrow", "67054745", "1.5%", "61344438", "5562516", "147791", "478693", "1397054"], ["2", "london gatwick", "34205887", "2.9%", "30431051", "3730963", "43873", "263653", "107702"], ["3", "london stansted", "22360364", "6.0%", "19996947", "2343428", "19989", "193282", "197738"], ["4", "manchester", "21219195", "4.0%", "18119230", "2943719", "156246", "204610", "141781"], ["5", "london luton", "10180734", "2.6%", "8853224", "1320678", "6832", "117859", "40518"], ["6", "birmingham airport", "9627589", "4.3%", "8105162", "1471538", "50889", "112227", "12192"], ["7", "edinburgh", "9006702", "0.5%", "3711140", "5281038", "14524", "125550", "12418"], ["8", "glasgow international", "8178891", "7.0%", "3943139", "4192121", "43631", "100087", "3546"], ["9", "bristol", "6267114", "5.7%", "5057051", "1171605", "38458", "76517", "3"], ["10", "east midlands", "5620673", "3.8%", "4870184", "746094", "4395", "93038", "261507"], ["11", "liverpool", "5334152", "2.5%", "4514926", "814900", "4326", "84890", "3740"], ["12", "belfast international", "5262354", "0.2%", "2122844", "3099995", "39515", "77943", "36115"], ["13", "newcastle", "5039993", "10.8%", "3506681", "1509959", "23353", "72904", "1938"], ["14", "aberdeen", "3290920", "3.6%", "1470099", "1820137", "684", "119831", "4006"], ["15", "london city", "3260236", "12.0%", "2600731", "659494", "11", "94516", "0"], ["16", "leeds bradford", "2873321", "0.3%", "2282358", "578089", "12874", "61699", "334"], ["17", "belfast city", "2570742", "17.5%", "70516", "2500225", "1", "42990", "168"], ["18", "glasgow prestwick", "2415755", "0.3%", "1728020", "685999", "1736", "42708", "22966"], ["19", "cardiff", "1994892", "5.5%", "1565991", "412728", "16173", "37123", "1334"]]}, "question": "How does a change in international passengers impact the rank of an airport?", "answer": "No clear impact"}
{"id": "ee32b677b3e51d25608fcdbef787f33b", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["-", "Total", "Male", "Female"], "data": [["Population", "7159", "3645", "3514"], ["Children aged below 6 years", "913", "479", "434"], ["Scheduled caste", "1782", "890", "892"], ["Scheduled tribe", "744", "383", "361"], ["Literates", "4323", "2642", "1681"], ["Workers (all)", "3612", "2007", "1605"], ["Main workers (total)", "2187", "1463", "724"], ["Main workers: Cultivators", "756", "500", "256"], ["Main workers: Agricultural labourers", "830", "443", "387"], ["Main workers: Household industry workers", "107", "86", "21"], ["Main workers: Other", "494", "434", "60"], ["Non-workers (total)", "3547", "1638", "1909"]]}, "question": "Which gender has a higher number of literates, Male or Female?", "answer": "Male"}
{"id": "c7d3b4bc8a57ba77136b864b42e00c90", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["period", "live births per year", "deaths per year", "natural change per year", "cbr", "cdr", "nc", "tfr", "imr", "life expectancy total", "life expectancy males", "life expectancy females"], "data": [["1950 - 1955", "2 572 000", "900 000", "1 672 000", "44.1", "15.5", "28.6", "6.15", "135", "50.9", "49.2", "52.6"], ["1955 - 1960", "2 918 000", "947 000", "1 971 000", "43.2", "14.0", "29.1", "6.15", "122", "53.3", "51.5", "55.2"], ["1960 - 1965", "3 303 000", "986 000", "2 317 000", "42.2", "12.6", "29.6", "6.15", "109", "55.7", "53.8", "57.6"], ["1965 - 1970", "3 330 000", "998 000", "2 332 000", "37.0", "11.1", "25.9", "5.38", "100", "57.6", "55.7", "59.6"], ["1970 - 1975", "3 441 000", "1 014 000", "2 427 000", "33.7", "9.9", "23.8", "4.72", "91", "59.5", "57.3", "61.8"], ["1975 - 1980", "3 741 000", "1 043 000", "2 698 000", "32.5", "9.0", "23.5", "4.31", "79", "61.5", "59.2", "63.9"], ["1980 - 1985", "3 974 000", "1 064 000", "2 910 000", "30.8", "8.2", "22.6", "3.8", "63", "63.4", "60.4", "66.8"], ["1985 - 1990", "3 757 000", "1 055 000", "2 702 000", "26.3", "7.4", "18.9", "3.1", "52", "65.3", "61.9", "69.1"], ["1990 - 1995", "3 519 000", "1 058 000", "2 461 000", "22.6", "6.8", "15.8", "2.6", "43", "67.3", "63.6", "71.2"], ["1995 - 2000", "3 624 000", "1 086 000", "2 538 000", "21.5", "6.5", "15.1", "2.45", "34", "69.3", "65.5", "73.3"], ["2000 - 2005", "3 572 000", "1 147 000", "2 425 000", "19.8", "6.4", "13.4", "2.25", "27", "70.9", "67.2", "74.8"]]}, "question": "Based on the historical data from 1950 to 2005, what might be the forecasted life expectancy for males and females for the period 2010 - 2015?", "answer": "69.07, 77.71"}
{"id": "12419b5c2150c7e8e68d32f85ffc9faf", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "us rank", "total s ton", "domestic s ton", "foreign total s ton", "foreign imports s ton", "foreign exports s ton"], "data": [["2006", "102", "2926536", "2306192", "620344", "464774", "155570"], ["2005", "94", "3527469", "2629553", "897916", "430396", "467520"], ["2004", "101", "3085753", "2323089", "762664", "284347", "478317"], ["2003", "96", "3178633", "2494261", "684372", "218233", "466139"], ["2002", "102", "2983137", "2318653", "664484", "251203", "413281"], ["2001", "108", "2861134", "2157496", "703638", "225281", "478357"], ["2000", "103", "3157247", "2416514", "740733", "382240", "358493"]]}, "question": "What is the total s ton that has the highest increase from the previous year between 2000 and 2006?", "answer": "3527469"}
{"id": "4a9ffbf9e8babf2558133ff3ffa87d19", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["region", "land area (km 2 )", "rainfall by depth (mm / year)", "rainfall by volume (km 3 / year)", "surface run off (km 3 / year)", "infiltration (km 3 / year)", "evapotranspiration (km 3 / year)"], "data": [["chorotega", "9552.4", "2006", "19.2", "5.7", "3.5", "10.3"], ["huetar norte", "9001.5", "3527", "31.8", "14.9", "9.6", "7.5"], ["huetar atlántico", "9688.5", "3933", "38.1", "17.6", "9.3", "11.1"], ["pacífico central", "4722.9", "2801", "13.2", "5.2", "2.2", "4.9"], ["central", "8543.2", "3461", "29.6", "13.0", "7.0", "8.6"], ["brunca", "9294.5", "3809", "35.4", "18.6", "5.6", "12.2"]]}, "question": "Which top2 factors in the table, such as 'land area (km^2)', 'rainfall by depth (mm/year)', 'surface run off (km^3/year)', 'infiltration (km^3/year)', and 'evapotranspiration (km^3/year)', significantly influence the 'rainfall by volume (km^3/year)' for each region?", "answer": "infiltration, surface run off"}
{"id": "67698e1118741098f31ddb5e6b27a831", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["name", "latitude", "longitude", "diameter (km)", "named after"], "data": [["caccini", "17.4", "170.4", "38.1", "francesca caccini , italian composer"], ["caitlin", "- 65.3", "12.0", "14.7", "irish first name"], ["caiwenji", "- 12.4", "287.6", "22.6", "cai wenji , chinese poet"], ["caldwell", "23.6", "112.4", "51.0", "taylor caldwell , american author"], ["callas", "2.4", "27.0", "33.8", "maria callas , american singer"], ["callirhoe", "21.2", "140.7", "33.8", "callirhoe , greek sculptor"], ["caroline", "6.9", "306.3", "18.0", "french first name"], ["carr", "- 24", "295.7", "31.9", "emily carr , canadian artist"], ["carreno", "- 3.9", "16.1", "57.0", "teresa carreño , n venezuela pianist"], ["carson", "- 24.2", "344.1", "38.8", "rachel carson , american biologist"], ["carter", "5.3", "67.3", "17.5", "maybelle carter , american singer"], ["castro", "3.4", "233.9", "22.9", "rosalía de castro , galician poet"], ["cather", "47.1", "107.0", "24.6", "willa cather , american novelist"], ["centlivre", "19.1", "290.4", "28.8", "susanna centlivre , english actress"], ["chapelle", "6.4", "103.8", "22.0", "georgette chapelle , american journalist"], ["chechek", "- 2.6", "272.3", "7.2", "tuvan first name"], ["chiyojo", "- 47.8", "95.7", "40.2", "chiyojo , japanese poet"], ["chloe", "- 7.4", "98.6", "18.6", "greek first name"], ["cholpon", "40", "290.0", "6.3", "kyrgyz first name"], ["christie", "28.3", "72.7", "23.3", "agatha christie , english author"], ["chubado", "45.3", "5.6", "7.0", "fulbe first name"], ["clara", "- 37.5", "235.3", "3.2", "latin first name"], ["clementina", "35.9", "208.6", "4.0", "portuguese form of clementine , french first name"], ["cleopatra", "65.8", "7.1", "105.0", "cleopatra , egyptian queen"], ["cline", "- 21.8", "317.1", "38.0", "patsy cline , american singer"], ["clio", "6.3", "333.5", "11.4", "greek first name"], ["cochran", "51.9", "143.4", "100.0", "jacqueline cochran , american aviator"], ["cohn", "- 33.3", "208.1", "18.3", "carola cohn , australian artist"], ["colleen", "- 60.8", "162.2", "13.5", "irish first name"], ["comnena", "1.2", "343.7", "19.5", "anna comnena , byzantine princess and writer"], ["conway", "48.3", "39.0", "49.3", "lady anne finch conway , english natural scientist"], ["cori", "25.4", "72.9", "56.1", "gerty cori , czech biochemist"], ["corinna", "22.9", "40.6", "19.2", "corinna , greek poet"], ["corpman", "0.3", "151.8", "46.0", "elizabeth koopman hevelius , astronomer"], ["cortese", "- 11.4", "218.4", "27.7", "isabella cortese , italian physician"], ["cotton", "70.8", "300.2", "48.1", "eugénie cotton , french physicist"], ["cunitz", "14.5", "350.9", "48.6", "maria cunitz , silesian astronomer"], ["cynthia", "- 16.7", "347.5", "15.9", "greek first name"]]}, "question": "What is the median diameter of craters on this celestial body?", "answer": "23.95"}
{"id": "7bec36f08c73ba8d7e122f15b4736e19", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["Rank", "Magnitude", "Death toll", "Location", "Depth (km)", "Date"], "data": [["1", "7.6", "0", "Peru Madre de Dios Region, Peru", "612.2", "August 19"], ["2", "7.5", "2", "Japan Miyazaki Prefecture, Kyushu, Japan", "35.0", "February 26"], ["2", "7.5", "0", "Peru Ucayali Region, Peru", "619.9", "August 31"], ["3", "7.4", "0", "New Zealand Kermadec Islands, New Zealand", "421.1", "June 18"], ["4", "7.3", "0", "Indonesia Gulf of Tomini, Indonesia", "144.8", "March 28"], ["4", "7.3", "0", "Vanuatu Vanuatu", "25.0", "July 23"], ["4", "7.3", "0", "United Kingdom South Sandwich Islands", "129.2", "September 1"], ["5", "7.2", "0", "Japan off the east coast of Honshu, Japan", "30.0", "January 16"], ["5", "7.2", "0", "Peru Madre de Dios Region, Peru", "597.5", "August 31"], ["6", "7.1", "0", "Japan eastern Hokkaido, Japan", "43.9", "August 11"], ["6", "7.1", "0", "United Kingdom South Sandwich Islands", "100.9", "September 8"], ["7", "7.0", "0", "Japan off the east coast of Honshu, Japan", "30.0", "January 16"], ["7", "7.0", "0", "New Zealand Kermadec Islands, New Zealand", "30.0", "March 7"], ["7", "7.0", "0", "China southern Xinjiang Province, China", "35.0", "April 13"], ["7", "7.0", "0", "Japan Ryukyu Islands, Japan", "30.6", "July 18"]]}, "question": "What is the difference in Depth (km) between the earthquake with the highest Magnitude and the earthquake with the lowest Magnitude?", "answer": "582.2"}
{"id": "01029e3c959b99916ea9d73a107a87bf", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["seed", "rank", "player", "points", "points defending", "points won", "new points", "status"], "data": [["1", "1", "rafael nadal", "12070", "2000", "1200", "11270", "runner - up , lost to novak djokovic"], ["2", "2", "novak djokovic", "12005", "720", "2000", "13285", "champion , defeated rafael nadal"], ["3", "3", "roger federer", "9230", "360", "360", "9230", "quarterfinals lost to jo - wilfried tsonga"], ["4", "4", "andy murray", "6855", "720", "720", "6855", "semifinals lost to rafael nadal"], ["5", "5", "robin söderling", "4595", "360", "90", "4325", "third round lost to bernard tomic (q)"], ["6", "7", "tomáš berdych", "3490", "1200", "180", "2470", "fourth round lost to mardy fish"], ["7", "6", "david ferrer", "4150", "180", "180", "4150", "fourth round lost to jo - wilfried tsonga"], ["8", "10", "andy roddick", "2200", "180", "90", "2110", "third round lost to feliciano lópez"], ["9", "8", "gaël monfils", "2780", "90", "90", "2780", "third round lost to łukasz kubot (q)"], ["10", "9", "mardy fish", "2335", "45", "360", "2650", "quarterfinals lost rafael nadal"], ["11", "11", "jürgen melzer", "2175", "180", "90", "2085", "third round lost to xavier malisse"], ["12", "19", "jo - wilfried tsonga", "1585", "360", "720", "1945", "semifinals lost to novak djokovic"], ["13", "12", "viktor troicki", "1930", "45", "45", "1930", "second round lost to lu yen - hsun"], ["14", "14", "stanislas wawrinka", "1900", "10", "45", "1935", "second round lost to simone bolelli (ll)"], ["15", "16", "gilles simon", "1745", "90", "90", "1745", "third round lost to juan martín del potro"], ["16", "15", "nicolás almagro", "1875", "10", "90", "1955", "third round lost to mikhail youzhny"], ["17", "13", "richard gasquet", "1925", "0", "180", "2105", "fourth round lost to andy murray"], ["18", "17", "mikhail youzhny", "1740", "45", "180", "1875", "fourth round lost to roger federer"], ["19", "35", "michaël llodra", "1195", "45", "180", "1330", "fourth round lost vs novak djokovic"], ["20", "18", "florian mayer", "1600", "90", "45", "1555", "second round lost to xavier malisse"], ["21", "23", "fernando verdasco", "1425", "10", "45", "1460", "second round lost to robin haase"], ["22", "21", "alexandr dolgopolov", "1405", "45", "10", "1370", "first round lost to fernando gonzález (pr)"], ["23", "29", "janko tipsarević", "1305", "10", "10", "1305", "first round lost to ivo karlović"], ["24", "22", "juan martín del potro", "1445", "0", "180", "1625", "fourth round lost to rafael nadal"], ["25", "20", "juan ignacio chela", "1475", "10", "45", "1505", "second round lost to alex bogomolov , jr"], ["26", "31", "guillermo garcía - lópez", "1120", "10", "45", "1155", "second round lost to karol beck (q)"], ["27", "26", "marin čilić", "1345", "10", "10", "1345", "first round lost to ivan ljubičić"], ["28", "24", "david nalbandian", "1425", "0", "90", "1515", "third round lost to roger federer"], ["29", "27", "nikolay davydenko", "1330", "45", "10", "1295", "first round lost to bernard tomic (q)"], ["30", "28", "thomaz bellucci", "1305", "90", "10", "1225", "first round lost to rainer schüttler"], ["31", "25", "milos raonic", "1354", "0", "45", "1399", "second round lost to gilles müller (wc)"]]}, "question": "According to the table, which player won the championship and how many points did they win?", "answer": "novak djokovic, 2000"}
{"id": "c174c1729df2ddfe323329b2677741eb", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["sno", "power plant", "state", "commissioned capacity (mw)", "year of commission"], "data": [["1", "baira siul", "himachal pradesh", "180", "1981"], ["2", "loktak", "manipur", "105", "1983"], ["3", "salal - i", "jammu & kashmir", "345", "1987"], ["4", "tanakpur", "uttarakhand", "120", "1992"], ["5", "chamera - i", "himachal pradesh", "540", "1994"], ["6", "salal - ii", "jammu & kashmir", "345", "1996"], ["7", "uri - i", "jammu & kashmir", "480", "1997"], ["8", "rangit", "sikkim", "60", "1999"], ["9", "chamera - ii", "himachal pradesh", "300", "2004"], ["10", "indira sagar", "madhya pradesh", "1000", "2005"], ["11", "dhauliganga - i", "uttarakhand", "280", "2005"], ["12", "dul hasti", "jammu & kashmir", "390", "2007"], ["13", "omkareshwar", "madhya pradesh", "520", "2007"], ["14", "teesta - v", "sikkim", "510", "2008"], ["15", "sewa - ii", "jammu & kashmir", "120", "2010"], ["16", "chamera - iii", "himachal pradesh", "231", "2012"]]}, "question": "What is the correlation between the 'commissioned capacity' and 'year commissioned' in the power plant data? Provide the correlation coefficient as evidence.", "answer": "No correlation, 0.28"}
{"id": "5329a545b17787e7625cddaa07da9250", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["season", "episodes", "timeslot (et)", "season premiere", "season finale", "tv season", "rank", "viewers (in millions)"], "data": [["1", "10", "saturday 8:00 pm", "february 21 , 2004", "august 14 , 2004", "2003 - 2004", "123", "6.21"], ["2", "17", "saturday 8:00 pm", "september 25 , 2004", "august 27 , 2005", "2004 - 2005", "107", "6.41"], ["3", "25", "saturday 8:00 pm", "september 17 , 2005", "august 12 , 2006", "2005 - 2006", "126", "5.74"], ["4", "25", "saturday 8:00 pm", "october 21 , 2006", "august 25 , 2007", "2006 - 2007", "180", "5.12"], ["5", "23", "saturday 8:00 pm", "december 8 , 2007", "august 23 , 2008", "2007 - 2008", "160", "4.69"], ["6", "21", "saturday 8:00 pm", "december 13 , 2008", "august 29 , 2009", "2008 - 2009", "149", "3.8"], ["7", "18", "saturday 8:00 pm", "december 12 , 2009", "august 28 , 2010", "2009 - 2010", "119", "3.55"], ["8", "22", "saturday 8:00 pm", "december 11 , 2010", "august 20 , 2011", "2010 - 2011", "170", "3.53"], ["9", "14", "saturday 8:00 pm", "december 24 , 2011", "august 18 , 2012", "2011 - 2012", "156", "3.46"]]}, "question": "In which season did the TV show have the highest rank?", "answer": "2"}
{"id": "a03bf2136a14c4e3380d552f794aa06c", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["Model", "8A", "8Aa", "8Ab", "8B", "8F"], "data": [["Bore (mm)", "120", "120", "120", "120", "140"], ["Stroke (mm)", "130", "130", "130", "130", "150"], ["Displacement (l)", "11.76", "11.76", "11.76", "18.47", "-"], ["Compression ratio", "4.7", "4.7", "5.3", "5.3", "5.3"], ["Length (m)", "1.19", "1.25", "1.31", "1.36", "1.32"], ["Width (m)", "0.81", "0.83", "0.85", "0.86", "0.89"], ["Height (m)", "0.77", "0.81", "0.87", "0.90", "0.88"], ["Weight(kg)", "195", "215", "230", "236", "256"], ["Power output (hp)", "140", "150", "180", "200/235", "300"], ["at (rpm)", "1900", "2000", "2100", "2300", "2100"]]}, "question": "Which model has a greater difference between its 'Displacement (l)' and 'Weight (kg)', Model 8Ab or Model 8B?", "answer": "8Ab"}
{"id": "c8a0829ce6f11dd2af255ba6d1e54552", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["peak", "country", "elevation (m)", "prominence (m)", "col (m)"], "data": [["mount stanley", "democratic republic of the congo / uganda", "5109", "3951", "1158"], ["mount karisimbi", "rwanda / democratic republic of the congo", "4507", "3312", "1195"], ["kinyeti", "south sudan", "3187", "2120", "1067"], ["emogadong", "south sudan", "2623", "1730", "893"], ["kabobo", "democratic republic of the congo", "2725", "1604", "1121"], ["mont mohi", "democratic republic of the congo", "3480", "1592", "1888"], ["wuhevi", "democratic republic of the congo", "3095", "1570", "1525"], ["mount muhabura", "rwanda / uganda", "4127", "1530", "2597"]]}, "question": "What is the average prominence of mountain peaks in the Democratic Republic of the Congo that have an elevation of at least 3000 meters?", "answer": "2606.25"}
{"id": "e4b977fd1814a0d762ac090b2882b94f", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["rank ( wjc )", "rank (arda)", "metro area", "number of jews (wjc)", "number of jews (asarb)"], "data": [["1", "1", "new york city", "1750000", "2028200"], ["2", "3", "miami", "535000", "337000"], ["3", "2", "los angeles", "490000", "662450"], ["4", "4", "philadelphia", "254000", "285950"], ["5", "6", "chicago", "248000", "265400"], ["6", "8", "san francisco", "210000", "218700"], ["7", "7", "boston", "208000", "261100"]]}, "question": "Which metro area has the highest 'number of jews (wjc)' and what's the difference when it compares to the metro area with the lowest?", "answer": "new york city, 1542000"}
{"id": "133a759ac2cdd5745e7b00c44c094dff", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["rank", "company", "headquarters", "industry", "sales (billion )", "profits (billion )", "assets (billion )", "market value (billion )"], "data": [["1", "citigroup", "usa", "banking", "146.56", "21.54", "1884.32", "247.42"], ["2", "bank of america", "usa", "banking", "116.57", "21.13", "1459.74", "226.61"], ["3", "hsbc", "uk", "banking", "121.51", "16.63", "1860.76", "202.29"], ["4", "general electric", "usa", "conglomerate", "163.39", "20.83", "697.24", "358.98"], ["5", "jpmorgan chase", "usa", "banking", "99.3", "14.44", "1351.52", "170.97"], ["6", "american international group", "usa", "insurance", "113.19", "14.01", "979.41", "174.47"], ["7", "exxonmobil", "usa", "oil and gas", "335.09", "39.5", "223.95", "410.65"], ["8", "royal dutch shell", "netherlands", "oil and gas", "318.85", "25.44", "232.31", "208.25"], ["9", "ubs", "switzerland", "diversified financials", "105.59", "9.78", "1776.89", "116.84"], ["10", "ing group", "netherlands", "diversified financials", "153.44", "9.65", "1615.05", "93.99"], ["11", "bp", "uk", "oil and gas", "265.91", "22.29", "217.6", "198.14"], ["12", "toyota", "japan", "automotive", "179.02", "11.68", "243.6", "217.69"], ["13", "the royal bank of scotland", "uk", "banking", "77.41", "12.51", "1705.35", "124.13"], ["14", "bnp paribas", "france", "banking", "89.16", "9.64", "1898.19", "97.03"], ["15", "allianz", "germany", "insurance", "125.33", "8.81", "1380.88", "87.22"], ["16", "berkshire hathaway", "usa", "diversified financials", "98.54", "11.02", "248.44", "163.79"], ["17", "walmart", "usa", "retailing", "348.65", "11.29", "151.19", "201.36"], ["18", "barclays", "uk", "banking", "67.71", "8.95", "1949.17", "94.79"], ["19", "chevron", "usa", "oil and gas", "195.34", "17.14", "132.63", "149.37"], ["19", "total sa", "france", "oil and gas", "175.05", "15.53", "138.82", "152.62"]]}, "question": "What is the primary driver of a company's `market value (billion)`: its `sales (billion)`, `profits (billion)`, or `assets (billion)`?", "answer": "Profits, with a correlation coefficient of 0.84, are the primary driver of a company's market value, compared to sales (0.53) and assets (-0.41)."}
