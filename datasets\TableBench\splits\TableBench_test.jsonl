{"id": "9fd3d8839e4a861d3caeef557dc78e70", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["district", "2010 population (000)", "2008 gdp (usd bn) a", "2008 gdp per capita (usd) a", "agri culture b", "mining b", "manufac turing b", "services & cons truction b", "exports (usd mn) 2011", "median mo salary (usd) a e", "vehicles (per 1000) d", "income poverty f", "structural poverty g"], "data": [["city of buenos aires", "2890", "118.0", "40828", "0.3", "1.0", "12.9", "85.8", "426", "1618", "528", "7.3", "7.8"], ["buenos aires province", "15625", "161.0", "10303", "4.5", "0.1", "21.3", "74.1", "28134", "1364", "266", "16.2", "15.8"], ["catamarca", "368", "2.331", "6009", "3.6", "20.8", "12.1", "63.5", "1596", "1241", "162", "24.3", "21.5"], ["chaco", "1055", "2.12", "2015", "12.6", "0.0", "7.5", "79.9", "602", "1061", "137", "35.4", "33.0"], ["chubut", "509", "7.11", "15422", "6.9", "21.3", "10.0", "61.8", "3148", "2281", "400", "4.6", "15.5"], ["córdoba", "3309", "33.239", "10050", "10.6", "0.2", "14.0", "75.2", "10635", "1200", "328", "14.8", "13.0"], ["corrientes", "993", "4.053", "4001", "12.6", "0.0", "8.2", "79.2", "230", "1019", "168", "31.5", "28.5"], ["entre ríos", "1236", "7.137", "5682", "11.9", "0.3", "11.6", "76.2", "1908", "1063", "280", "13.0", "17.6"], ["formosa", "530", "1.555", "2879", "7.6", "1.5", "6.4", "84.5", "40", "1007", "107", "30.7", "33.6"], ["jujuy", "673", "2.553", "3755", "5.5", "0.7", "14.6", "79.2", "456", "1123", "153", "30.0", "28.8"], ["la pampa", "319", "2.0", "5987", "19.0", "3.7", "5.3", "72.0", "378", "1164", "364", "13.6", "10.3"], ["la rioja", "334", "1.419", "4162", "3.9", "0.1", "16.8", "79.2", "281", "1040", "172", "22.0", "20.4"], ["mendoza", "1739", "18.8", "10758", "5.4", "6.1", "17.5", "71.0", "1862", "1153", "313", "12.2", "15.4"], ["misiones", "1102", "4.044", "3751", "6.7", "0.0", "13.0", "80.3", "536", "971", "153", "32.6", "27.1"], ["neuquén", "551", "14.398", "26273", "0.7", "42.8", "2.9", "53.6", "353", "2211", "339", "11.2", "17.0"], ["río negro", "639", "4.924", "8247", "4.9", "8.4", "7.3", "79.4", "670", "1309", "285", "20.6", "17.9"], ["salta", "1214", "5.165", "4220", "8.1", "7.6", "10.4", "73.9", "1332", "1045", "139", "29.7", "31.6"], ["san juan", "681", "3.927", "5642", "8.1", "0.3", "15.9", "75.7", "2470", "1293", "216", "18.4", "17.4"], ["san luis", "432", "2.444", "5580", "4.9", "0.5", "42.4", "52.2", "735", "1288", "245", "22.0", "15.6"], ["santa cruz", "274", "6.892", "30496", "4.4", "47.1", "2.3", "46.2", "1857", "2646", "432", "3.6", "10.4"], ["santa fe", "3195", "37.5", "10670", "10.1", "0.0", "17.4", "72.5", "17804", "1265", "299", "18.2", "14.8"], ["santiago del estero", "874", "2.598", "3003", "11.5", "0.1", "6.2", "82.2", "1082", "945", "103", "31.0", "31.3"], ["tierra del fuego", "127", "2.606", "20682", "4.7", "18.5", "18.6", "58.2", "443", "2267", "478", "6.4", "14.1"], ["tucumán", "1448", "5.807", "3937", "6.0", "0.1", "12.6", "81.3", "1031", "973", "146", "27.7", "23.9"]]}, "question": "How many districts have a 2008 GDP per capita (USD) above 6700?", "answer": "10"}
{"id": "1b9948ab23157ac39233152f4b88fba6", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["Unnamed: 0", "Average population (x 1000)", "Live births", "Deaths", "Natural change", "Crude birth rate (per 1000)", "Crude death rate (per 1000)", "Natural change (per 1000)"], "data": [["1970", "38", "761", "299", "462", "20.0", "7.9", "12.2"], ["1975", "42", "857", "317", "540", "20.4", "7.5", "12.9"], ["1980", "46", "996", "333", "663", "21.7", "7.2", "14.4"], ["1985", "51", "1 104", "370", "734", "21.6", "7.3", "14.4"], ["1990", "51", "842", "360", "482", "16.4", "7.0", "9.4"], ["1991", "50", "789", "335", "454", "15.8", "6.7", "9.1"], ["1992", "48", "692", "401", "291", "14.4", "8.3", "6.0"], ["1993", "46", "617", "448", "169", "13.4", "9.7", "3.7"], ["1994", "44", "585", "518", "67", "13.3", "11.8", "1.5"], ["1995", "43", "537", "501", "36", "12.6", "11.8", "0.8"], ["1996", "42", "486", "441", "45", "11.7", "10.6", "1.1"], ["1997", "41", "483", "374", "109", "11.9", "9.2", "2.7"], ["1998", "40", "498", "368", "130", "12.6", "9.3", "3.3"], ["1999", "39", "448", "376", "72", "11.6", "9.7", "1.9"], ["2000", "38", "460", "438", "22", "12.0", "11.4", "0.6"], ["2001", "39", "562", "438", "124", "14.5", "11.3", "3.2"], ["2002", "39", "608", "397", "211", "15.5", "10.1", "5.4"], ["2003", "39", "625", "386", "239", "15.9", "9.8", "6.1"], ["2004", "39", "637", "345", "292", "16.5", "8.9", "7.6"], ["2005", "38", "548", "369", "179", "14.5", "9.7", "4.7"], ["2006", "37", "540", "347", "193", "14.5", "9.3", "5.2"]]}, "question": "How many years had a natural change of more than 150 and death of less than 350?", "answer": "6"}
{"id": "d5edf188f93efcfec0bcbc664b3b8445", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["rocket", "country", "type", "launches", "successes", "failures", "partial failures"], "data": [["ariane 5eca", "europe", "ariane 5", "6", "6", "0", "0"], ["atlas v 401", "united states", "atlas v", "1", "1", "0", "0"], ["atlas v 501", "united states", "atlas v", "2", "2", "0", "0"], ["atlas v 531", "united states", "atlas v", "1", "1", "0", "0"], ["delta ii 7420", "united states", "delta ii", "1", "1", "0", "0"], ["delta iv - m + (4 , 2)", "united states", "delta iv", "2", "2", "0", "0"], ["delta iv - h", "united states", "delta iv", "1", "1", "0", "0"], ["dnepr - 1", "ukraine", "dnepr", "3", "3", "0", "0"], ["falcon 9", "united states", "falcon 9", "2", "2", "0", "0"], ["gslv mk i (c)", "india", "gslv", "1", "0", "1", "0"], ["gslv mk ii", "india", "gslv", "1", "0", "1", "0"], ["h - iia 202", "japan", "h - iia", "2", "2", "0", "0"], ["kosmos - 3 m", "russia", "kosmos", "1", "1", "0", "0"], ["long march 2d", "china", "long march 2", "3", "3", "0", "0"], ["long march 3a", "china", "long march 3", "3", "3", "0", "0"], ["long march 3b", "china", "long march 3", "1", "1", "0", "0"], ["long march 3c", "china", "long march 3", "4", "4", "0", "0"], ["long march 4b", "china", "long march 4", "1", "1", "0", "0"], ["long march 4c", "china", "long march 4", "3", "3", "0", "0"], ["minotaur iv", "united states", "minotaur iv", "1", "1", "0", "0"], ["minotaur iv / haps", "united states", "minotaur iv", "1", "1", "0", "0"], ["molniya - m / 2bl", "russia", "molniya", "1", "1", "0", "0"], ["naro - 1", "russia south korea", "naro", "1", "0", "1", "0"], ["proton - m / dm - 2", "russia", "proton", "2", "2", "0", "0"], ["proton - m / dm - 03", "russia", "proton", "1", "0", "1", "0"], ["proton - m / briz - m", "russia", "proton", "9", "9", "0", "0"], ["pslv - ca", "india", "pslv", "1", "1", "0", "0"], ["rokot / briz - km", "russia", "ur - 100", "2", "2", "0", "0"], ["shavit - 2", "israel", "shavit", "1", "1", "0", "0"], ["soyuz - 2.1a / fregat", "russia", "soyuz", "2", "2", "0", "0"], ["soyuz - u", "russia", "soyuz", "6", "6", "0", "0"], ["soyuz - fg", "russia", "soyuz", "4", "4", "0", "0"], ["space shuttle", "united states", "space shuttle", "3", "3", "0", "0"]]}, "question": "Did the introduction of the \"Falcon 9\" rocket type lead to an positive, negative, or no clear impact in the overall success rate of launches by the United States?", "answer": "Positive impact"}
{"id": "101130aa9241715f197257e7a2821303", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["Club", "Season", "League", "League", "League", "National Cup", "National Cup", "League Cup", "League Cup", "Europe", "Europe", "Total", "Total"], "data": [["Club", "Season", "Division", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals", "Apps", "Goals"], ["Liverpool", "1990–91", "First Division", "2", "0", "1", "0", "0", "0", "0", "0", "3", "0"], ["Liverpool", "1991–92", "First Division", "30", "5", "8", "3", "5", "3", "8", "0", "51", "11"], ["Liverpool", "1992–93", "Premier League", "31", "4", "1", "0", "5", "2", "3", "1", "40", "7"], ["Liverpool", "1993–94", "Premier League", "30", "2", "2", "0", "2", "0", "0", "0", "34", "2"], ["Liverpool", "1994–95", "Premier League", "40", "7", "7", "0", "8", "2", "0", "0", "55", "9"], ["Liverpool", "1995–96", "Premier League", "38", "6", "7", "2", "4", "1", "4", "1", "53", "10"], ["Liverpool", "1996–97", "Premier League", "37", "7", "2", "0", "4", "2", "8", "1", "51", "10"], ["Liverpool", "1997–98", "Premier League", "36", "11", "1", "0", "5", "0", "4", "1", "46", "12"], ["Liverpool", "1998–99", "Premier League", "28", "4", "0", "0", "0", "0", "3", "1", "31", "5"], ["Liverpool", "Liverpool Total", "Liverpool Total", "272", "46", "29", "5", "33", "10", "30", "5", "364", "66"], ["Real Madrid", "1999–2000", "La Liga", "30", "3", "10", "0", "0", "0", "7", "1", "47", "4"], ["Real Madrid", "2000–01", "La Liga", "26", "2", "6", "0", "0", "0", "10", "0", "42", "2"], ["Real Madrid", "2001–02", "La Liga", "23", "2", "2", "0", "0", "0", "13", "2", "38", "4"], ["Real Madrid", "2002–03", "La Liga", "15", "1", "4", "1", "0", "0", "6", "2", "25", "4"], ["Real Madrid", "Real Madrid Total", "Real Madrid Total", "94", "8", "22", "1", "0", "0", "36", "5", "152", "14"], ["Manchester City", "2003–04", "Premier League", "22", "0", "3", "0", "1", "0", "4", "0", "30", "0"], ["Manchester City", "2004–05", "Premier League", "13", "0", "1", "0", "0", "0", "0", "0", "14", "0"], ["Manchester City", "Manchester City Total", "Manchester City Total", "35", "0", "4", "0", "1", "0", "4", "0", "44", "0"], ["Career Total", "Career Total", "Career Total", "401", "54", "52", "6", "37", "10", "70", "10", "560", "80"]]}, "question": "How many seasons did Liverpool play in the Premier League?", "answer": "7"}
{"id": "be0a8690532485156b570f11e933f6fe", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["rank in nyagatare sectors , 2012", "sector", "area in sqkm", "population august 15 , 2012", "population , august 15 , 2002", "population change 2002 - 2012 (%)", "population density 2012 (km 2 )"], "data": [["8", "gatunda", "52", "27879", "19716", "41.4", "535"], ["10", "karama", "53", "26727", "19727", "35.5", "499"], ["2", "karangazi", "563", "56871", "21234", "167.8", "101"], ["4", "katabagemu", "98", "34651", "22101", "56.8", "354"], ["14", "kiyombe", "69", "17061", "16483", "3.5", "247"], ["11", "matimba", "79", "24168", "13476", "79.3", "307"], ["9", "mimuli", "48", "27366", "22452", "21.9", "573"], ["12", "mukama", "64", "21819", "17970", "21.4", "339"], ["7", "musheli", "96", "32403", "14742", "119.8", "338"], ["3", "nyagatare", "164", "52125", "19475", "167.7", "317"], ["5", "rukomo", "58", "34377", "20945", "64.1", "588"], ["13", "rwempasha", "169", "19328", "11428", "69.1", "115"], ["1", "rwimiyaga", "309", "58847", "16802", "250.2", "190"], ["6", "tabagwe", "106", "33322", "18533", "79.6", "313"]]}, "question": "What is the correlation between the 'area in sqkm' and 'population density 2012 (km 2)' in the Nyagatare sectors? Provide the correlation coefficient as evidence.", "answer": "Weak negative correlation, -0.68"}
{"id": "7798dced750cb1cec4f868390ffc17b5", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["decimal32", "decimal64", "decimal128", "decimal(32k)", "Format"], "data": [["1", "1", "1", "1", "Sign field (bits)"], ["5", "5", "5", "5", "Combination field (bits)"], ["6", "8", "12", "w = 2×k + 4", "Exponent continuation field (bits)"], ["20", "50", "110", "t = 30×k−10", "Coefficient continuation field (bits)"], ["32", "64", "128", "32×k", "Total size (bits)"], ["7", "16", "34", "p = 3×t/10+1 = 9×k−2", "Coefficient size (decimal digits)"], ["192", "768", "12288", "3×2w = 48×4k", "Exponent range"], ["96", "384", "6144", "Emax = 3×2w−1", "Largest value is 9.99...×10Emax"], ["−95", "−383", "−6143", "Emin = 1−Emax", "Smallest normalized value is 1.00...×10Emin"], ["−101", "−398", "−6176", "Etiny = 2−p−Emax", "Smallest non-zero value is 1×10Etiny"]]}, "question": "Can you identify any data points in the table that significantly deviate from the expected pattern?", "answer": "No anomalies are detected in the table."}
{"id": "876647763592d2d08384449540eb212d", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["crime", "reported offenses", "killeen rate", "texas rate", "us rate"], "data": [["murder", "10", "8.6", "5.6", "5.6"], ["rape", "66", "56.9", "32.9", "29.4"], ["robbery", "216", "186.4", "155.2", "154.0"], ["aggravated assault", "593", "511.6", "314.4", "281.6"], ["violent crime", "885", "763.5", "508.2", "470.6"], ["burglary", "1711", "1476.2", "946.5", "743.4"], ["larceny - theft", "2877", "2482.2", "2688.9", "2200.1"], ["motor vehicle theft", "169", "145.8", "351.1", "330.5"], ["non - violent crime", "4757", "4104.2", "3986.6", "3274.0"]]}, "question": "How many more reported offenses of 'larceny - theft' were there compared to 'burglary'?", "answer": "1166"}
{"id": "c5b41b1733a460472e3d1bc744be96d1", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["ballarat fl", "wins", "byes", "losses", "draws", "against"], "data": [["sunbury", "16", "1", "1", "0", "1022"], ["melton south", "12", "2", "4", "0", "1191"], ["redan", "12", "2", "4", "0", "974"], ["lake wendouree", "12", "2", "4", "0", "1127"], ["daylesford", "11", "2", "5", "0", "1109"], ["darley", "11", "2", "5", "0", "1230"], ["ballarat", "5", "2", "11", "0", "1665"], ["melton", "4", "2", "12", "0", "1638"], ["sebastapol", "3", "1", "14", "0", "1802"], ["east point", "2", "1", "15", "0", "2090"], ["bacchus marsh", "2", "1", "15", "0", "2375"]]}, "question": "What is the mean and standard deviation of the `against` column?", "answer": "1474.81, 470.08"}
{"id": "b1ea3f96d887432df9bb3aa3bcf800d6", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Ethnicity", "1880", "1899", "1913", "19301", "1956", "1966", "1977", "1992", "2002"], "data": [["All", "139,671", "258,242", "380,430", "437,131", "593,659", "702,461", "863,348", "1,019,766", "971,643"], ["Romanian", "43,671 (31%)", "118,919 (46%)", "216,425 (56.8%)", "282,844 (64.7%)", "514,331 (86.6%)", "622,996 (88.7%)", "784,934 (90.9%)", "926,608 (90.8%)", "883,620 (90.9%)"], ["Bulgarian", "24,915 (17%)", "38,439 (14%)", "51,149 (13.4%)", "42,070 (9.6%)", "749 (0.13%)", "524 (0.07%)", "415 (0.05%)", "311 (0.03%)", "135 (0.01%)"], ["Turkish", "18,624 (13%)", "12,146 (4%)", "20,092 (5.3%)", "21,748 (5%)", "11,994 (2%)", "16,209 (2.3%)", "21,666 (2.5%)", "27,685 (2.7%)", "27,580 (2.8%)"], ["Tatar", "29,476 (21%)", "28,670 (11%)", "21,350 (5.6%)", "15,546 (3.6%)", "20,239 (3.4%)", "21,939 (3.1%)", "22,875 (2.65%)", "24,185 (2.4%)", "23,409 (2.4%)"], ["Russian-Lipovan", "8,250 (6%)", "12,801 (5%)", "35,859 (9.4%)", "26,210 (6%)²", "29,944 (5%)", "30,509 (4.35%)", "24,098 (2.8%)", "26,154 (2.6%)", "21,623 (2.2%)"], ["Ruthenian\n(Ukrainian from 1956)", "455 (0.3%)", "13,680 (5%)", "35,859 (9.4%)", "33 (0.01%)", "7,025 (1.18%)", "5,154 (0.73%)", "2,639 (0.3%)", "4,101 (0.4%)", "1,465 (0.1%)"], ["Dobrujan Germans", "2,461 (1.7%)", "8,566 (3%)", "7,697 (2%)", "12,023 (2.75%)", "735 (0.12%)", "599 (0.09%)", "648 (0.08%)", "677 (0.07%)", "398 (0.04%)"], ["Greek", "4,015 (2.8%)", "8,445 (3%)", "9,999 (2.6%)", "7,743 (1.8%)", "1,399 (0.24%)", "908 (0.13%)", "635 (0.07%)", "1,230 (0.12%)", "2,270 (0.23%)"], ["Roma", "702 (0.5%)", "2,252 (0.87%)", "3,263 (0.9%)", "3,831 (0.88%)", "1,176 (0.2%)", "378 (0.05%)", "2,565 (0.3%)", "5,983 (0.59%)", "8,295 (0.85%)"]]}, "chart_type": "bar", "question": "Please help me draw a percentage stacked bar chart that shows the proportion of different ethnic populations over time.", "answer": "y_references = [[31, 46, 56.8, 64.7, 86.6, 88.7, 90.9, 90.8, 90.9], [17, 14, 13.4, 9.6, 0.13, 0.07, 0.05, 0.03, 0.01], [13, 4, 5.3, 5, 2, 2.3, 2.5, 2.7, 2.8], [21, 11, 5.6, 3.6, 3.4, 3.1, 2.65, 2.4, 2.4], [6, 5, 9.4, 6, 5, 4.35, 2.8, 2.6, 2.2], [0.3, 5, 9.4, 0.01, 1.18, 0.73, 0.3, 0.4, 0.1], [1.7, 3, 2, 2.75, 0.12, 0.09, 0.08, 0.07, 0.04], [2.8, 3, 2.6, 1.8, 0.24, 0.13, 0.07, 0.12, 0.23], [0.5, 0.87, 0.9, 0.88, 0.2, 0.05, 0.3, 0.59, 0.85]]"}
{"id": "c1657743b6eeb5b20e41af290a3dad55", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["ensemble", "gold medals", "silver medals", "bronze medals", "total medals"], "data": [["amador valley hs", "0", "1", "0", "1"], ["ayala high school", "4", "2", "1", "7"], ["baldwinsville hs", "2", "0", "0", "2"], ["claremont hs", "1", "1", "0", "2"], ["downers grove hs", "0", "0", "1", "1"], ["father ryan hs", "0", "1", "0", "1"], ["fort mill hs", "2", "1", "2", "5"], ["franklin central hs", "6", "0", "0", "6"], ["gateway high school", "2", "1", "1", "4"], ["goshen hs", "0", "2", "1", "3"], ["harrison central paragon hs", "0", "0", "1", "1"], ["james logan high school", "1", "1", "0", "2"], ["john overton hs", "0", "1", "2", "3"], ["king philip high school", "0", "1", "0", "1"], ["mansfield hs", "0", "1", "0", "1"], ["mission viejo high school", "0", "1", "0", "1"], ["muscle shoals hs", "1", "1", "2", "4"], ["new philadelphia hs", "0", "1", "0", "1"], ["northglenn hs", "0", "0", "1", "1"], ["rangeview hs", "0", "1", "0", "1"], ["roland hayes school", "0", "0", "1", "1"], ["tarpon springs hs", "0", "1", "0", "1"], ["tunstall hs", "0", "3", "4", "7"], ["warsaw community hs", "0", "0", "1", "1"], ["woodbridge hs", "1", "0", "0", "1"]]}, "question": "How many more total medals did the school with the highest total medals win than the school with the lowest total medals?", "answer": "6"}
{"id": "c77daa5488bbd256403af096bfea88b2", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["ward", "bello", "ben - tahir", "doucet", "furtenbacher", "gauthier", "haydon", "larter", "lawrance", "libweshya", "liscumb"], "data": [["orlãans", "51", "27", "1918", "14", "132", "939", "18", "27", "6", "6"], ["innes", "41", "11", "1466", "11", "105", "638", "10", "7", "7", "5"], ["barrhaven", "36", "32", "1267", "6", "26", "1305", "10", "15", "4", "3"], ["kanata north", "23", "23", "1222", "14", "14", "704", "12", "9", "3", "2"], ["west carleton - march", "6", "5", "958", "2", "10", "909", "3", "8", "2", "1"], ["stittsville", "9", "7", "771", "1", "9", "664", "2", "8", "2", "1"], ["bay", "37", "68", "2009", "20", "38", "1226", "20", "21", "8", "8"], ["college", "40", "32", "2112", "13", "22", "1632", "7", "15", "6", "10"], ["knoxdale - merivale", "33", "47", "1583", "17", "17", "1281", "11", "12", "4", "3"], ["gloucester - southgate", "84", "62", "1378", "25", "39", "726", "15", "20", "12", "8"], ["beacon hill - cyrville", "70", "24", "1297", "7", "143", "592", "7", "10", "1", "6"], ["rideau - vanier", "66", "24", "2148", "15", "261", "423", "11", "14", "11", "4"], ["rideau - rockcliffe", "68", "48", "1975", "15", "179", "481", "11", "19", "8", "6"], ["somerset", "47", "33", "2455", "17", "45", "326", "15", "18", "12", "1"], ["kitchissippi", "39", "21", "3556", "12", "21", "603", "10", "10", "3", "6"], ["river", "52", "57", "1917", "16", "31", "798", "11", "13", "6", "4"], ["capital", "40", "20", "4430", "18", "34", "369", "8", "7", "7", "5"], ["alta vista", "58", "89", "2114", "12", "74", "801", "8", "15", "5", "2"], ["cumberland", "39", "32", "1282", "12", "135", "634", "8", "8", "5", "5"], ["osgoode", "15", "2", "769", "8", "22", "768", "5", "11", "1", "4"], ["rideau - goulbourn", "7", "4", "898", "11", "15", "1010", "1", "7", "1", "4"], ["gloucester - south nepean", "36", "35", "976", "9", "23", "721", "10", "6", "5", "5"], ["kanata south", "29", "26", "1646", "24", "18", "1354", "6", "20", "3", "5"], ["ward", "lyrette", "maguire", "o'brien", "pita", "ryan", "st arnaud", "scharf", "taylor", "watson", "wright"], ["orlãans", "14", "332", "3937", "8", "27", "17", "84", "52", "8685", "14"], ["innes", "5", "229", "2952", "9", "26", "11", "44", "35", "6746", "11"], ["barrhaven", "3", "394", "3335", "14", "20", "4", "46", "46", "5943", "19"], ["kanata north", "3", "209", "2612", "10", "8", "3", "35", "44", "4516", "15"], ["west carleton - march", "1", "297", "3072", "2", "13", "3", "28", "28", "2746", "88"], ["stittsville", "2", "265", "2884", "10", "7", "6", "33", "15", "3195", "8"], ["bay", "9", "299", "3221", "8", "16", "9", "82", "96", "7220", "19"], ["college", "4", "378", "4249", "14", "28", "8", "68", "83", "7668", "21"], ["knoxdale - merivale", "8", "301", "3269", "14", "20", "1", "43", "47", "5540", "18"], ["gloucester - southgate", "7", "288", "3006", "16", "24", "17", "46", "39", "6107", "13"], ["beacon hill - cyrville", "9", "239", "2329", "20", "11", "15", "59", "39", "5484", "7"], ["rideau - vanier", "17", "129", "1503", "10", "11", "17", "58", "58", "5784", "21"], ["rideau - rockcliffe", "18", "139", "1729", "16", "13", "17", "55", "42", "5850", "27"], ["somerset", "8", "126", "1393", "12", "16", "12", "59", "80", "5164", "21"], ["kitchissippi", "6", "211", "2389", "13", "10", "9", "56", "80", "7034", "22"], ["river", "9", "312", "2875", "20", "13", "8", "53", "69", "6539", "27"], ["capital", "5", "140", "1436", "12", "6", "10", "35", "52", "6543", "14"], ["alta vista", "9", "265", "2672", "13", "15", "8", "52", "60", "6666", "22"], ["cumberland", "11", "296", "3203", "6", "25", "7", "53", "40", "6371", "12"], ["osgoode", "6", "441", "3039", "6", "9", "1", "48", "27", "2844", "11"], ["rideau - goulbourn", "2", "649", "3556", "6", "10", "3", "36", "19", "3359", "8"], ["gloucester - south nepean", "8", "247", "2372", "12", "13", "4", "33", "36", "4759", "11"]]}, "question": "Which ward has a value of 66 in the \"bello\" column?", "answer": "rideau - vanier"}
{"id": "676e45fc03dc0b9f312ff42d35bda0d1", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["Year", "Competition", "Venue", "Position", "Event", "Notes"], "data": [["Representing Great Britain", "Representing Great Britain", "Representing Great Britain", "Representing Great Britain", "Representing Great Britain", "Representing Great Britain"], ["2009", "World Youth Championships", "Brixen, Italy", "1st", "100 m", "11.39"], ["2009", "World Youth Championships", "Brixen, Italy", "1st", "200 m", "23.08"], ["2010", "World Junior Championships", "Moncton, New Brunswick, Canada", "1st", "100m", "11.40 (wind: -0.7 m/s)"], ["2010", "World Junior Championships", "Moncton, New Brunswick, Canada", "2nd", "200m", "23.19 (wind: -0.5 m/s)"], ["2010", "World Junior Championships", "Moncton, New Brunswick, Canada", "—", "4 × 100 m relay", "DNF"], ["2011", "European Indoor Championships", "Paris, France", "4th", "60 m", "7.21"], ["2011", "European Junior Championships", "Tallinn, Estonia", "1st", "100 m", "11.18"], ["2011", "European Junior Championships", "Tallinn, Estonia", "1st", "200 m", "22.94"], ["2011", "European Junior Championships", "Tallinn, Estonia", "3rd", "4 × 100 m", "45.00"], ["2012", "World Indoor Championships", "Istanbul, Turkey", "16th (sf)", "60 m", "7.32"], ["2013", "European U23 Championships", "Tampere, Finland", "2nd", "100 m", "11.42 (wind: -0.7 m/s)"], ["2013", "European U23 Championships", "Tampere, Finland", "1st", "200 m", "22.92 (wind: -0.5 m/s)"], ["2013", "European U23 Championships", "Tampere, Finland", "2nd", "4 × 100 m", "43.83"], ["2013", "World Championships", "Moscow, Russia", "semi-final", "200 m", "23.21"], ["2014", "Commonwealth Games", "Glasgow, Scotland", "2nd", "200 m", "22.50"], ["2014", "Commonwealth Games", "Glasgow, Scotland", "3rd", "4 × 100 m relay", "43.10"], ["2014", "European Championships", "Zurich, Switzerland", "2nd", "200 m", "22.46"], ["2014", "European Championships", "Zurich, Switzerland", "1st", "4 × 100 m relay", "42.25 NR"], ["2015", "World Championships", "Beijing, China", "4th", "4 × 100 m relay", "42.10"], ["2016", "Olympic Games", "Rio de Janeiro, Brazil", "22nd (sf)", "200 m", "22.99"], ["2018", "European Championships", "Berlin, Germany", "13th (sf)", "200 m", "23.28"]]}, "question": "In which year did the athlete win the gold medal in the 200m event at the European Junior Championships?", "answer": "2011"}
{"id": "f557ff1c99aaf41e253a7295f416c91a", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["rank", "airport", "passengers", "aircraft movements", "carriers"], "data": [["1", "shanghai , china", "192701", "1465", "china eastern airlines , jin air"], ["2", "osaka , japan", "131338", "1157", "jeju air , korean air"], ["3", "tokyo , japan", "124296", "734", "korean air"], ["4", "beijing , china", "97055", "768", "china eastern airlines , korean air"], ["5", "taipei , republic of china (taiwan)", "73754", "585", "jin air , transasia airways"], ["6", "ningbo , china", "44067", "303", "china eastern airlines , eastar jet"], ["7", "nagoya , japan", "41460", "416", "korean air"], ["8", "harbin , china", "31574", "201", "china southern airlines , jin air"], ["9", "changchun , china", "29129", "214", "china southern airlines"], ["10", "fukuoka , japan", "27592", "306", "asiana airlines"], ["11", "shenyang , china", "26168", "238", "china southern airlines"], ["12", "dalian , china", "25359", "204", "china southern airlines"], ["13", "hong kong", "24940", "208", "dragonair"], ["14", "hangzhou , china", "22191", "165", "china eastern airlines"], ["15", "macau", "21278", "178", "eastar jet"], ["16", "nanning , china", "17114", "122", "eastar jet"], ["17", "xi'an , china", "15022", "107", "jin air"], ["18", "guangzhou , china", "14983", "95", "korean air"], ["19", "hefei , china", "14226", "105", "eastar jet"], ["20", "changsha , china", "12947", "105", "eastar jet"]]}, "question": "What is the median number of aircraft movements among the top 10 busiest airports in the table?", "answer": "500.5"}
{"id": "466d38cce925e5567977bc108dffbcc4", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["Rank", "Region", "GDP (€, billions)", "GDP (% of national total)", "GDP per capita (€)", "GDP per capita (PPS)", "GDP per capita (PPS, EU28=100)"], "data": [["0", "a", "0", "0", "0", "0", "0"], ["1", "Attica", "85.285", "47.3", "22,700", "27,300", "91"], ["2", "Central Macedonia", "24.953", "13.8", "13,300", "16,000", "53"], ["3", "Thessaly", "9.437", "5.2", "13,000", "15,700", "52"], ["4", "Crete", "8.962", "5.0", "14,200", "17,000", "57"], ["5", "Central Greece", "8.552", "4.7", "15,400", "18,500", "62"], ["6", "Western Greece", "8.164", "4.5", "12,300", "14,900", "49"], ["7", "Peloponnese", "8.144", "4.5", "14,100", "17,000", "56"], ["8", "Eastern Macedonia and Thrace", "6.939", "3.9", "11,500", "13,900", "46"], ["9", "South Aegean", "6.114", "3.4", "18,000", "21,700", "72"], ["10", "Western Macedonia", "4.010", "2.2", "14,800", "17,900", "59"], ["11", "Epirus", "4.001", "2.2", "12,000", "14,400", "48"], ["12", "Ionian Islands", "3.159", "1.8", "15,400", "18,600", "62"], ["13", "North Aegean", "2.498", "1.4", "12,000", "14,500", "48"], ["-", "-", "-", "-", "-", "-", "-"], ["–", "Greece", "180.218", "100", "16,800", "20,200", "67"], ["-", "-", "-", "-", "-", "-", "-"], ["–", "European Union", "15,383.066", "8535.8", "30,000", "30,000", "100"], ["100", "z", "1000000000000000", "1000", "100", "1000000000", "1000"]]}, "question": "How does the GDP per capita (€) change with increasing GDP (€, billions) for regions with a GDP (% of national total) above 5%?", "answer": "GDP per capita (€) shows a strong positive correlation (0.80) with increasing regional GDP (€, billions) for regions where GDP exceeds 5% of the national total."}
{"id": "6f14bb8e38c0ab01f17ae3f61cf3b0dc", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["autonomous community", "hydroelectric power", "wind power", "solar power", "biomass power", "solid waste power", "total renewable generation", "total electricity demand", "% renewable of total electricity demand"], "data": [["castile and leã cubicn", "6960", "3840", "14", "274", "87", "11175", "15793", "70.8%"], ["galicia", "7561", "5970", "1", "242", "317", "14091", "20279", "69.5%"], ["la rioja", "124", "897", "1", "3", "2", "1027", "1860", "55.2%"], ["aragã cubicn", "3073", "3342", "1", "63", "8", "6487", "11885", "54.6%"], ["navarre", "379", "2248", "28", "269", "0", "2924", "5401", "54.1%"], ["extremadura", "2244", "0", "1", "0", "0", "2245", "5076", "44.2%"], ["castile - la mancha", "710", "3935", "8", "99", "34", "4786", "12686", "37.7%"], ["asturias", "1680", "357", "0", "221", "400", "2658", "12391", "21.5%"], ["cantabria", "875", "0", "0", "11", "41", "927", "5693", "16.3%"], ["catalonia", "3223", "301", "7", "77", "241", "3849", "48498", "7.9%"], ["andalusia", "946", "1042", "5", "728", "0", "2721", "40737", "6.7%"], ["basque country", "336", "339", "3", "55", "326", "1059", "20934", "5.1%"], ["valencia", "1041", "266", "13", "55", "0", "1375", "27668", "5.0%"], ["canary islands", "0", "288", "0", "0", "0", "288", "9372", "3.1%"], ["balearic islands", "0", "5", "0", "0", "133", "138", "6235", "2.2%"], ["murcia", "65", "93", "6", "12", "0", "176", "8334", "2.1%"], ["madrid", "83", "0", "8", "58", "330", "479", "30598", "1.6%"], ["ceuta & melilla", "0", "0", "0", "0", "2", "2", "391", "0.5%"]]}, "question": "What is the correlation between 'total renewable generation' and 'total electricity demand' across Spanish autonomous communities? Provide the correlation coefficient as evidence.", "answer": "No correlation, 0.17"}
{"id": "e7b71d1c7427df2a8dd74f7b599ff66e", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["party", "pr seats", "district seats", "total elected 2001", "total seats"], "data": [["liberal democratic party", "20", "45", "65", "111"], ["democratic party", "8", "18", "26", "59"], ["new komeito party", "8", "5", "13", "23"], ["liberal party", "4", "2", "6", "8"], ["communist party", "4", "1", "5", "20"], ["social democratic party", "3", "0", "3", "8"], ["new conservative party", "1", "0", "1", "5"], ["others", "0", "2", "2", "2"], ["independents", "0", "0", "0", "4"], ["total", "48", "73", "121", "247"]]}, "question": "What is the total number of PR seats won by all parties combined?", "answer": "48"}
{"id": "285c59f7f980d49b638f546b5d11d7ef", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["country / territory", "area (km square)", "population", "pop density ( / km square)", "gdp millions of usd (2009)", "gdp per capita usd (2009 - 2011)", "capital"], "data": [["american samoa", "199", "55519", "326", "537", "7874", "pago pago"], ["australia", "7617930", "23154782", "3", "1515468", "41500", "canberra"], ["brunei", "5765", "407000", "70", "14700", "36700", "bandar seri begawan"], ["cambodia", "181035", "14805000", "82", "10900", "800", "phnom penh"], ["china", "9671018", "1339530000", "138", "7203784", "6076", "beijing"], ["hong kong", "1104", "7055071", "6390", "210730", "30000", "hong kong"], ["indonesia", "1904569", "*********", "126", "514900", "2200", "jakarta"], ["japan", "377944", "*********", "337", "5870357", "39700", "tokyo"], ["north korea", "120540", "23906000", "198", "27820", "1200", "pyongyang"], ["south korea", "100140", "50062000", "500", "800300", "20000", "seoul"], ["laos", "236800", "6320000", "27", "5721", "900", "vientiane"], ["macau", "29", "541200", "18662", "36428", "39800", "macau"], ["malaysia", "329847", "28318000", "86", "191399", "7525", "kuala lumpur"], ["mongolia", "1564116", "2736800", "2", "4212", "1500", "ulan bator"], ["burma", "676578", "50496000", "74", "26820", "500", "naypyidaw"], ["new zealand", "268021", "4357437", "16", "109600", "25500", "wellington"], ["papua new guinea", "462840", "6732000", "15", "8200", "1200", "port moresby"], ["philippines", "299764", "91983000", "307", "158700", "1700", "manila"], ["singapore", "710", "5183700", "7023", "177133", "35500", "city of singapore"], ["taiwan", "36191", "23119772", "639", "466054", "20328", "taipei"], ["thailand", "513120", "67764000", "132", "263510", "3900", "bangkok"], ["timor - leste", "14874", "1171000", "76", "599", "500", "dili"]]}, "question": "What is the capital of Australia, according to the table?", "answer": "canberra"}
{"id": "6861b3d742e8183a3955590530e6c805", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["Name", "Title", "Start", "End"], "data": [["William J. Porter", "Chargé d'Affaires", "1956", "1956"], ["Cavendish W. Cannon", "Ambassador", "1956", "1958"], ["Charles Yost", "Ambassador", "1958", "1961"], ["Philip W. Bonsal", "Ambassador", "1961", "1962"], ["John H. Ferguson", "Ambassador", "1962", "1964"], ["Henry J. Tasca", "Ambassador", "1965", "1969"], ["Stuart W. Rockwell", "Ambassador", "1970", "1973"], ["Robert G. Neumann", "Ambassador", "1973", "1976"], ["Robert Anderson", "Ambassador", "1976", "1978"], ["Richard B. Parker", "Ambassador", "1978", "1979"], ["Angier Biddle Duke", "Ambassador", "1979", "1981"], ["Joseph Verner Reed, Jr.", "Ambassador", "1981", "1985"], ["Thomas Anthony Nassif", "Ambassador", "1985", "1988"], ["Michael Ussery", "Ambassador", "1988", "1991"], ["Frederick Vreeland", "Ambassador", "1991", "1993"], ["Marc Charles Ginsberg", "Ambassador", "1994", "1997"], ["Gary S. Usrey", "Chargé d'Affaires", "1997", "1998"], ["Edward M. Gabriel", "Ambassador", "1998", "2001"], ["Margaret D. Tutwiler", "Ambassador", "2001", "2003"], ["Thomas Riley", "Ambassador", "2004", "2009"], ["Samuel L. Kaplan", "Ambassador", "2009", "2013"], ["Matthew Lussenhop", "Chargé d'Affaires", "2013", "2014"], ["Dwight L. Bush Sr.", "Ambassador", "2014", "2017"]]}, "question": "Who has served the longest in this position?", "answer": "Thomas Riley"}
{"id": "d4a5c36f72e87f2eeac0751416cafcb4", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["sno", "power plant", "state", "total capacity (mw)", "completion schedule"], "data": [["1", "kishenganga", "jammu & kashmir", "330", "2016"], ["2", "parbati - ii", "himachal pradesh", "800", "2013"], ["3", "subansiri (lower)", "assam", "2000", "2014"], ["4", "teesta low dam - iv", "west bengal", "160", "2011"], ["5", "parbati - iii", "himachal pradesh", "520", "2012"], ["6", "nimmo - bazgo", "jammu & kashmir", "45", "2011"], ["7", "chutak", "jammu & kashmir", "44", "2011"], ["8", "uri - ii", "jammu & kashmir", "240", "2011"]]}, "question": "Does a higher total capacity (mw) causally influence the completion schedule for the power plants listed in the table?", "answer": "Yes, a higher total capacity (MW) indicates a weak positive influences (correlation coefficient of 0.48) the completion schedule for the power plants."}
{"id": "31725a5bb8447511b205abce4655d29c", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["Period", "Live births per year", "Deaths per year", "Natural change per year", "CBR1", "CDR1", "NC1", "TFR1", "IMR1"], "data": [["1950-1955", "9 000", "5 000", "4 000", "47.9", "27.1", "20.8", "6.67", "184.8"], ["1955-1960", "10 000", "6 000", "5 000", "49.0", "26.8", "22.3", "6.67", "181.4"], ["1960-1965", "12 000", "6 000", "6 000", "48.5", "25.7", "22.8", "6.67", "174.1"], ["1965-1970", "13 000", "7 000", "7 000", "47.8", "24.1", "23.8", "6.67", "163.1"], ["1970-1975", "16 000", "7 000", "8 000", "47.0", "22.0", "25.1", "6.67", "149.3"], ["1975-1980", "18 000", "8 000", "10 000", "45.8", "19.6", "26.2", "6.67", "133.2"], ["1980-1985", "20 000", "8 000", "12 000", "42.7", "17.1", "25.6", "6.39", "117.1"], ["1985-1990", "21 000", "8 000", "13 000", "40.4", "15.0", "25.3", "6.11", "104.0"], ["1990-1995", "19 000", "7 000", "12 000", "35.2", "12.5", "22.7", "5.27", "87.5"], ["1995-2000", "16 000", "5 000", "11 000", "29.2", "9.9", "19.3", "4.13", "69.7"], ["2000-2005", "15 000", "5 000", "11 000", "25.2", "7.9", "17.2", "3.3", "52.8"], ["2005-2010", "15 000", "5 000", "10 000", "21.5", "7.2", "14.4", "2.61", "44.4"]]}, "question": "What is the total number of live births from 1950-1955 to 1975-1980?", "answer": "78000"}
{"id": "5a6a21f05be43637076dc55fd0420587", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["peak", "country", "elevation (m)", "prominence (m)", "col (m)"], "data": [["mount kilimanjaro", "tanzania", "5895", "5885", "10"], ["mount kenya", "kenya", "5199", "3825", "1374"], ["mount meru", "tanzania", "4565", "3170", "1395"], ["mount elgon", "uganda", "4321", "2458", "1863"], ["mulanje massif", "malawi", "3002", "2319", "683"], ["kimhandu", "tanzania", "2653", "2121", "532"], ["mount satima", "kenya", "4001", "2081", "1920"], ["mount hanang", "tanzania", "3420", "2050", "1370"], ["loolmalassin", "tanzania", "3682", "2040", "1642"], ["gelai peak", "tanzania", "2948", "1930", "1018"], ["mount moroto", "uganda", "3083", "1818", "1265"], ["kitumbeine hill", "tanzania", "2858", "1770", "1088"], ["chepunyal hills", "kenya", "3334", "1759", "1575"], ["mount namuli", "mozambique", "2419", "1757", "662"], ["shengena", "tanzania", "2464", "1750", "714"], ["sungwi", "tanzania", "2300", "1730", "570"], ["mount kadam", "uganda", "3063", "1690", "1373"], ["mtorwi", "tanzania", "2980", "1688", "1292"], ["mount kulal", "kenya", "2285", "1542", "743"], ["karenga", "tanzania", "2279", "1529", "750"], ["mount ng'iro", "kenya", "2848", "1501", "1347"]]}, "question": "How many mountains in Tanzania have an elevation above 3000 meters and a prominence less than 3000?", "answer": "2"}
{"id": "5c61003ff264d8ec6019f2440dce475e", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["name", "latitude", "longitude", "diameter (km)", "named after"], "data": [["caccini", "17.4", "170.4", "38.1", "francesca caccini , italian composer"], ["caitlin", "- 65.3", "12.0", "14.7", "irish first name"], ["caiwenji", "- 12.4", "287.6", "22.6", "cai wenji , chinese poet"], ["caldwell", "23.6", "112.4", "51.0", "taylor caldwell , american author"], ["callas", "2.4", "27.0", "33.8", "maria callas , american singer"], ["callirhoe", "21.2", "140.7", "33.8", "callirhoe , greek sculptor"], ["caroline", "6.9", "306.3", "18.0", "french first name"], ["carr", "- 24", "295.7", "31.9", "emily carr , canadian artist"], ["carreno", "- 3.9", "16.1", "57.0", "teresa carreño , n venezuela pianist"], ["carson", "- 24.2", "344.1", "38.8", "rachel carson , american biologist"], ["carter", "5.3", "67.3", "17.5", "maybelle carter , american singer"], ["castro", "3.4", "233.9", "22.9", "rosalía de castro , galician poet"], ["cather", "47.1", "107.0", "24.6", "willa cather , american novelist"], ["centlivre", "19.1", "290.4", "28.8", "susanna centlivre , english actress"], ["chapelle", "6.4", "103.8", "22.0", "georgette chapelle , american journalist"], ["chechek", "- 2.6", "272.3", "7.2", "tuvan first name"], ["chiyojo", "- 47.8", "95.7", "40.2", "chiyojo , japanese poet"], ["chloe", "- 7.4", "98.6", "18.6", "greek first name"], ["cholpon", "40", "290.0", "6.3", "kyrgyz first name"], ["christie", "28.3", "72.7", "23.3", "agatha christie , english author"], ["chubado", "45.3", "5.6", "7.0", "fulbe first name"], ["clara", "- 37.5", "235.3", "3.2", "latin first name"], ["clementina", "35.9", "208.6", "4.0", "portuguese form of clementine , french first name"], ["cleopatra", "65.8", "7.1", "105.0", "cleopatra , egyptian queen"], ["cline", "- 21.8", "317.1", "38.0", "patsy cline , american singer"], ["clio", "6.3", "333.5", "11.4", "greek first name"], ["cochran", "51.9", "143.4", "100.0", "jacqueline cochran , american aviator"], ["cohn", "- 33.3", "208.1", "18.3", "carola cohn , australian artist"], ["colleen", "- 60.8", "162.2", "13.5", "irish first name"], ["comnena", "1.2", "343.7", "19.5", "anna comnena , byzantine princess and writer"], ["conway", "48.3", "39.0", "49.3", "lady anne finch conway , english natural scientist"], ["cori", "25.4", "72.9", "56.1", "gerty cori , czech biochemist"], ["corinna", "22.9", "40.6", "19.2", "corinna , greek poet"], ["corpman", "0.3", "151.8", "46.0", "elizabeth koopman hevelius , astronomer"], ["cortese", "- 11.4", "218.4", "27.7", "isabella cortese , italian physician"], ["cotton", "70.8", "300.2", "48.1", "eugénie cotton , french physicist"], ["cunitz", "14.5", "350.9", "48.6", "maria cunitz , silesian astronomer"], ["cynthia", "- 16.7", "347.5", "15.9", "greek first name"]]}, "question": "What is the total diameter of all craters with a diameter greater than 40 km?", "answer": "601.3"}
{"id": "b2d44040ff634ed681b901635e63fbbd", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["rank", "company", "headquarters", "industry", "sales (billion )", "profits (billion )", "assets (billion )", "market value (billion )"], "data": [["1", "jpmorgan chase", "usa", "banking", "115.5", "17.4", "2117.6", "182.2"], ["2", "hsbc", "uk", "banking", "103.3", "13.3", "2467.9", "186.5"], ["3", "general electric", "usa", "conglomerate", "156.2", "11.6", "751.2", "216.2"], ["4", "exxonmobil", "usa", "oil and gas", "341.6", "30.5", "302.5", "407.2"], ["5", "royal dutch shell", "netherlands", "oil and gas", "369.1", "20.1", "317.2", "212.9"], ["6", "petrochina", "china", "oil and gas", "222.3", "21.2", "251.3", "320.8"], ["7", "industrial and commercial bank of china", "china", "banking", "69.2", "18.8", "1723.5", "239.5"], ["8", "berkshire hathaway", "usa", "conglomerate", "136.2", "13.0", "372.2", "211.0"], ["8", "petrobras", "brazil", "oil and gas", "121.3", "21.2", "313.2", "238.8"], ["10", "citigroup", "usa", "banking", "111.5", "10.6", "1913.9", "132.8"], ["11", "bnp paribas", "france", "banking", "130.4", "10.5", "2680.7", "88.0"], ["11", "wells fargo", "usa", "banking", "93.2", "12.4", "1258.1", "170.6"], ["13", "santander group", "spain", "banking", "109.7", "12.8", "1570.6", "94.7"], ["14", "at&t inc", "usa", "telecommunications", "124.3", "19.9", "268.5", "168.2"], ["15", "gazprom", "russia", "oil and gas", "98.7", "25.7", "275.9", "172.9"], ["16", "chevron", "usa", "oil and gas", "189.6", "19.0", "184.8", "200.6"], ["17", "china construction bank", "china", "banking", "58.2", "15.6", "1408.0", "224.8"], ["18", "walmart", "usa", "retailing", "421.8", "16.4", "180.7", "187.3"], ["19", "total", "france", "oil and gas", "188.1", "14.2", "192.8", "138.0"], ["20", "allianz", "germany", "insurance", "142.9", "6.7", "838.4", "62.7"]]}, "question": "What is the correlation between 'assets (billion)' and 'profits (billion)' among banking industry companies? Provide the correlation coefficient as evidence.", "answer": "Weak negative correlation, -0.48"}
{"id": "5921991ef07b397dfd587a30c770faea", "qtype": "FactChecking", "qsubtype": "Multi-hop FactChecking", "table": {"columns": ["year", "delegate", "hometown", "pageant", "result", "other awards"], "data": [["1971", "nelia sancho", "no information available", "queen of the pacific", "winner", "none"], ["1971", "milagros gutierrez", "no information available", "miss charming international", "second runner - up", "none"], ["1972", "maria isabel seva", "no information available", "miss charming international", "did not place", "none"], ["1989", "maria rita apostol", "no information available", "miss flower queen", "did not place", "none"], ["1992", "sharmaine rama gutierrez", "manila , metro manila", "elite model look", "did not place", "none"], ["1993", "anna maria gonzalez", "no information available", "elite model look", "did not place", "none"], ["1995", "rollen richelle caralde", "no information available", "elite model look", "did not place", "none"], ["1996", "ailleen marfori damiles", "las piñas , metro manila", "international folklore beauty pageant", "top 5 finalist", "miss photogenic"], ["1997", "joanne zapanta santos", "san fernando , pampanga", "miss tourism international", "winner", "none"], ["2000", "rachel muyot soriano", "no information available", "miss tourism world", "second runner - up", "best in long gown"], ["2001", "maricar manalaysay balagtas", "bulacan", "miss globe international", "winner", "best national costume"], ["2001", "michelle cueva reyes", "caloocan city , metro manila", "miss tourism international", "winner", "best national costume"], ["2001", "zorayda ruth blanco andam", "baguio city", "miss tourism world", "finalist", "miss tourism world asia"], ["2001", "joanna maria mijares peñaloza", "mandaluyong city , metro manila", "miss internet www", "did not place", "face of the net"], ["2002", "kristine reyes alzar", "lipa , batangas", "miss tourism international", "winner", "best national costume"], ["2002", "karen loren medrano agustin", "manila , metro manila", "miss globe international", "fifth runner - up", "best in swimsuit"], ["2002", "michelle cueva reyes", "caloocan city , metro manila", "miss tourism world", "winner", "best national costume"], ["2002", "margaret - ann awitan bayot", "antipolo , rizal", "miss internet www", "second runner - up", "none"], ["2003", "noella mae evangelista", "iligan city", "queen of tourism international", "winner", "best national costume"], ["2004", "karen loren medrano agustin", "manila , metro manila", "world coffee queen", "second runner - up", "none"], ["2004", "margaret ann awitan bayot", "antipolo , rizal", "miss maja mundial", "first runner - up / virreina", "none"], ["2005", "jhezarie games javier", "manila , metro manila", "miss asean", "winner", "none"], ["2006", "carlene ang aguilar", "quezon city , metro manila", "miss internet www", "winner", "none"], ["2009", "april love antolo jordan", "manila , metro manila", "beauty of the world", "winner", "none"], ["2010", "mariella castillo", "mabini , batangas", "miss global teen", "top 12 semi - finalist", "teen queen of asia and oceania"], ["2011", "czarina catherine gatbonton", "malolos , bulacan", "miss humanity international", "second runner - up", "none"]]}, "question": "How many delegates from Metro Manila won a pageant title?", "answer": "2"}
{"id": "c3ce5811d8041e247d700aa708d16934", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["Year", "-", "Year", "-", "Year", "-"], "data": [["1820", "8,385", "1885", "395,346", "1950", "249,187"], ["1825", "10,199", "1890", "455,302", "1955", "237,790"], ["1830", "23,322", "1895", "258,536", "1960", "265,398"], ["1835", "45,374", "1900", "448,572", "1965", "296,697"], ["1840", "84,066", "1905", "1,026,499", "1970", "373,326"], ["1845", "114,371", "1910", "1,041,570", "1975", "385,378"], ["1850", "369,980", "1915", "326,700", "1980", "524,295"], ["1855", "200,877", "1920", "430,001", "1985", "568,149"], ["1860", "153,640", "1925", "294,314", "1990", "1,535,872"], ["1865", "248,120", "1930", "241,700", "1995", "720,177"], ["1870", "387,203", "1935", "34,956", "2000", "841,002"], ["1875", "227,498", "1940", "70,756", "2005", "1,122,257"], ["1880", "457,257", "1945", "38,119", "2010", "1,042,625"]]}, "question": "What is the value in the year with the highest increase in value between 1850 and 1870?", "answer": "387203"}
{"id": "410a163d2c0672ff88d17cab3cecc0c6", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["Year", "Numer of Jamaicans\ngranted British\ncitizenship", "Naturalisation\nby residence", "Naturalisation\nby marriage", "Registration\nof a minor child", "Registration\nby other means"], "data": [["1997", "732", "327", "279", "114", "12"], ["1998", "1,370", "571", "564", "221", "14"], ["1999", "1,437", "678", "526", "226", "7"], ["2000", "1,882", "927", "664", "281", "10"], ["2001", "2,070", "1,025", "710", "330", "0"], ["2002", "2,025", "1,035", "705", "285", "0"], ["2003", "2,795", "1,285", "985", "520", "5"], ["2004", "3,180", "1,415", "1,060", "640", "65"], ["2005", "3,515", "1,585", "1,080", "770", "80"], ["2006", "2,525", "1,110", "710", "655", "55"], ["2007", "3,165", "1,575", "825", "725", "45"], ["2008", "2,715", "1,275", "695", "700", "45"]]}, "question": "In 2001, how many Jamaicans were granted British citizenship through naturalization by marriage?", "answer": "710"}
{"id": "a6bf1a5c7ab44c8674bb88b508865392", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["city (utility)", "residential monthly usage : 1000 kwh", "small power power demand : 40 kw , consumption 10000 kwh , load factor : 35%", "medium power power demand : 1000 kw , consumption : 400000 kwh , load factor : 56 %", "large power power demand : 50000 kw , consumption : 30600000 kwh , load factor : 85%"], "data": [["st john 's ( newfoundland power / nl hydro )", "11.8", "11.83", "9.05", "3.98"], ["charlottetown ( maritime electric )", "14.51", "15.18", "12.68", "8.36"], ["halifax ( nova scotia power )", "15.01", "14.25", "11.99", "9.0"], ["moncton ( nb power )", "11.82", "12.46", "10.98", "6.86"], ["montreal ( hydro - quãbec )", "6.76", "8.85", "7.19", "4.51"], ["ottawa ( hydro ottawa )", "13.14", "12.94", "11.42", "10.58"], ["toronto ( toronto hydro )", "13.57", "13.41", "11.43", "10.46"], ["winnipeg ( manitoba hydro )", "7.46", "7.29", "5.62", "3.69"], ["regina ( saskpower )", "12.54", "10.31", "9.08", "5.67"], ["edmonton ( epcor )", "12.9", "12.41", "11.07", "6.97"], ["calgary ( enmax )", "13.89", "11.24", "9.53", "8.28"]]}, "question": "How many cities have a residential monthly usage of 1000 kwh above 12?", "answer": "7"}
{"id": "7cfdc47e6cfdc865ffb5d0a9ddd1d380", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Rank", "City", "Passengers", "Ranking", "Airline"], "data": [["1", "Quintana Roo, Cancún", "132,046", null, "Aeroméxico Connect, Interjet, Volaris"], ["2", "Nuevo León, Monterrey", "106,513", null, "Aeroméxico Connect, Interjet"], ["3", "Guerrero, Acapulco", "56,069", null, "Aeroméxico Connect, Interjet"], ["4", "Jalisco, Guadalajara", "52,584", null, "Aeroméxico Connect, Volaris"], ["5", "Jalisco, Puerto Vallarta", "43,419", "1.0", "Interjet"], ["6", "Baja California Sur, Los Cabos", "37,526", "1.0", "Interjet"], ["7", "Guerrero, Ixtapa/Zihuatanejo", "35,507", null, "Interjet"], ["8", "Baja California, Tijuana", "14,906", null, "Interjet"], ["9", "Tabasco, Villahermosa", "6,928", "1.0", "VivaAerobus"], ["10", "Tamaulipas, Tampico", "3,619", "1.0", "VivaAerobus"]]}, "chart_type": "bar", "question": "Please  draw a bar chart displaying the number of passengers for each city", "answer": "y_references = [[132046, 106513, 56069, 52584, 43419, 37526, 35507, 14906, 6928, 3619]]"}
{"id": "a9196b8ddb587ea972419f2fec183f52", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["year", "district - wide", "high schools", "middle schools", "elementary schools", "other programs & adjustments"], "data": [["2006 - 2007", "31221", "8808", "6405", "14369", "1639"], ["2005 - 2006", "31599", "8570", "6610", "14249", "2170"], ["2004 - 2005", "31851", "8620", "6876", "14384", "1971"], ["2003 - 2004", "32150", "8430", "7115", "14497", "2108"], ["2002 - 2003", "32464", "8696", "7103", "14733", "1932"], ["2001 - 2002", "35399", "10114", "5504", "19541", "240"]]}, "question": "What is the average annual change in the 'district-wide' budget from 2001-2002 to 2006-2007?", "answer": "835.6"}
{"id": "94d72b367c09d2eb2aac84632358348e", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["south american rank", "world rank", "nation", "2011 (imf)", "2008 (cia factbook)"], "data": [["1", "51", "argentina", "17376", "14500"], ["2", "55", "chile", "16171", "15400"], ["3", "59", "uruguay", "15469", "12300"], ["4", "71", "venezuela", "50000", "40000"], ["5", "74", "brazil", "11845", "10513"], ["6", "82", "colombia", "10155", "9000"], ["7", "83", "peru", "500", "300"], ["8", "86", "suriname", "9492", "8900"], ["9", "91", "ecuador", "8335", "7700"], ["10", "96", "guyana", "7541", "4000"], ["11", "110", "paraguay", "5548", "4400"]]}, "question": "Which countries have values that deviate significantly from the norm?", "answer": "The two anomalies are Venezuela's unusually high GDP per capita in 2011 (IMF) at 50,000, and Peru's suspiciously low GDP per capita in the same year at 500."}
{"id": "9f15a6aac09b294c6ed56c01b70bc268", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year (january)", "population (000)", "rural , %", "urban , %", "source"], "data": [["1939", "6081", "72", "28", "census"], ["1959", "9295", "56", "44", "census"], ["1970", "13001", "50", "50", "census"], ["1979", "14685", "46", "54", "census"], ["1989", "16537", "43", "57", "census"], ["1999", "14953", "43", "57", "census"], ["2002", "14851", "43", "57", "estimate"], ["2005", "15075", "43", "57", "estimate"], ["2008", "15572", "47", "53", "estimate"]]}, "question": "Considering the historical trend of urbanization from 1939 to 2008, what might be the expected urban population percentage in 2015 if the trend continues?", "answer": "62.21%"}
{"id": "0150c6f2c5f3a2b3ea7326b41446cbf3", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["region", "start datum", "target datum", "c_x ( metre )", "c_y (metre)", "c_z (metre)", "s ( ppm )", "r x ( arcsecond )", "r y ( arcsecond )", "r z ( arcsecond )"], "data": [["slovenia etrs89", "d48", "d96", "409.545", "72.164", "486.872", "17.919665", "3.085957", "5.46911", "11.020289"], ["england , scotland , wales", "wgs84", "osgb36", "446.448", "125.157", "542.06", "20.4894", "0.1502", "0.247", "0.8421"], ["ireland", "wgs84", "ireland 1965", "482.53", "130.596", "564.557", "8.15", "1.042", "0.214", "0.631"], ["germany", "wgs84", "dhdn", "591.28", "81.35", "396.39", "9.82", "1.477", "0.0736", "1.458"], ["germany", "wgs84", "bessel 1841", "582.0", "105.0", "414.0", "8.3", "1.04", "0.35", "3.08"], ["germany", "wgs84", "krassovski 1940", "24.0", "123.0", "94.0", "1.1", "0.02", "0.26", "0.13"], ["austria (bev)", "wgs84", "mgi", "577.326", "90.129", "463.92", "2.423", "5.137", "1.474", "5.297"]]}, "question": "What is the average value of `c_x (metre)` across all regions?", "answer": "444.73"}
{"id": "28c3c56d475d8da371f9ea72756681dc", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["rank", "country / territory", "manhunt international", "1st runner - up", "2nd runner - up", "3rd runner - up", "4th runner - up", "semifinalists", "total"], "data": [["1", "china", "2", "1", "1", "1", "0", "5", "10"], ["2", "india", "1", "2", "0", "0", "3", "5", "11"], ["3", "sweden", "1", "2", "0", "0", "0", "3", "6"], ["4", "venezuela", "1", "1", "1", "1", "1", "6", "11"], ["5", "turkey", "1", "1", "1", "1", "0", "3", "7"], ["6", "australia", "1", "1", "0", "1", "0", "4", "7"], ["7", "germany", "1", "1", "0", "0", "0", "1", "3"], ["8", "usa", "1", "0", "3", "1", "0", "3", "8"], ["9", "philippines", "1", "0", "1", "1", "0", "3", "6"], ["10", "greece", "1", "0", "1", "0", "0", "3", "5"], ["11", "south africa", "1", "0", "0", "0", "1", "3", "5"], ["12", "slovakia", "1", "0", "0", "0", "1", "0", "2"], ["13", "france", "1", "0", "0", "0", "0", "2", "3"], ["14", "morocco", "1", "0", "0", "0", "0", "0", "1"]]}, "question": "What is the standard deviation of the total points across all countries in the table?", "answer": "3.19"}
{"id": "a79cd8ec27af6973720047fe8cd8e217", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["draw", "artist", "song", "jury votes", "televotes", "total votes", "result"], "data": [["1", "diqesi", "subiré", "5", "4", "9", "out"], ["2", "roel", "y ahora dices", "6", "3", "9", "out"], ["3", "salva ortega", "lujuria", "7", "7", "14", "second chance >final"], ["4", "soraya", "la noche es para mí", "12", "12", "24", "final"], ["5", "virginia", "true love", "10", "10", "20", "final"], ["6", "calipop", "burbuja", "2", "2", "4", "out"], ["7", "ángeles vela", "vístete de primavera", "4", "5", "9", "out"], ["8", "jorge gonzález", "si yo vengo a enamorarte", "8", "8", "16", "final"], ["9", "electronikboy", "mon petit oiseau", "1", "1", "2", "out"]]}, "question": "What is the total number of jury votes received by artists who made it to the 'final'?", "answer": "37"}
{"id": "1eeb4d900062e9c62b8ffb728e07c584", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["county", "1948", "1956", "1966", "1977", "1992", "2002", "2011"], "data": [["zzz bucharest", "1025180", "1177661", "1366684", "1807239", "2064474", "1926334", "1883425"], ["iași", "431586", "516635", "619027", "729243", "806778", "816910", "772348"], ["prahova", "557776", "623817", "701057", "817168", "873229", "829945", "762886"], ["cluj", "520073", "580344", "629746", "715507", "735077", "702755", "691106"], ["constanța", "311062", "369940", "465752", "608817", "748044", "715151", "684082"], ["timiș", "588936", "568881", "607596", "696884", "700292", "677926", "683540"], ["dolj", "615301", "642028", "691116", "750328", "761074", "734231", "660544"], ["suceava", "439751", "507674", "572781", "633899", "700799", "688435", "634810"], ["bacău", "414996", "507937", "598321", "667791", "736078", "706623", "616168"], ["argeș", "448964", "483741", "529833", "631918", "680574", "652625", "612431"], ["bihor", "536323", "574488", "586460", "633094", "634093", "600246", "575398"], ["mureș", "461403", "513261", "561598", "605345", "607298", "580851", "550846"], ["brașov", "300836", "373941", "442692", "582863", "642513", "589028", "549217"], ["galați", "341797", "396138", "474279", "581561", "639853", "619556", "536167"], ["dmbovița", "409272", "438985", "453241", "527620", "559874", "541763", "518745"], ["maramureș", "321287", "367114", "427645", "492860", "538534", "510110", "478659"], ["neamț", "357348", "419949", "470206", "532096", "577619", "554516", "470766"], ["buzău", "430225", "465829", "480951", "508424", "516307", "496214", "451069"], ["olt", "442442", "458982", "476513", "518804", "520966", "489274", "436400"], ["arad", "476207", "475620", "481248", "512020", "487370", "461791", "430629"], ["hunedoara", "306955", "381902", "474602", "514436", "547993", "485712", "418565"], ["botoșani", "385236", "428050", "452406", "451217", "458904", "452834", "412626"], ["sibiu", "335116", "372687", "414756", "481645", "452820", "421724", "397322"], ["vaslui", "344917", "401626", "431555", "437251", "457799", "455049", "395499"], ["ilfov", "167533", "196265", "229773", "287738", "286510", "300123", "388738"], ["teleorman", "487394", "510488", "516222", "518943", "482281", "436025", "380123"], ["vlcea", "341590", "362356", "368779", "414241", "436298", "413247", "371714"], ["satu mare", "312672", "337351", "359393", "393840", "400158", "367281", "344360"], ["alba", "361062", "370800", "382786", "409634", "414227", "382747", "342376"], ["gorj", "280524", "293031", "298382", "348521", "400100", "387308", "341594"], ["vrancea", "290183", "326532", "351292", "369740", "392651", "387632", "340310"], ["brăila", "271251", "297276", "339954", "377954", "392069", "373174", "321212"], ["harghita", "258495", "273964", "282392", "326310", "347637", "326222", "310867"], ["călărași", "287722", "318573", "337261", "338807", "338844", "324617", "306691"], ["caraș - severin", "302254", "327787", "358726", "385577", "375794", "333219", "295579"], ["bistrița - năsăud", "233650", "255789", "269954", "286628", "327238", "311657", "286225"], ["giurgiu", "313793", "325045", "320120", "327494", "313084", "297859", "281422"], ["ialomiţa", "244750", "274655", "291373", "295965", "304008", "296572", "274148"], ["mehedinți", "304788", "304091", "310021", "322371", "332091", "306732", "265390"], ["sălaj", "262580", "271989", "263103", "264569", "266308", "248015", "224384"], ["tulcea", "192228", "223719", "236709", "254531", "270197", "256492", "213083"], ["covasna", "157166", "172509", "176858", "199017", "232592", "222449", "210177"], ["total", "15872624", "17489450", "19103163", "21559910", "22760449", "21680974", "20121641"]]}, "question": "What is the total population of the top 5 counties in 1948?", "answer": "2844677"}
{"id": "c053c02d128201b79cbbd11c395f542a", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["institution", "location", "established", "gained university status", "vice - chancellor", "total number of students", "research funding (000)"], "data": [["birkbeck , university of london", "london", "1823", "1920", "professor david latchman", "19020", "9985"], ["university of east anglia", "norwich", "1963", "1963", "professor edward acton", "19585", "16482"], ["university of essex", "colchester", "1964", "1964", "professor anthony forster", "11690", "9967"], ["goldsmiths , university of london", "london", "1891", "1904", "dr pat loughrey", "7615", "8539"], ["institute of education , university of london", "london", "1902", "1932", "professor chris husbands", "7215", "7734"], ["university of lancaster", "lancaster", "1964", "1964", "professor mark smith", "12695", "18640"], ["university of leicester", "leicester", "1921", "1957", "professor robert burgess", "16160", "22225"], ["loughborough university", "loughborough", "1909", "1966", "professor robert allison", "17825", "22398"], ["royal holloway , university of london", "egham", "1849", "1900", "professor paul layzell (principal)", "7620", "13699"], ["soas , university of london", "london", "1916", "1916", "professor paul webley", "4525", "7238"]]}, "question": "How many universities are located in London?", "answer": "4"}
{"id": "65aadc9add4b1a42f5b5071d6a16cfd6", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["Year", "Number", "Name", "Year.1", "Number.1", "Name.1", "Year.2", "Number.2", "Name.2"], "data": [["1884–1885", "7", "Lukin Homphrey Irving (first)", "1886–1887", "18", "Duncan MacPherson", "1888", "4", "William Mahlon Davis"], ["1889–1890", "6", "Septimus Julius Augustus Denison", "1891", "10", "Victor Brereton Rivers", "1892", "86", "Reuben Wells Leonard"], ["1893–1894", "37", "E.H. Drury", "1895–1896", "15", "Francis Joseph Dixon", "1897", "48", "A.K. Kirkpatrick"], ["1898", "57", "H.S. Greenwood", "1899", "14", "John Bray Cochrane", "1900", "41", "Robert Cartwright"], ["1901", "154", "F.M. Gaudet", "1902", "47", "Ernest Frederick Wurtele", "1903", "21", "A.E. Doucet"], ["1904", "82", "Wallace Bruce Matthews Carruthers", "1905", "188", "W.A.H. Kerr", "1906", "186", "V.A.S. Williams"], ["1907", "139", "C.R.F. Coutlee", "1908", "232", "John Houlison", "1909", "91", "J.D. Gibson"], ["1910", "63", "George Hooper", "1911", "255", "H.A. Panet", "1912", "246", "Major-General Sir Henry Edward Burstall"], ["1913", "268", "Henry Robert Visart de Bury et de Bocarmé", "1914; 1919", "299", "Col. Harry J. Lamb DSO, VD", "1920", "293", "C.J. Armstrong"], ["1920–1922", "392", "W.B. Kingsmill", "1923", "377", "A.C. Caldwell", "1924", "140", "G.S. Cartwright"], ["1925", "499", "Edouard de B. Panet", "1926", "631", "A.B. Gillies", "1927", "623", "S.B. Coristine"], ["1928", "555", "R.R. Carr-Harris", "1929", "667", "E.G. Hanson", "1929–1930", "SUO", "G.D. de S. Wotherspoon"], ["1930–1931", "1119", "J.H. Price", "1932", "472", "A.R. Chipman", "1933–1934", "805", "Colin W. G. Gibson"], ["1935", "727", "D.A. White", "1936–1937", "877", "G.L. Magann", "1938–1939", "1003", "A.M. Mitchell"], ["1940–1941", "803", "J.V. Young", "1942–1943", "1141", "W.H. O'Reilly", "1944", "698", "Everett Bristol"], ["1945", "982", "D.W. MacKeen", "1946", "1841", "D.G. Cunningham", "1947", "1230", "S.H. Dobell"], ["1948", "1855", "Ian S. Johnston", "1949", "1625", "J.D. Watt", "1950", "1542", "E.W. Crowe"], ["1951", "1860", "Nicol Kingsmill", "1952", "1828", "Ted G.E. Beament", "1953", "1620", "R.R. Labatt"], ["1954", "1766", "Ken H. Tremain", "1955", "1474", "de L.H.M Panet", "1956", "2034", "Paul Y. Davoud"], ["1957", "1954", "W.P. Carr", "1960", "1379", "H.A. Mackenzie", "1961", "2157", "J.H.R. Gagnon"], ["1962", "2183", "James E. Pepall", "1963", "2336", "J.H. Moore", "1964", "2351", "Guy Savard"], ["1965", "2749", "James B. Cronyn", "1966", "2601", "J. Fergus Maclaren", "1967", "2791", "Jean P.W. Ostiguy"], ["1968–1969", "RCNC90", "John F. Frank", "1975–1976", "3661", "Terry Yates", "1976–1977", "5533", "Glenn Allen"], ["1977–1978", "3172", "Marshall Soule", "1980–1981", "3251", "Jim Tremain", "1981–1982", "2897", "Herb Pitts"], ["1986–1987", "5604", "Ken Smee", "1987–1988", "3010", "Peter McLoughlin", "1992–1993", "H3356", "Robin Cumine"], ["1993–1994", "5244", "Tony Downs", "1994–1995", "H7543", "Senator Joseph A. Day", "1995–1996", "5739", "Andre Costin"], ["1996–1997", "3550", "Murray Johnston", "1997–1998", "8813", "John D. Gibson", "1998–1999", "G0055", "Valerie Keyes (first female)"], ["1999–2000", "8833", "John Leggat", "2000–2001", "5758", "Michael Morres", "2001–2002", "16461", "Ian MacKinnon"], ["2002–2003", "6777", "Michel Charron", "2003–2004", "7776", "Chris Lythgo", "2004–2005", "7943", "J. William K. Lye"], ["2005–2006", "10080", "Robert Booth", "2007–2008", "6776", "Tim Sparling", "2008–2009", "15988", "Jeff Kearns"], ["2010", "16412", "Gord Clarke", "2011", "19307", "David Benoit", "2012", "9889", "Robert Benn"], ["2013", "M0058", "Marc Drolet (first UTPNCM)", null, null, null, null, null, null]]}, "question": "How many individuals have a 'Number' value greater than 1500?", "answer": "14"}
{"id": "de82a1f1334f8d83cfd1a7fd13c29ed3", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Name", "Position", "Length\n[km]", "Drainage basin area\n[km2]", "Confluence\n[by Lahn-km]", "Mouth elevation\n[m above MSL]"], "data": [["Feudinge (Rüppersbach)", "left", "6.3", "21.2", "9.8", "388"], ["Ilse", "right", "8.4", "11.8", "10.5", "382"], ["Banfe", "right", "11.5", "38.9", "18.5", "326"], ["Laasphe", "left", "8.3", "19.6", "19.4", "324"], ["Perf", "right", "20.0", "113.1", "24.7", "285"], ["Dautphe", "left", "8.8", "41.8", "37.5", "245"], ["Wetschaft", "left", "29.0", "196.2", "56.3", "192"], ["Ohm", "left", "59.7", "983.8", "58.7", "188"], ["Allna", "right", "19.1", "92.0", "77.1", "172"], ["Zwester Ohm", "left", "20.0", "69.5", "84.0", "165"], ["Salzböde", "right", "27.6", "137.8", "87.4", "164"], ["Lumda", "left", "30.0", "131.5", "93.6", "160"], ["Wieseck", "left", "24.3", "119.6", "102.2", "155"], ["Bieber", "right", "13.6", "34.7", "105.1", "151"], ["Kleebach", "left", "26.9", "164.6", "106.2", "150"], ["Wetzbach", "left", "11.7", "32.9", "119.6", "147"], ["Dill", "right", "55.0", "717.7", "120.4", "147"], ["Solmsbach", "left", "24.6", "112.5", "128.1", "141"], ["Iserbach (Möttbach)", "left", "19.2", "31.2", "131.4", "139"], ["Ulmbach", "right", "22.9", "60.9", "138.2", "135"], ["Kallenbach", "right", "14.6", "84.7", "141.3", "132"], ["Weil", "left", "46.6", "247.9", "149.4", "130"], ["Kerkerbach", "right", "20.7", "70.2", "176.0", "112"], ["Emsbach", "left", "39.1", "321.8", "181.0", "110"], ["Elbbach", "right", "40.7", "323.7", null, "109"], ["Aar", "left", "49.7", "312.6", null, "103"], ["Dörsbach", "left", "32.0", "114.0", null, "94"], ["Gelbach (Aubach)", "right", "39.7", "221.2", null, "93"], ["Mühlbach", "left", "32.1", "171.9", null, "85"], ["Emsbach", "right", "11.5", "29.4", null, "75"]]}, "chart_type": "bar", "question": "Please help me draw an bar chart that shows the length of rivers and their drainage basin areas.", "answer": "y_references = [[6.3, 8.4, 11.5, 8.3, 20.0, 8.8, 29.0, 59.7, 19.1, 20.0, 27.6, 30.0, 24.3, 13.6, 26.9, 11.7, 55.0, 24.6, 19.2, 22.9, 14.6, 46.6, 20.7, 39.1, 40.7, 49.7, 32.0, 39.7, 32.1, 11.5], [21.2, 11.8, 38.9, 19.6, 113.1, 41.8, 196.2, 983.8, 92.0, 69.5, 137.8, 131.5, 119.6, 34.7, 164.6, 32.9, 717.7, 112.5, 31.2, 60.9, 84.7, 247.9, 70.2, 321.8, 323.7, 312.6, 114.0, 221.2, 171.9, 29.4]]"}
{"id": "bdfcc7e1bb6dc5eef09456c8ba56f46d", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["canton", "years of kindergarten", "years of kindergarten provided", "years of kindergarten legally required", "length of primary school", "length of mandatory secondary school", "separate secondary schools", "cooperative secondary schools", "integrated secondary schools"], "data": [["zurich", "2", "2", "2", "6", "3", "yes", "no", "no"], ["bern", "1", "1", "0", "6", "3", "yes", "yes", "yes"], ["lucerne", "1", "1", "1", "6", "3", "yes", "yes", "yes"], ["uri", "1", "1", "0", "6", "3", "no", "no", "yes"], ["schwyz", "1", "1", "1", "6", "3", "no", "no", "yes"], ["obwalden", "1", "1", "1", "6", "3", "no", "no", "yes"], ["nidwalden", "2", "2", "1", "6", "3", "no", "no", "yes"], ["glarus", "2", "2", "1", "6", "3", "yes", "yes", "yes"], ["zug", "2", "1", "1", "6", "3", "no", "no", "yes"], ["fribourg", "2", "1 or 2", "0 or 2", "6", "3", "yes", "no", "yes"], ["solothurn", "2", "2", "0", "6", "3", "yes", "yes", "yes"], ["basel - stadt", "2", "2", "2", "4", "5", "yes", "no", "no"], ["basel - landschaft", "2", "2", "1", "5", "4", "yes", "no", "no"], ["schaffhausen", "2", "2", "1", "6", "3", "no", "no", "yes"], ["appenzell ausserrhoden", "2", "2", "1", "6", "3", "yes", "yes", "yes"], ["appenzell innerrhoden", "2", "2", "1", "6", "3", "yes", "yes", "yes"], ["st gallen", "2", "2", "2", "6", "3", "no", "no", "yes"], ["graubã¼nden", "1", "1", "0", "6", "3", "yes", "no", "no"], ["aargau", "1", "1", "0", "5", "4", "yes", "no", "no"], ["thurgau", "2", "2", "2", "6", "3", "yes", "no", "no"], ["ticino", "3", "3", "0", "5", "4", "yes", "no", "no"], ["vaud", "2", "2", "0", "4", "5", "yes", "no", "no"], ["valais", "1", "0", "0", "6", "3", "yes", "no", "no"], ["neuchãtel", "2", "2", "0", "5", "4", "yes", "no", "no"], ["geneva", "2", "2", "0", "6", "3", "yes", "no", "no"]]}, "question": "How many cantons have 'yes' in the 'separate secondary schools' column?", "answer": "18"}
{"id": "f10d21dbe9cca173c388760beaa75c80", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["name", "league goals", "fa cup goals", "fl cup goals", "other goals", "total", "career"], "data": [["george brown", "142", "17", "0", "0", "159", "1921 - 1929"], ["jimmy glazzard", "142", "12", "0", "0", "154", "1946 - 1956"], ["andy booth", "133", "5", "4", "8", "150", "1991 - 1996 and 2001 - 2009"], ["billy smith", "114", "12", "0", "0", "126", "1913 - 1934"], ["les massie", "100", "6", "2", "0", "108", "1956 - 1966"], ["vic metcalfe", "87", "3", "0", "0", "90", "1946 - 1958"], ["alex jackson", "70", "19", "0", "0", "89", "1925 - 1930"], ["jordan rhodes", "73", "2", "6", "6", "87", "2009 - 2012"], ["frank mann", "68", "7", "0", "0", "75", "1912 - 1923"], ["dave mangnall", "61", "12", "0", "0", "73", "1929 - 1934"], ["derek stokes", "65", "2", "2", "0", "69", "1960 - 1965"], ["kevin mchale", "60", "5", "3", "0", "68", "1956 - 1967"], ["iwan roberts", "50", "4", "6", "8", "68", "1990 - 1993"], ["ian robins", "59", "5", "3", "0", "67", "1978 - 1982"], ["marcus stewart", "58", "2", "7", "0", "67", "1996 - 2000"], ["mark lillis", "56", "4", "3", "0", "63", "1978 - 1985"], ["charlie wilson", "57", "5", "0", "0", "62", "1922 - 1925"], ["alan gowling", "58", "1", "2", "0", "61", "1972 - 1975"], ["craig maskell", "43", "3", "4", "4", "55", "1988 - 1990"], ["brian stanton", "45", "6", "3", "0", "54", "1979 - 1986"], ["colin dobson", "50", "0", "2", "0", "52", "1966 - 1970"], ["ernie islip", "44", "8", "0", "0", "52", "1913 - 1923"], ["paweł abbott", "48", "1", "2", "0", "51", "2004 - 2007"], ["clem stephenson", "42", "8", "0", "0", "50", "1921 - 1929"], ["david cowling", "43", "2", "3", "0", "48", "1978 - 1987"], ["duncan shearer", "38", "3", "6", "1", "48", "1986 - 1988"], ["frank worthington", "41", "5", "2", "0", "48", "1967 - 1972"], ["charlie luke", "40", "7", "0", "0", "47", "1931 - 1936"], ["phil starbuck", "36", "4", "2", "5", "47", "1991 - 1995"], ["jimmy lawson", "42", "4", "0", "0", "46", "1968 - 1976"], ["alf lythgoe", "42", "4", "0", "0", "46", "1934 - 1938"], ["george mclean", "43", "3", "0", "0", "46", "1930 - 1934"], ["danny schofield", "39", "1", "0", "6", "46", "1998 - 2008"], ["peter fletcher", "36", "4", "5", "0", "45", "1978 - 1982"], ["sammy taylor", "39", "6", "0", "0", "45", "1919 - 1921"], ["tony leighton", "40", "2", "2", "0", "44", "1965 - 1968"], ["ronnie jepson", "36", "3", "2", "1", "42", "1993 - 1996"], ["bob kelly", "39", "3", "0", "0", "42", "1927 - 1932"], ["lee novak", "34", "5", "2", "1", "42", "2009 - 2013"], ["terry gray", "36", "2", "3", "0", "41", "1973 - 1979"]]}, "question": "According to the table, how many league goals did george mclean score during his career from 1930 - 1934?", "answer": "43"}
{"id": "977fbcfd2756614b2cdb69c9f742d8bb", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["rank in nyagatare sectors , 2012", "sector", "area in sqkm", "population august 15 , 2012", "population , august 15 , 2002", "population change 2002 - 2012 (%)", "population density 2012 (km 2 )"], "data": [["8", "gatunda", "52", "27879", "19716", "41.4", "535"], ["10", "karama", "53", "26727", "19727", "35.5", "499"], ["2", "karangazi", "563", "56871", "21234", "167.8", "101"], ["4", "katabagemu", "98", "34651", "22101", "56.8", "354"], ["14", "kiyombe", "69", "17061", "16483", "3.5", "247"], ["11", "matimba", "79", "24168", "13476", "79.3", "307"], ["9", "mimuli", "48", "27366", "22452", "21.9", "573"], ["12", "mukama", "64", "21819", "17970", "21.4", "339"], ["7", "musheli", "96", "32403", "14742", "119.8", "338"], ["3", "nyagatare", "164", "52125", "19475", "167.7", "317"], ["5", "rukomo", "58", "34377", "20945", "64.1", "588"], ["13", "rwempasha", "169", "19328", "11428", "69.1", "115"], ["1", "rwimiyaga", "309", "58847", "16802", "250.2", "190"], ["6", "tabagwe", "106", "33322", "18533", "79.6", "313"]]}, "question": "How much greater is the population density in 2012 of the sector with the highest population density compared to the sector with the lowest population density?", "answer": "487"}
{"id": "b60f42005fcd5f0e80b5e791178df802", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["engine type", "scenario", "sfc in lb / (lbf h)", "sfc in g / (kn s)", "specific impulse (s)", "effective exhaust velocity (m / s)"], "data": [["nk - 33 rocket engine", "vacuum", "10.9", "309.0", "331", "3240"], ["ssme rocket engine", "space shuttle vacuum", "7.95", "225.0", "453", "4423"], ["ramjet", "mach 1", "4.5", "127.0", "800", "7877"], ["j - 58 turbojet", "sr - 71 at mach 3.2 (wet)", "1.9", "53.8", "1900", "18587"], ["rolls - royce / snecma olympus 593", "concorde mach 2 cruise (dry)", "1.195", "33.8", "3012", "29553"], ["cf6 - 80c2b1f turbofan", "boeing 747 - 400 cruise", "0.605", "17.1", "5950", "58400"], ["general electric cf6 turbofan", "sea level", "0.307", "8.696", "11700", "115000"]]}, "question": "How many engines have a specific impulse greater than 1000 seconds?", "answer": "4"}
{"id": "01e4774ada60feb0c31a3f78ab112c78", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "number of tropical storms", "number of hurricanes", "number of major hurricanes", "deaths", "strongest storm"], "data": [["1860", "1", "5", "1", "60 +", "one"], ["1861", "2", "6", "0", "22 +", "one and three"], ["1862", "3", "3", "0", "3", "two and three"], ["1863", "4", "5", "0", "90", "one , two , three & four"], ["1864", "2", "3", "0", "none", "one , three & five"], ["1865", "4", "3", "0", "326", "four & seven"], ["1866", "1", "5", "1", "383", "six"], ["1867", "2", "6", "0", "811", "'san narciso'"], ["1868", "1", "3", "0", "2", "one , two & four"]]}, "question": "Based on the historical data on tropical storms, hurricanes, and major hurricanes from 1860 to 1868, what can we predict for the number of major hurricanes in the next decade?", "answer": "No clear trend"}
{"id": "3afc9bfc5a2dfffbf342318f9bd41ee6", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["year (january)", "population (000)", "rural , %", "urban , %", "source"], "data": [["1939", "6081", "72", "28", "census"], ["1959", "9295", "56", "44", "census"], ["1970", "13001", "50", "50", "census"], ["1979", "14685", "46", "54", "census"], ["1989", "16537", "43", "57", "census"], ["1999", "14953", "43", "57", "census"], ["2002", "14851", "43", "57", "estimate"], ["2005", "15075", "43", "57", "estimate"], ["2008", "15572", "47", "53", "estimate"]]}, "question": "How does the urban percentage change with increasing population size for the years between 1959 and 1989?", "answer": "Urban percentage consistently increases with population size from 1959 to 1989 (correlation coefficient of 1.0)."}
{"id": "fde54f80bfc55153ce8d62c818c381df", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["subject", "no sat", "no passed", "% pass", "highest mark", "lowest mark", "mean"], "data": [["english", "55", "46", "84", "100", "37", "59"], ["mathematics", "55", "39", "71", "83", "36", "58"], ["biology", "17", "17", "100", "85", "54", "72"], ["chemistry", "20", "16", "80", "84", "43", "64"], ["physics", "10", "8", "80", "79", "47", "63"], ["accounting", "35", "27", "77", "75", "31", "58"], ["economics", "35", "33", "94", "88", "33", "63"], ["computer studies", "25", "19", "76", "78", "35", "56"], ["geography", "8", "7", "88", "76", "45", "64"], ["introduction to technology", "3", "3", "100", "69", "50", "61"], ["food technology", "9", "9", "100", "80", "50", "64"]]}, "question": "Which factors in the table, such as 'no sat', 'highest mark', 'lowest mark', and 'mean',contribute most to the '% pass' values for each subject? If none have an effect, please reply 'no clear impact'.", "answer": "lowest mark"}
{"id": "641049a7c6d1991bcab451db8e49ac54", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["rank", "country (or dependent territory)", "july 1 , 2013 projection", "% of pop", "average relative annual growth (%)", "average absolute annual growth"], "data": [["1", "egypt", "********.0", "22.81", "2.29", "1893000"], ["2", "algeria", "********.0", "10.32", "2.11", "792000"], ["3", "iraq", "35404000.0", "9.54", "3.06", "1051000"], ["4", "sudan", "35150000.0", "9.47", "2.52", "863000"], ["5", "morocco", "32950000.0", "8.88", "1.08", "353000"], ["6", "saudi arabia", "30193000.0", "8.14", "3.41", "997000"], ["7", "yemen", "25252000.0", "6.81", "2.96", "725000"], ["8", "syria", "22169000.0", "5.98", "2.45", "531000"], ["9", "tunisia", "10889000.0", "2.94", "1.03", "111000"], ["10", "somalia", "9662000.0", "2.6", "1.17", "112000"], ["11", "united arab emirates", "8659000.0", "2.33", "1.56", "133000"], ["12", "jordan", "6517000.0", "1.76", "2.84", "180000"], ["13", "libya", "6323000.0", "1.7", "1.56", "97000"], ["14", "palestine", "4421000.0", "1.19", "2.91", "125000"], ["15", "lebanon", "4127000.0", "1.11", "1.58", "64000"], ["16", "oman", "3942000.0", "1.06", "8.8", "319000"], ["17", "kuwait", "3852000.0", "1.04", "2.94", "110000"], ["18", "mauritania", "3461000.0", "0.93", "2.58", "87000"], ["19", "qatar", "1917000.0", "0.52", "3.85", "71000"], ["20", "bahrain", "1546000.0", "0.42", "7.36", "106000"], ["21", "djibouti", "912000.0", "0.25", "2.7", "24000"], ["22", "comoros", "743000.0", "0.2", "2.62", "19000"], ["align = left|total", "370989000", "100.0", "2.42", "8763000.0", "29"]]}, "question": "How much greater is the average relative annual growth rate of Egypt compared to Morocco?", "answer": "1.21"}
{"id": "3b25f146ef2692abc071056934ba47e7", "qtype": "NumericalReasoning", "qsubtype": "Counting", "table": {"columns": ["rank", "country (or dependent territory)", "july 1 , 2013 projection", "% of pop", "average relative annual growth (%)", "average absolute annual growth"], "data": [["1", "germany", "80640000.0", "15.99", "0.24", "196000"], ["2", "united kingdom", "64231000.0", "12.73", "0.73", "465000"], ["3", "france", "63820000.0", "12.65", "0.49", "309000"], ["4", "italy", "59789000.0", "11.85", "0.35", "206000"], ["5", "spain", "46958000.0", "9.31", "- 0.43", "- 205000"], ["6", "poland", "38548000.0", "7.64", "0.08", "29000"], ["7", "romania", "19858000.0", "3.94", "- 0.77", "- 155000"], ["8", "netherlands", "16795000.0", "3.33", "0.33", "55000"], ["9", "belgium", "11162000.0", "2.21", "0.66", "73000"], ["10", "greece", "10758000.0", "2.13", "- 0.13", "- 14000"], ["11", "portugal", "10609000.0", "2.1", "0.19", "20000"], ["12", "czech republic", "10519000.0", "2.09", "0.23", "24000"], ["13", "hungary", "9894000.0", "1.96", "- 0.25", "- 25000"], ["14", "sweden", "9595000.0", "1.9", "0.76", "72000"], ["15", "austria", "8477000.0", "1.68", "0.61", "51000"], ["16", "bulgaria", "7261000.0", "1.44", "- 0.59", "- 43000"], ["17", "denmark", "5612000.0", "1.11", "0.45", "25000"], ["18", "finland", "5436000.0", "1.08", "0.44", "24000"], ["19", "slovakia", "5413000.0", "1.07", "0.15", "8000"], ["20", "ireland", "4662000.0", "0.92", "1.35", "62000"], ["21", "croatia", "4258000.0", "0.84", "- 0.35", "- 15000"], ["22", "lithuania", "2956000.0", "0.59", "- 1.30", "- 39000"], ["23", "slovenia", "2062000.0", "0.41", "0.24", "5000"], ["24", "latvia", "2011000.0", "0.4", "- 1.23", "- 25000"], ["25", "estonia", "1283000.0", "0.25", "- 0.62", "- 8000"], ["26", "cyprus", "888000.0", "0.18", "1.95", "17000"], ["27", "luxembourg", "542000.0", "0.11", "1.88", "10000"], ["28", "malta", "419000.0", "0.08", "0.48", "2000"], ["align = left|total", "504456000", "100.0", "0.22", "1124000", "311"]]}, "question": "How many countries have a population of over 50 million according to the 2013 projection?", "answer": "4"}
{"id": "9ecabd8f7a2216e40154f32530e59947", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["Unnamed: 0", "Average population (x 1000)", "Live births", "Deaths", "Natural change", "Crude birth rate (per 1000)", "Crude death rate (per 1000)", "Natural change (per 1000)"], "data": [["1970", "31", "683", "356", "327", "22.0", "11.5", "10.5"], ["1975", "33", "706", "374", "332", "21.4", "11.3", "10.1"], ["1980", "35", "701", "351", "350", "20.0", "10.0", "10.0"], ["1985", "37", "793", "289", "504", "21.4", "7.8", "13.6"], ["1990", "38", "635", "342", "293", "16.9", "9.1", "7.8"], ["1991", "38", "623", "350", "273", "16.6", "9.3", "7.3"], ["1992", "37", "611", "369", "242", "16.7", "10.1", "6.6"], ["1993", "34", "459", "433", "26", "13.3", "12.6", "0.8"], ["1994", "32", "433", "460", "- 27", "13.5", "14.3", "-0.8"], ["1995", "31", "382", "481", "- 99", "12.5", "15.8", "-3.2"], ["1996", "29", "374", "436", "- 62", "12.7", "14.8", "-2.1"], ["1997", "29", "373", "400", "- 27", "13.0", "13.9", "-0.9"], ["1998", "28", "396", "355", "41", "14.2", "12.7", "1.5"], ["1999", "27", "319", "397", "- 78", "11.8", "14.7", "-2.9"], ["2000", "26", "289", "391", "- 102", "11.0", "14.9", "-3.9"], ["2001", "26", "298", "390", "- 92", "11.6", "15.1", "-3.6"], ["2002", "25", "310", "376", "- 66", "12.3", "14.9", "-2.6"], ["2003", "24", "268", "462", "- 194", "11.0", "19.0", "-8.0"], ["2004", "24", "339", "463", "- 124", "14.4", "19.7", "-5.3"], ["2005", "23", "294", "466", "- 172", "12.9", "20.5", "-7.6"], ["2006", "22", "270", "366", "- 96", "12.3", "16.7", "-4.4"], ["2007", "21", "280", "351", "- 71", "13.2", "16.5", "-3.3"], ["2008", "20", "267", "368", "- 101", "13.0", "18.0", "-4.9"], ["2009", "20", "268", "365", "- 97", "13.6", "18.5", "-4.9"], ["2010", "19", "233", "397", "- 164", "12.3", "20.9", "-8.7"]]}, "question": "In which year did the crude birth rate (per 1000) exceed the crude death rate (per 1000) by the largest margin?", "answer": "1985"}
{"id": "cf3ad747caa0247ad77c95ead07d364f", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["election", "dã¡il", "share of votes", "seats", "total seats"], "data": [["1927 (jun)", "5th", "26.2%", "44", "153"], ["1927 (sep)", "6th", "35.2%", "57", "153"], ["1932", "7th", "44.5%", "72", "153"], ["1933", "8th", "49.7%", "76", "153"], ["1937", "9th", "45.2%", "68", "138"], ["1938", "10th", "51.9%", "76", "138"], ["1943", "11th", "41.8%", "66", "138"], ["1944", "12th", "48.9%", "75", "138"], ["1948", "13th", "41.9%", "67", "147"], ["1951", "14th", "46.3%", "68", "147"], ["1954", "15th", "43.4%", "65", "147"], ["1957", "16th", "48.3%", "78", "147"], ["1961", "17th", "43.8%", "70", "144"], ["1965", "18th", "47.7%", "72", "144"], ["1969", "19th", "44.6%", "74", "144"], ["1973", "20th", "46.2%", "68", "144"], ["1977", "21st", "50.6%", "84", "148"], ["1981", "22nd", "45.3%", "77", "166"], ["1982 (feb)", "23rd", "47.3%", "81", "166"], ["1982 (nov)", "24th", "45.2%", "75", "166"], ["1987", "25th", "44.2%", "81", "166"], ["1989", "26th", "44.2%", "77", "166"], ["1992", "27th", "39.1%", "68", "166"], ["1997", "28th", "39.3%", "77", "166"], ["2002", "29th", "41.5%", "81", "166"], ["2007", "30th", "41.6%", "77", "166"], ["2011", "31st", "17.4%", "20", "166"]]}, "question": "Which election had the highest 'share of votes', and how is the difference compared to the election with the lowest?", "answer": "1938, 34.50%"}
{"id": "20e1c96525644ffc2d2b4f807f0c8901", "qtype": "DataAnalysis", "qsubtype": "ImpactAnalysis", "table": {"columns": ["rank", "company", "headquarters", "industry", "sales (billion )", "profits (billion )", "assets (billion )", "market value (billion )"], "data": [["1", "citigroup", "usa", "banking", "146.56", "21.54", "1884.32", "247.42"], ["2", "bank of america", "usa", "banking", "116.57", "21.13", "1459.74", "226.61"], ["3", "hsbc", "uk", "banking", "121.51", "16.63", "1860.76", "202.29"], ["4", "general electric", "usa", "conglomerate", "163.39", "20.83", "697.24", "358.98"], ["5", "jpmorgan chase", "usa", "banking", "99.3", "14.44", "1351.52", "170.97"], ["6", "american international group", "usa", "insurance", "113.19", "14.01", "979.41", "174.47"], ["7", "exxonmobil", "usa", "oil and gas", "335.09", "39.5", "223.95", "410.65"], ["8", "royal dutch shell", "netherlands", "oil and gas", "318.85", "25.44", "232.31", "208.25"], ["9", "ubs", "switzerland", "diversified financials", "105.59", "9.78", "1776.89", "116.84"], ["10", "ing group", "netherlands", "diversified financials", "153.44", "9.65", "1615.05", "93.99"], ["11", "bp", "uk", "oil and gas", "265.91", "22.29", "217.6", "198.14"], ["12", "toyota", "japan", "automotive", "179.02", "11.68", "243.6", "217.69"], ["13", "the royal bank of scotland", "uk", "banking", "77.41", "12.51", "1705.35", "124.13"], ["14", "bnp paribas", "france", "banking", "89.16", "9.64", "1898.19", "97.03"], ["15", "allianz", "germany", "insurance", "125.33", "8.81", "1380.88", "87.22"], ["16", "berkshire hathaway", "usa", "diversified financials", "98.54", "11.02", "248.44", "163.79"], ["17", "walmart", "usa", "retailing", "348.65", "11.29", "151.19", "201.36"], ["18", "barclays", "uk", "banking", "67.71", "8.95", "1949.17", "94.79"], ["19", "chevron", "usa", "oil and gas", "195.34", "17.14", "132.63", "149.37"], ["19", "total sa", "france", "oil and gas", "175.05", "15.53", "138.82", "152.62"]]}, "question": "Which is the main factor in the table, such as 'sales (billion)', 'profits (billion)', and 'assets (billion)', significantly contribute to the 'market value (billion)' of the companies listed?", "answer": "profits"}
{"id": "fa122c72bb9993414246ed7ba7f9ac79", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["chambering", "p1 diameter (mm)", "a external (cm 2 )", "p max ( bar )", "f bolt ( kgf )", "f bolt"], "data": [["5.45x39 mm", "10.0", "0.7854", "3800", "2985", "n ( lbf )"], [".223 remington", "9.58", "0.7208", "4300", "3099", "n (lbf)"], ["7.62x39 mm", "11.35", "1.0118", "3550", "3592", "n (lbf)"], [".308 winchester", "11.96", "1.1234", "4150", "4662", "n (lbf)"], [".300 winchester magnum", "13.03", "1.3335", "4300", "5734", "n (lbf)"], [".300 wsm", "14.12", "1.5659", "4450", "6968", "n (lbf)"], [".300 remington ultra magnum", "13.97", "1.5328", "4480", "6876", "n (lbf)"], [".338 lapua magnum", "14.91", "1.746", "4200", "7333", "n (lbf)"], [".300 lapua magnum", "14.91", "1.746", "4700", "8339", "n (lbf)"], [".50 bmg", "20.42", "3.2749", "3700", "12117", "n (lbf)"]]}, "question": "What is the mean and standard deviation of `p max ( bar )` for all ammunition types in the table?", "answer": "4163, 370.02"}
{"id": "30b79e19a0d46928045a2eeaf12733ed", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "property taxes", "investment earnings", "other local sources", "state & federal", "total revenue"], "data": [["2005", "24384901", "255041", "2670060", "13581968", "40891700"], ["2004", "21099214", "181103", "2624131", "13999169", "37903617"], ["2003", "17199210", "509862", "2309087", "12794922", "32539572"], ["2002", "14359199", "879398", "2168096", "15132879", "32539572"], ["2001", "11631227", "1949885", "1987436", "12929489", "28498037"], ["2000", "10608734", "493839", "2127480", "8549565", "21779618"]]}, "question": "How has the total revenue trended over the years?", "answer": "Increasing trend"}
{"id": "06cf0ed5987ea6984c584de1d8eda280", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["#", "Mayor", "Taking Office", "Leaving"], "data": [["1", "Vivian Burrill", "1901", "1902"], ["2", "Arthur Dufresne", "1902", "1902"], ["3", "Beaudry Leman", "1902", "1908"], ["1", "Vivian Burrill", "1908", "1913"], ["4", "Joseph-Auguste Frigon", "1913", "1915"], ["5", "Edmond Thibaudeau", "1915", "1917"], ["4", "Joseph-Auguste Frigon", "1917", "1918"], ["6", "Napoléon Désaulniers", "1918", "1920"], ["7", "Joseph-Alexis Dufresne", "1920", "1928"], ["6", "Napoléon Désaulniers", "1928", "1930"], ["8", "Albert Gigaire", "1930", "1936"], ["9", "Lucien Bourassa", "1936", "1937"], ["10", "Alexandre Gélinas", "1937", "1938"], ["11", "J.A. Bilodeau", "1938", "1946"], ["12", "François Roy", "1946", "1954"], ["13", "Gaston Hardy", "1954", "1957"], ["14", "Armand Foucher", "1957", "1963"], ["15", "Gérard Dufresne", "1963", "1966"], ["16", "Maurice Bruneau", "1966", "1970"], ["17", "Dominique Grenier", "1970", "1986"], ["18", "Roland Désaulniers", "1986", "1994"], ["19", "Lise Landry", "1994", "2009"], ["20", "Michel Angers", "2009", "Current"]]}, "question": "What is the total number of years served by all mayors listed in the table?", "answer": "108"}
{"id": "47219e7225da35f61cb5307288f2eac3", "qtype": "DataAnalysis", "qsubtype": "StatisticalAnalysis", "table": {"columns": ["asian rank", "world rank", "country", "gdp per capita", "gdp world rank"], "data": [["1", "1", "qatar", "85638", "69"], ["2", "4", "brunei", "50790", "113"], ["3", "5", "singapore", "49754", "44"], ["4", "9", "kuwait", "39344", "56"], ["5", "14", "united arab emirates", "37941", "55"], ["6", "22", "japan", "33596", "3"], ["7", "24", "bahrain", "31899", "105"], ["8", "26", "republic of china (taiwan)", "30322", "19"], ["9", "31", "israel", "27147", "52"], ["10", "34", "south korea", "24803", "14"], ["11", "36", "oman", "23987", "77"], ["12", "38", "saudi arabia", "22852", "22"], ["13", "59", "malaysia", "13385", "30"], ["14", "66", "lebanon", "11279", "84"], ["15", "71", "iran", "10570", "18"], ["16", "83", "thailand", "7907", "24"], ["17", "100", "people 's republic of china", "7325", "2"], ["18", "105", "jordan", "6976", "99"], ["19", "106", "bhutan", "6962", "n / a"], ["20", "109", "maldives", "4603", "n / a"], ["21", "111", "syria", "6892", "63"], ["22", "113", "sri lanka", "6765", "65"], ["23", "120", "indonesia", "6728", "16"], ["24", "122", "philippines", "3383", "37"], ["25", "124", "mongolia", "3222", "141"], ["26", "127", "pakistan", "2594", "26"], ["27", "128", "vietnam", "2589", "46"], ["28", "129", "india", "2563", "4"], ["29", "107", "east timor", "4770", "156"], ["30", "132", "yemen", "2343", "81"], ["31", "136", "laos", "2054", "128"], ["32", "140", "papua new guinea", "1974", "131"], ["33", "143", "cambodia", "1818", "103"], ["34", "153", "bangladesh", "1311", "48"], ["35", "159", "nepal", "3397", "96"], ["36", "161", "burma", "1040", "78"]]}, "question": "What is the median GDP per capita of the top 20 countries by world rank?", "answer": "24395"}
{"id": "5eb7d24afce65f474b46fe270f680c2e", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["year", "theme", "artist", "finish", "issue price", "total mintage"], "data": [["2002", "golden tulip", "anthony testa", "proof (selectively gold plated)", "24.95", "19986"], ["2003", "golden daffodil", "christie paquet", "proof (selectively gold plated)", "34.95", "36293"], ["2004", "golden easter lily", "christie paquet", "proof (selectively gold plated)", "34.95", "23486"], ["2005", "golden rose", "christie paquet", "proof (selectively gold plated)", "34.95", "23000"], ["2006", "golden daisy", "christie paquet", "proof (selectively gold plated)", "34.95", "23000"], ["2007", "golden forget - me - not", "christie paquet", "proof (selectively gold plated)", "38.95", "20000"]]}, "question": "What is the average percentage increase in total mintage from one year to the next for the coins with an issue price of $34.95?", "answer": "-12.45%."}
{"id": "63778f1c58e40f5f1d126bdcb4c30665", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["School", "2007", "2008", "2009", "2010", "2011"], "data": [["Francisco Bravo Medical Magnet High School", "807.0", "818", "815", "820", "832.0"], ["Marc and Eva Stern Math and Science School", "718.0", "792", "788", "788", "809.0"], ["Oscar De La Hoya Animo Charter High School", "662.0", "726", "709", "710", "744.0"], ["James A. Garfield High School", "553.0", "597", "593", "632", "705.0"], ["Abraham Lincoln High School", "594.0", "609", "588", "616", "643.0"], ["Woodrow Wilson High School", "582.0", "585", "600", "615", "636.0"], ["Theodore Roosevelt High School", "557.0", "551", "576", "608", null], ["Thomas Jefferson High School", "457.0", "516", "514", "546", "546.0"], ["Santee Education Complex", null, "502", "521", "552", "565.0"]]}, "question": "What is the average score of Francisco Bravo Medical Magnet High School from 2007 to 2011?", "answer": "818.4"}
{"id": "c1ed7900082c6c50b396f9e4d696e45e", "qtype": "DataAnalysis", "qsubtype": "AnomalyDetection", "table": {"columns": ["county", "1948", "1956", "1966", "1977", "1992", "2002", "2011"], "data": [["zzz bucharest", "1025180", "1177661", "1366684", "1807239", "2064474", "1926334", "1883425"], ["iași", "431586", "516635", "619027", "729243", "806778", "816910", "772348"], ["prahova", "557776", "623817", "701057", "817168", "873229", "829945", "762886"], ["cluj", "520073", "580344", "629746", "715507", "735077", "702755", "691106"], ["constanța", "311062", "369940", "465752", "608817", "748044", "715151", "684082"], ["timiș", "588936", "568881", "607596", "696884", "700292", "677926", "683540"], ["dolj", "615301", "642028", "691116", "750328", "761074", "734231", "660544"], ["suceava", "439751", "507674", "572781", "633899", "700799", "688435", "634810"], ["bacău", "414996", "507937", "598321", "667791", "736078", "706623", "616168"], ["argeș", "448964", "483741", "529833", "631918", "680574", "652625", "612431"], ["bihor", "536323", "574488", "586460", "633094", "634093", "600246", "575398"], ["mureș", "461403", "513261", "561598", "605345", "607298", "580851", "550846"], ["brașov", "300836", "373941", "442692", "582863", "642513", "589028", "549217"], ["galați", "341797", "396138", "474279", "581561", "639853", "619556", "536167"], ["dmbovița", "409272", "438985", "453241", "527620", "559874", "541763", "518745"], ["maramureș", "321287", "367114", "427645", "492860", "538534", "510110", "478659"], ["neamț", "357348", "419949", "470206", "532096", "577619", "554516", "470766"], ["buzău", "430225", "465829", "480951", "508424", "516307", "496214", "451069"], ["olt", "442442", "458982", "476513", "518804", "520966", "489274", "436400"], ["arad", "476207", "475620", "481248", "512020", "487370", "461791", "430629"], ["hunedoara", "306955", "381902", "474602", "514436", "547993", "485712", "418565"], ["botoșani", "385236", "428050", "452406", "451217", "458904", "452834", "412626"], ["sibiu", "335116", "372687", "414756", "481645", "452820", "421724", "397322"], ["vaslui", "344917", "401626", "431555", "437251", "457799", "455049", "395499"], ["ilfov", "167533", "196265", "229773", "287738", "286510", "300123", "388738"], ["teleorman", "487394", "510488", "516222", "518943", "482281", "436025", "380123"], ["vlcea", "341590", "362356", "368779", "414241", "436298", "413247", "371714"], ["satu mare", "312672", "337351", "359393", "393840", "400158", "367281", "344360"], ["alba", "361062", "370800", "382786", "409634", "414227", "382747", "342376"], ["gorj", "280524", "293031", "298382", "348521", "400100", "387308", "341594"], ["vrancea", "290183", "326532", "351292", "369740", "392651", "387632", "340310"], ["brăila", "271251", "297276", "339954", "377954", "392069", "373174", "321212"], ["harghita", "258495", "273964", "282392", "326310", "347637", "326222", "310867"], ["călărași", "287722", "318573", "337261", "338807", "338844", "324617", "306691"], ["caraș - severin", "302254", "327787", "358726", "385577", "375794", "333219", "295579"], ["bistrița - năsăud", "233650", "255789", "269954", "286628", "327238", "311657", "286225"], ["giurgiu", "313793", "325045", "320120", "327494", "313084", "297859", "281422"], ["ialomiţa", "244750", "274655", "291373", "295965", "304008", "296572", "274148"], ["mehedinți", "304788", "304091", "310021", "322371", "332091", "306732", "265390"], ["sălaj", "262580", "271989", "263103", "264569", "266308", "248015", "224384"], ["tulcea", "192228", "223719", "236709", "254531", "270197", "256492", "213083"], ["covasna", "157166", "172509", "176858", "199017", "232592", "222449", "210177"], ["total", "15872624", "17489450", "19103163", "21559910", "22760449", "21680974", "20121641"]]}, "question": "Which counties in the table exhibit unusual data patterns in specific years?", "answer": "No countries has anomalies in the table."}
{"id": "909198221c6530a86885112b88cf997d", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["pilot", "organization", "total flights", "usaf space flights", "fai space flights", "max mach", "max speed (mph)", "max altitude (miles)"], "data": [["michael j adams", "us air force", "7", "1", "0", "5.59", "3822", "50.3"], ["neil armstrong", "nasa", "7", "0", "0", "5.74", "3989", "39.2"], ["scott crossfield", "north american aviation", "14", "0", "0", "2.97", "1959", "15.3"], ["william h dana", "nasa", "16", "2", "0", "5.53", "3897", "58.1"], ["joseph h engle", "us air force", "16", "3", "0", "5.71", "3887", "53.1"], ["william j pete knight", "us air force", "16", "1", "0", "6.7", "4519", "53.1"], ["john b mckay", "nasa", "29", "1", "0", "5.65", "3863", "55.9"], ["forrest s petersen", "us navy", "5", "0", "0", "5.3", "3600", "19.2"], ["robert a rushworth", "us air force", "34", "1", "0", "6.06", "4017", "53.9"], ["milton o thompson", "nasa", "14", "0", "0", "5.48", "3723", "40.5"], ["joseph a walker", "nasa", "25", "3", "2", "5.92", "4104", "67.0"]]}, "question": "Is there a causal relationship between the total number of flights and the maximum altitude reached by a pilot?", "answer": "Yes, a correlation coefficient of 0.57 indicates a moderate positive causal relationship between the total number of flights and the maximum altitude reached by a pilot."}
{"id": "72edfab023d22e153488d63e733711fd", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["frequency (hz)", "r (î / km)", "l (mh / km)", "g (î¼s / km)", "c (nf / km)"], "data": [["1", "172.24", "0.6129", "0.0", "51.57"], ["1k", "172.28", "0.6125", "0.072", "51.57"], ["10k", "172.7", "0.6099", "0.531", "51.57"], ["100k", "191.63", "0.5807", "3.327", "51.57"], ["1 m", "463.59", "0.5062", "29.111", "51.57"], ["2 m", "643.14", "0.4862", "53.205", "51.57"]]}, "question": "Can you describe the main characteristics of the table, including the purpose of each column and any notable trends observed in the data?", "answer": "The table details the electrical characteristics of a transmission line across various frequencies, specifying resistance, inductance, conductance, and capacitance per kilometer. Key observations reveal that resistance and conductance rise with frequency, while inductance decreases and capacitance remains constant throughout the frequency spectrum."}
{"id": "29c36dbc873ed833d3fdc8c19375453b", "qtype": "NumericalReasoning", "qsubtype": "Comparison", "table": {"columns": ["place", "code", "area (km 2 )", "population", "most spoken language"], "data": [["abantungwa / kholwa", "51401", "245.25", "57273", "zulu"], ["colenso", "51402", "4.9", "4476", "zulu"], ["ezakheni", "51404", "39.0", "59943", "zulu"], ["kliprivier nu", "51406", "2.8", "381", "zulu"], ["kliprivier", "51405", "107.21", "27696", "zulu"], ["ladysmith", "51407", "91.97", "41425", "zulu"], ["mchunu", "51408", "34.42", "2301", "zulu"], ["mthembu", "51409", "80.7", "6920", "zulu"], ["mvelani", "51410", "2.43", "11898", "zulu"], ["nkankezi", "51411", "4.86", "824", "zulu"], ["remainder of the municipality", "51403", "2350.72", "12316", "zulu"]]}, "question": "How much larger is the area of the \"remainder of the municipality\" compared to the area of \"ladysmith\"?", "answer": "2258.75"}
{"id": "2db548f48fdb4c88e453aa5013ee9448", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["institution", "location", "established", "gained university status", "vice - chancellor", "total number of students", "research funding (000)"], "data": [["birkbeck , university of london", "london", "1823", "1920", "professor david latchman", "19020", "9985"], ["university of east anglia", "norwich", "1963", "1963", "professor edward acton", "19585", "16482"], ["university of essex", "colchester", "1964", "1964", "professor anthony forster", "11690", "9967"], ["goldsmiths , university of london", "london", "1891", "1904", "dr pat loughrey", "7615", "8539"], ["institute of education , university of london", "london", "1902", "1932", "professor chris husbands", "7215", "7734"], ["university of lancaster", "lancaster", "1964", "1964", "professor mark smith", "12695", "18640"], ["university of leicester", "leicester", "1921", "1957", "professor robert burgess", "16160", "22225"], ["loughborough university", "loughborough", "1909", "1966", "professor robert allison", "17825", "22398"], ["royal holloway , university of london", "egham", "1849", "1900", "professor paul layzell (principal)", "7620", "13699"], ["soas , university of london", "london", "1916", "1916", "professor paul webley", "4525", "7238"], ["university of sussex", "brighton", "1961", "1961", "professor michael farthing", "12415", "16196"]]}, "question": "Which university has the highest research funding, and how is the difference compared to the university with the lowest research funding(000)?", "answer": "loughborough university,15160"}
{"id": "979a0eff0ecb9837c0a9c7968160830e", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["no", "name", "mi from kingston", "km from kingston", "parish", "length feet", "length meters"], "data": [["1", "scotts pass", "44.25", "71.2", "clarendon", "70", "21.3"], ["2", "scotts pass", "44.5", "71.6", "clarendon", "170", "51.8"], ["3", "comfort hall", "65.5", "105.4", "st elizabeth", "688", "209.1"], ["4", "balaclava", "70.0", "112.6", "st elizabeth", "348", "106.1"], ["5", "highworth", "84.0", "135.2", "st elizabeth", "182", "55.5"], ["6", "y s", "84.5", "136.0", "st elizabeth", "218", "66.4"], ["7", "ipswich", "86.25", "138.8", "st elizabeth", "855", "260.6"], ["8", "unnamed", "87.75", "141.2", "st james", "555", "164.6"], ["9", "merrywood", "88.5", "142.4", "st james", "362", "115.8"], ["10", "anchovy", "104.5", "168.2", "st james", "102", "31.1"], ["11", "ramble", "108.0", "173.8", "st james", "182", "55.5"], ["12", "bogue hill", "108.5", "174.6", "st james", "1276", "388.9"]]}, "question": "What is the correlation between the 'distance from Kingston (km)' and 'road length (m)' in the table? Provide the correlation coefficient as evidence.", "answer": "Weak positive correlation, 0.34"}
{"id": "f6ecd642029c0d49a2548e984de32a34", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["s dam and gnis query link", "s lake and gnis query link", "s reservoir and gnis query link", "borough or census area", "comment"], "data": [["5", "27", "0", "aleutians east", "lakes in table , reservoirs done"], ["15", "134", "0", "aleutians west (ca)", "lakes , reservoirs done"], ["8", "58", "6", "anchorage", "lakes and reservoirs in table"], ["0", "81", "0", "bethel (ca)", "lakes , reservoirs done"], ["0", "0", "0", "bristol bay", "lakes and reservoirs done"], ["0", "50", "0", "denali", "lakes in table , reservoirs done"], ["0", "55", "0", "dillingham (ca)", "lakes , reservoirs done"], ["3", "19", "1", "fairbanks north star", "lakes and reservoirs in table"], ["3", "10", "0", "haines", "lakes in table , reservoirs done"], ["6", "55", "3", "hoonah - angoon (ca)", "lakes and reservoirs in table"], ["8", "31", "5", "juneau", "lakes and reservoirs in table"], ["10", "440", "4", "kenai peninsula", "lakes , reservoirs in table"], ["12", "57", "8", "ketchikan gateway", "lakes , reservoirs in table"], ["31", "82", "11", "kodiak island", "lakes , reservoirs in table"], ["3", "83", "0", "lake and peninsula", "lakes , reservoirs done"], ["5", "451", "1", "matanuska - susitna", "lakes , reservoirs in table"], ["1", "36", "0", "nome (ca)", "lakes in table , reservoirs done"], ["2", "142", "2", "north slope", "lakes , reservoirs in table"], ["1", "80", "1", "northwest arctic", "lakes , reservoirs in table"], ["9", "163", "4", "p of wales - o ketchikan (ca)", "lakes , reservoirs in table"], ["9", "90", "3", "sitka", "lakes , reservoirs in table"], ["3", "9", "3", "skagway", "lakes and reservoirs in table"], ["0", "130", "0", "southeast fairbanks (ca)", "lakes , reservoirs in table"], ["22", "293", "10", "valdez - cordova (ca)", "lakes , reservoirs in table"], ["1", "21", "0", "wade hampton (ca)", "lakes in table , reservoirs done"], ["8", "60", "5", "wrangell - petersburg (ca)", "lakes , reservoirs in table"], ["0", "26", "0", "yakutat", "lakes in table , reservoirs done"], ["2", "513", "0", "yukon - koyukuk (ca)", "lakes , reservoirs done"]]}, "question": "Which borough or census area has the highest number of lakes and reservoirs combined, based on the 's lake and gnis query link' and 's reservoir and gnis query link' columns?", "answer": "yukon - koyukuk (ca)"}
{"id": "2a81093974f2cc5cb278ad23d0b23d74", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["episode", "18 - 49", "viewers (m)", "rating", "share"], "data": [["hero", "3.2 / 8", "12.16", "7.2", "11"], ["project run away", "2.8 / 7", "10.69", "6.3", "10"], ["dmv", "2.6 / 6", "10.86", "6.6", "10"], ["40 days", "2.4 / 6", "9.91", "6.0", "9"], ["burn , bougainvillea , burn", "1.0 / 3", "2.83", "1.9", "4"], ["if the shoe fits , steal it", "0.6 / 3", "2.87", "1.3", "3"], ["dirty stevie", "0.6 / 3", "2.59", "1.8", "4"], ["the game of life", "0.7 / 3", "2.76", "1.7", "4"], ["nothing for money", "0.5 / 2", "2.23", "1.8", "4"], ["school council", "0.7 / 3", "2.62", "1.7", "4"], ["three end tables", "0.6 / 3", "2.42", "tba", "tba"], ["desperate housewife", "0.6 / 3", "2.6", "1.6", "4"], ["no reception", "0.7 / 3", "2.73", "1.8", "4"]]}, "question": "Does an increase in viewers (m) cause an increase in the rating?", "answer": "Yes, a strong positive correlation (0.99) indicates that an increase in viewers causally influences the rating."}
{"id": "8d8faa04091d6652a0503ea81462de9f", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["Builder", "Year", "Works No.", "CSAR No.", "SAR No.", "Class"], "data": [["NBL", "1910", "18976", "670", "752", "10B"], ["NBL", "1910", "18977", "671", "753", "10BR"], ["NBL", "1910", "18978", "672", "754", "10BR"], ["NBL", "1910", "18979", "673", "755", "10B"], ["NBL", "1910", "18980", "674", "756", "10BR"], ["BP", "1911", "5483", "-", "757", "10B"], ["BP", "1911", "5484", "-", "758", "10BR"], ["BP", "1911", "5486", "-", "759", "10BR"], ["BP", "1911", "5487", "-", "760", "10B"], ["BP", "1911", "5485", "-", "761", "10B"]]}, "question": "In which year did the builder with 10BR class has most works?", "answer": "1910"}
{"id": "1307e6264752997dde75a6bd237b9e28", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["driver", "car", "make", "points", "laps", "winnings"], "data": [["kasey kahne", "9", "dodge", "185", "334", "530164"], ["matt kenseth", "17", "ford", "175", "334", "362491"], ["tony stewart", "20", "chevrolet", "175", "334", "286386"], ["denny hamlin", "11", "chevrolet", "165", "334", "208500"], ["kevin harvick", "29", "chevrolet", "160", "334", "204511"], ["jeff burton", "31", "chevrolet", "150", "334", "172220"], ["scott riggs", "10", "dodge", "146", "334", "133850"], ["martin truex jr", "1", "chevrolet", "147", "334", "156608"], ["mark martin", "6", "ford", "143", "334", "151850"], ["bobby labonte", "43", "dodge", "134", "334", "164211"], ["jimmie johnson", "48", "chevrolet", "130", "334", "165161"], ["dale earnhardt jr", "8", "chevrolet", "127", "334", "154816"], ["reed sorenson", "41", "dodge", "124", "334", "126675"], ["casey mears", "42", "dodge", "121", "334", "150233"], ["kyle busch", "5", "chevrolet", "118", "334", "129725"], ["ken schrader", "21", "ford", "115", "334", "140089"], ["dale jarrett", "88", "ford", "112", "334", "143350"], ["jeff green", "66", "chevrolet", "114", "334", "133833"], ["clint bowyer", "7", "chevrolet", "106", "333", "116075"], ["robby gordon", "7", "chevrolet", "103", "333", "109275"], ["david stremme", "40", "dodge", "100", "333", "127033"], ["jeff gordon", "24", "chevrolet", "97", "332", "148411"], ["joe nemechek", "1", "chevrolet", "94", "332", "129070"], ["tony raines", "96", "chevrolet", "91", "332", "97075"], ["terry labonte", "44", "chevrolet", "88", "332", "95975"], ["michael waltrip", "55", "dodge", "85", "331", "108833"], ["travis kvapil", "32", "chevrolet", "82", "331", "105122"], ["scott wimmer", "4", "chevrolet", "79", "330", "94075"], ["dave blaney", "22", "dodge", "76", "330", "92475"], ["sterling marlin", "14", "chevrolet", "73", "329", "89325"], ["jeremy mayfield", "19", "dodge", "70", "328", "116891"], ["kevin lepage", "61", "ford", "67", "328", "85800"], ["elliott sadler", "38", "ford", "69", "286", "113558"], ["kurt busch", "2", "dodge", "61", "286", "124633"], ["jj yeley", "18", "chevrolet", "63", "270", "118075"], ["carl edwards", "99", "ford", "60", "256", "101175"], ["jamie mcmurray", "26", "ford", "52", "254", "127100"], ["mike garvey", "151", "chevrolet", "49", "251", "79125"], ["kyle petty", "45", "dodge", "46", "248", "87000"], ["ryan newman", "12", "dodge", "43", "200", "124283"], ["derrike cope", "74", "dodge", "pe", "169", "78760"], ["greg biffle", "16", "ford", "42", "81", "98860"], ["brian vickers", "25", "chevrolet", "34", "24", "86847"]]}, "question": "What is the total amount of winnings for all drivers who drove a Chevrolet car, and which driver among them has the highest winnings?", "answer": "2880210, tony stewart"}
{"id": "9c3b3255f540891cedc76da5e251d40f", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["rank", "english title", "chinese title", "average", "peak", "premiere", "finale", "hk viewers"], "data": [["1", "the family link", "師奶兵團", "33", "42", "31", "33", "2.12 million"], ["2", "fathers and sons", "爸爸閉翳", "32", "40", "31", "37", "2.11 million"], ["3", "heart of greed", "溏心風暴", "32", "48", "29", "40", "2.08 million"], ["4", "ten brothers", "十兄弟", "32", "39", "29", "36", "2.05 million"], ["5", "on the first beat", "學警出更", "31", "38", "30", "35", "2.03 million"], ["6", "the green grass of home", "緣來自有機", "31", "36", "29", "33", "2.01 million"], ["7", "dicey business", "賭場風雲", "31", "37", "30", "34", "1.99 million"], ["8", "steps", "舞動全城", "31", "36", "31", "32", "1.98 million"], ["9", "the drive of life", "歲月風雲", "30", "39", "31", "33", "1.97 million"]]}, "question": "What is the total average viewership of the top 5 TV shows?", "answer": "160"}
{"id": "5876f5eaa7677055acaf8f89e1215cd8", "qtype": "NumericalReasoning", "qsubtype": "Domain-Specific", "table": {"columns": ["country", "orphans as % of all children", "aids orphans as % of orphans", "total orphans (total)", "total orphans (aids related)", "maternal (total)", "maternal (aids related)", "paternal (total)", "paternal (aids related)", "double (total)", "double (aids related)"], "data": [["botswana (1990)", "5.9", "3.0", "34000", "1000", "14000", "< 100", "23000", "1000", "2000", "< 100"], ["botswana (1995)", "8.3", "33.7", "52000", "18000", "19000", "7000", "37000", "13000", "5000", "3000"], ["botswana (2001)", "15.1", "70.5", "98000", "69000", "69000", "58000", "91000", "69000", "62000", "61000"], ["lesotho (1990)", "10.6", "2.9", "73000", "< 100", "31000", "< 100", "49000", "< 100", "8000", "< 100"], ["lesotho (1995)", "10.3", "5.5", "77000", "4000", "31000", "1000", "52000", "4000", "7000", "1000"], ["lesotho (2001)", "17.0", "53.5", "137000", "73000", "66000", "38000", "108000", "63000", "37000", "32000"], ["malawi (1990)", "11.8", "5.7", "524000", "30000", "233000", "11000", "346000", "23000", "55000", "6000"], ["malawi (1995)", "14.2", "24.6", "664000", "163000", "305000", "78000", "442000", "115000", "83000", "41000"], ["malawi (2001)", "17.5", "49.9", "937000", "468000", "506000", "282000", "624000", "315000", "194000", "159000"], ["uganda (1990)", "12.2", "17.4", "1015000", "177000", "437000", "72000", "700000", "138000", "122000", "44000"], ["uganda (1995)", "14.9", "42.4", "1456000", "617000", "720000", "341000", "1019000", "450000", "282000", "211000"], ["uganda (2001)", "14.6", "51.1", "1731000", "884000", "902000", "517000", "1144000", "581000", "315000", "257000"]]}, "question": "In the context of orphan demographics, \"AIDS-related orphans\" refers to the number of orphans who have lost one or both parents due to AIDS. Based on the provided data, which year has most of AIDS-related orphans among all orphans?", "answer": "2001"}
{"id": "f006cbc7a735f7755e32dde42be5b50b", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["Province", "DC", "LV*", "PSI", "PCI"], "data": [["Verona", "44.3", "10.8", "14.2", "11.5"], ["Vicenza", "49.1", "11.4", "10.1", "8.6"], ["Padua", "46.1", "6.4", "10.7", "16.3"], ["Treviso", "44.5", "7.8", "14.1", "12.1"], ["Belluno", "39.3", "7.0", "23.8", "13.1"], ["Venice", "31.7", "4.9", "15.9", "24.2"], ["Rovigo", "35.2", "3.3", "15.5", "29.0"], ["Veneto", "42.3", "7.8", "13.7", "15.5"]]}, "question": "How does the PSI value change with increasing DC values for provinces with a PCI value above 12?", "answer": "The PSI value decreases moderately (correlation coefficient of -0.40) as DC values increase in provinces where the PCI value above12."}
{"id": "e50db28add493534433a98cb3ccfcdbf", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["county", "starky", "starky %", "hancock", "hancock %", "mccain", "mccain %", "total"], "data": [["apache", "9588", "40.95%", "905", "3.86%", "12923", "55.19%", "23416"], ["cochise", "9555", "21.80%", "1394", "3.18%", "32879", "75.02%", "43828"], ["coconino", "13520", "26.58%", "1504", "2.96%", "35849", "70.47%", "50873"], ["gila", "4291", "20.96%", "632", "3.09%", "15551", "75.95%", "20474"], ["graham", "2000", "19.06%", "322", "3.07%", "8171", "77.87%", "10493"], ["greenlee", "746", "25.03%", "68", "2.28%", "2166", "72.68%", "2980"], ["la paz", "965", "19.51%", "156", "3.15%", "3826", "77.34%", "4947"], ["maricopa", "216124", "18.58%", "29769", "2.56%", "917527", "78.86%", "1163420"], ["mohave", "10423", "18.44%", "1686", "2.98%", "44402", "78.57%", "56511"], ["navajo", "7434", "23.42%", "1222", "3.85%", "23091", "72.73%", "31747"], ["pima", "89483", "25.17%", "7980", "2.24%", "258010", "72.58%", "355473"], ["pinal", "13595", "21.45%", "1692", "2.67%", "48094", "75.88%", "63381"], ["santa cruz", "3583", "31.60%", "252", "2.22%", "7502", "66.17%", "11337"], ["yavapai", "14852", "17.41%", "3160", "3.70%", "67312", "78.89%", "85324"], ["yuma", "8348", "22.28%", "1056", "2.82%", "28069", "74.90%", "37473"]]}, "question": "How does the percentage of votes for McCain (`mccain %`) change with increasing total votes (`total`) across different counties?", "answer": "The percentage of votes for McCain (`mccain %`) exhibits no causal effect (correlation coefficient of 0.24) with increasing total votes (`total`) across different counties."}
{"id": "fb233753896ca878c04484eeb4f019b9", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], "data": [["club", "played", "drawn", "lost", "points for", "points against", "tries for", "tries against", "try bonus", "losing bonus", "points"], ["oakdale rfc", "22", "2", "0", "614", "226", "88", "23", "13", "0", "97"], ["blaenavon rfc", "22", "1", "5", "444", "271", "61", "33", "5", "2", "73"], ["brynithel rfc", "22", "3", "4", "398", "292", "41", "24", "4", "1", "71"], ["caldicot rfc", "22", "0", "8", "500", "330", "69", "44", "8", "3", "67"], ["usk rfc", "22", "2", "8", "484", "431", "71", "58", "11", "1", "64"], ["hartridge rfc", "22", "1", "11", "424", "345", "52", "45", "5", "5", "52"], ["bettws rfc", "22", "3", "11", "476", "438", "59", "53", "6", "7", "51"], ["rtb (ebbw vale) rfc", "22", "3", "12", "317", "371", "38", "50", "5", "4", "43"], ["ynysddu rfc", "22", "1", "14", "315", "376", "35", "44", "3", "9", "42"], ["llanhilleth rfc", "22", "3", "13", "357", "475", "42", "61", "3", "4", "37"], ["trinant rfc", "22", "1", "15", "261", "487", "29", "65", "1", "4", "31"], ["pontllanfraith rfc", "22", "0", "21", "160", "708", "17", "102", "2", "1", "7"]]}, "question": "How many points did the llanhilleth rfc score in the league season?", "answer": "37"}
{"id": "f05eecdcb6b316d67cfcdee33a48b838", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["year", "theme", "artist", "mintage", "issue price"], "data": [["2002", "15th anniversary loonie", "dora de pãdery - hunt", "67672", "39.95"], ["2004", "jack miner bird sanctuary", "susan taylor", "46493", "39.95"], ["2005", "tufted puffin", "n / a", "39818", "39.95"], ["2006", "snowy owl", "glen loates", "39935", "44.95"], ["2007", "trumpeter swan", "kerri burnett", "40000", "45.95"], ["2008", "common eider", "mark hobson", "40000", "47.95"], ["2009", "great blue heron", "chris jordison", "40000", "47.95"], ["2010", "northern harrier", "arnold nogy", "35000", "49.95"], ["2011", "great gray owl", "arnold nogy", "35000", "49.95"], ["2012", "25th anniversary loonie", "arnold nogy", "35000", "49.95"]]}, "question": "What is the total mintage of all coins from 2002 to 2012?", "answer": "418918"}
{"id": "e3cdc02ff933ead57a5ddd0f4dc189c2", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["south american rank", "world rank", "nation", "2011 (imf)", "2008 (cia factbook)"], "data": [["1", "51", "argentina", "17376", "14500"], ["2", "55", "chile", "16171", "15400"], ["3", "59", "uruguay", "15469", "12300"], ["4", "71", "venezuela", "12407", "13500"], ["5", "74", "brazil", "11845", "10513"], ["6", "82", "colombia", "10155", "9000"], ["7", "83", "peru", "10000", "8500"], ["8", "86", "suriname", "9492", "8900"], ["9", "91", "ecuador", "8335", "7700"], ["10", "96", "guyana", "7541", "4000"], ["11", "110", "paraguay", "5548", "4400"]]}, "question": "What is the total GDP of all South American countries listed in the table according to the 2011 IMF estimates?", "answer": "124339"}
{"id": "c1bb893ef3668efd4e9d6a33f283ba01", "qtype": "DataAnalysis", "qsubtype": "DescriptiveAnalysis", "table": {"columns": ["year", "marriages between men", "marriages between women", "same - sex marriages", "total marriages", "% same - sex marriages"], "data": [["2005 (since july)", "923", "352", "1275", "120728", "1.06"], ["2006", "3190", "1384", "4574", "211818", "2.16"], ["2007", "2180", "1070", "3250", "203697", "1.6"], ["2008", "2299", "1250", "3549", "196613", "1.81"], ["2009", "2212", "1200", "3412", "175952", "1.94"], ["2010", "2216", "1367", "3583", "170815", "2.1"], ["2011", "2293", "1587", "3880", "163085", "2.38"]]}, "question": "Can you describe the content of the table, explain the main columns, and provide some initial insights into the trends observed in same-sex marriages over the years?", "answer": "The table provides data on marriages from 2005 to 2011, detailing counts for marriages between men, marriages between women, total same-sex marriages, and overall marriages, along with the percentage of same-sex marriages. The data reveals a rising trend in both the number and percentage of same-sex marriages, concurrent with a decline in total marriages."}
{"id": "5fff0c0fb4be0e28ec40c3b1dcbcd84b", "qtype": "NumericalReasoning", "qsubtype": "ArithmeticCalculation", "table": {"columns": ["class", "wheel arrangement", "railway", "number at doncaster", "number at pyewipe", "number at march"], "data": [["d13", "4 - 4 - 0", "ger", "3", "3", "6"], ["d14", "4 - 4 - 0", "ger", "0", "0", "1"], ["d15", "4 - 4 - 0", "ger", "1", "0", "4"], ["e4", "2 - 4 - 0", "ger", "1", "0", "4"], ["j14", "0 - 6 - 0", "ger", "0", "0", "1"], ["j15", "0 - 6 - 0", "ger", "0", "3", "17"], ["j16", "0 - 6 - 0", "ger", "0", "1", "7"], ["j17", "0 - 6 - 0", "ger", "0", "2", "15"], ["j18", "0 - 6 - 0", "ger", "0", "0", "7"], ["j19", "0 - 6 - 0", "ger", "0", "0", "8"], ["j20", "0 - 6 - 0", "ger", "0", "0", "14"], ["j66", "0 - 6 - 0t", "ger", "0", "3", "10"], ["j67", "0 - 6 - 0t", "ger", "0", "0", "1"], ["j68", "0 - 6 - 0t", "ger", "0", "0", "1"]]}, "question": "What is the total number of locomotives at Doncaster and Pyewipe combined for the 'j17' and 'e4' classes?", "answer": "3"}
{"id": "ee38d1e26018264f906e82e45b85e4d8", "qtype": "FactChecking", "qsubtype": "MatchBased", "table": {"columns": ["year", "2010", "2009", "2008", "2005", "2000"], "data": [["shanghai", "1", "2", "1", "1", "1"], ["beijing", "2", "1", "2", "2", "2"], ["tianjin", "3", "3", "3", "3", "3"], ["jiangsu", "4", "4", "5", "6", "6"], ["zhejiang", "5", "5", "4", "4", "4"], ["inner mongolia", "6", "6", "7", "10", "15"], ["guangdong", "7", "7", "6", "5", "5"], ["liaoning", "8", "9", "9", "8", "8"], ["shandong", "9", "8", "8", "7", "9"], ["fujian", "10", "10", "10", "9", "7"], ["jilin", "11", "11", "11", "13", "13"], ["hebei", "12", "12", "12", "11", "11"], ["hubei", "13", "14", "16", "17", "16"], ["chongqing", "14", "13", "15", "16", "17"], ["shaanxi", "15", "16", "18", "20", "23"], ["heilongjiang", "16", "15", "13", "12", "10"], ["ningxia", "17", "17", "19", "22", "21"], ["shanxi", "18", "18", "14", "15", "18"], ["xinjiang", "19", "21", "17", "14", "12"], ["hunan", "20", "20", "22", "21", "20"], ["henan", "21", "19", "20", "18", "19"], ["qinghai", "22", "22", "21", "23", "22"], ["hainan", "23", "23", "23", "19", "14"], ["jiangxi", "24", "25", "24", "24", "25"], ["sichuan", "25", "24", "25", "25", "24"], ["anhui", "26", "26", "27", "27", "26"], ["guangxi", "27", "27", "26", "28", "28"], ["tibet", "28", "28", "28", "26", "29"], ["gansu", "29", "30", "30", "30", "30"], ["yunnan", "30", "29", "29", "29", "27"], ["guizhou", "31", "31", "31", "31", "31"]]}, "question": "What was the ranking of guangdong in 2008?", "answer": "6"}
{"id": "8c08ad04ed79ae9165b5ae54d1c489bd", "qtype": "NumericalReasoning", "qsubtype": "Time-basedCalculation", "table": {"columns": ["Year", "R class in service at start of year", "R1 class in service at start of year", "Quantity withdrawn", "Locomotive numbers", "Notes"], "data": [["1931", "11", "13", "1", "1342", "-"], ["1932", "10", "13", "1", "1077", "-"], ["1934", "9", "13", "3", "1126, 1152, 1338", "-"], ["1935", "6", "13", "1", "1153", "-"], ["1937", "5", "13", "1", "1125", "-"], ["1939", "4", "13", "1", "1155", "-"], ["1941", "3", "13", "1", "1336", "-"], ["1942", "2", "13", "1", "1070", "-"], ["1943", "1", "13", "1", "1124", "-"], ["1949", "0", "13", "1", "1127", "-"], ["1955", "—", "12", "2", "31154, 31335", "-"], ["1958", "—", "10", "2", "31069, 31147", "-"], ["1959", "—", "8", "6", "31010, 31107, 31128, 31174, 31339, 31340", "-"], ["1960", "—", "2", "2", "31047, 31337", "-"]]}, "question": "What is the average number of locomotives withdrawn per year from 1935 to 1943?", "answer": "1160.5"}
{"id": "0116e7d6e612aa460deb91c8cd6ffe15", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["no", "-", "title", "directed by", "written by", "original air date", "production code", "us viewers (million)"], "data": [["89", "1", "revival", "steward lee", "chris collins", "september 29 , 2012", "4.26", "1.94"], ["90", "2", "a war on two fronts", "dave filoni", "chris collins", "october 6 , 2012", "4.15", "1.71"], ["91", "3", "front runners", "steward lee", "chris collins", "october 13 , 2012", "4.16", "1.75"], ["92", "4", "the soft war", "kyle dunlevy", "chris collins", "october 20 , 2012", "4.17", "1.57"], ["93", "5", "tipping points", "bosco ng", "chris collins", "october 27 , 2012", "4.18", "1.42"], ["94", "6", "the gathering", "kyle dunlevy", "christian taylor", "november 3 , 2012", "4.22", "1.66"], ["95", "7", "a test of strength", "bosco ng", "christian taylor", "november 10 , 2012", "4.23", "1.74"], ["96", "8", "bound for rescue", "brian kalin o'connell", "christian taylor", "november 17 , 2012", "4.24", "1.96"], ["97", "9", "a necessary bond", "danny keller", "christian taylor", "november 24 , 2012", "4.25", "1.39"], ["98", "10", "secret weapons", "danny keller", "brent friedman", "december 1 , 2012", "5.04", "1.46"], ["99", "11", "a sunny day in the void", "kyle dunlevy", "brent friedman", "december 8 , 2012", "5.05", "1.43"], ["100", "12", "missing in action", "steward lee", "brent friedman", "january 5 , 2013", "5.06", "1.74"], ["101", "13", "point of no return", "bosco ng", "brent friedman", "january 12 , 2013", "5.07", "1.47"], ["102", "14", "eminence", "kyle dunlevy", "chris collins", "january 19 , 2013", "5.01", "1.85"], ["103", "15", "shades of reason", "bosco ng", "chris collins", "january 26 , 2013", "5.02", "1.83"], ["104", "16", "the lawless", "brian kalin o'connell", "chris collins", "february 2 , 2013", "5.03", "1.86"], ["105", "17", "sabotage", "brian kalin o'connell", "charles murray", "february 9 , 2013", "5.08", "2.02"], ["106", "18", "the jedi who knew too much", "danny keller", "charles murray", "february 16 , 2013", "5.09", "1.64"], ["107", "19", "to catch a jedi", "kyle dunlevy", "charles murray", "february 23 , 2013", "5.1", "2.06"]]}, "question": "Based on the viewership trends from episodes aired between September 2012 and February 2013, what can be forecasted about the viewership for an episode directed by 'Kyle Dunlevy' if it were to air in March 2013?", "answer": "2.07"}
{"id": "047443783007a597076b5c7abb63cd53", "qtype": "NumericalReasoning", "qsubtype": "Ranking", "table": {"columns": ["rank", "country (or dependent territory)", "july 1 , 2013 projection", "% of pop", "average relative annual growth (%)", "average absolute annual growth"], "data": [["1", "egypt", "********.0", "22.81", "2.29", "1893000"], ["2", "algeria", "********.0", "10.32", "2.11", "792000"], ["3", "iraq", "35404000.0", "9.54", "3.06", "1051000"], ["4", "sudan", "35150000.0", "9.47", "2.52", "863000"], ["5", "morocco", "32950000.0", "8.88", "1.08", "353000"], ["6", "saudi arabia", "30193000.0", "8.14", "3.41", "997000"], ["7", "yemen", "25252000.0", "6.81", "2.96", "725000"], ["8", "syria", "22169000.0", "5.98", "2.45", "531000"], ["9", "tunisia", "10889000.0", "2.94", "1.03", "111000"], ["10", "somalia", "9662000.0", "2.6", "1.17", "112000"], ["11", "united arab emirates", "8659000.0", "2.33", "1.56", "133000"], ["12", "jordan", "6517000.0", "1.76", "2.84", "180000"], ["13", "libya", "6323000.0", "1.7", "1.56", "97000"], ["14", "palestine", "4421000.0", "1.19", "2.91", "125000"], ["15", "lebanon", "4127000.0", "1.11", "1.58", "64000"], ["16", "oman", "3942000.0", "1.06", "8.8", "319000"], ["17", "kuwait", "3852000.0", "1.04", "2.94", "110000"], ["18", "mauritania", "3461000.0", "0.93", "2.58", "87000"], ["19", "qatar", "1917000.0", "0.52", "3.85", "71000"], ["20", "bahrain", "1546000.0", "0.42", "7.36", "106000"], ["21", "djibouti", "912000.0", "0.25", "2.7", "24000"], ["22", "comoros", "743000.0", "0.2", "2.62", "19000"], ["align = left|total", "370989000", "100.0", "2.42", "8763000.0", "29"]]}, "question": "Which country has the highest average relative annual growth (%) in population?", "answer": "oman"}
{"id": "b0bce3c8708c147f9d7b85cac2fb8549", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Pos", "No", "Driver", "Constructor", "Lap", "Gap"], "data": [["1", "98", "Walt Faulkner", "Kurtis Kraft-Offenhauser", "4:27.97", "–"], ["2", "28", "Fred Agabashian", "Kurtis Kraft-Offenhauser", "4:31.10", "+ 3.13"], ["3", "31", "Mauri Rose", "Deidt-Offenhauser", "4:32.07", "+ 4.10"], ["4", "5", "George Connor", "Lesovsky-Offenhauser", "4:32.39", "+ 4.42"], ["5", "1", "Johnnie Parsons", "Kurtis Kraft-Offenhauser", "4:32.43", "+ 4.46"], ["6", "49", "Jack McGrath", "Kurtis Kraft-Offenhauser", "4:33.00", "+ 5.03"], ["7", "69", "Duke Dinsmore", "Kurtis Kraft-Offenhauser", "4:34.67", "+ 6.70"], ["8", "14", "Tony Bettenhausen", "Deidt-Offenhauser", "4:34.92", "+ 6.95"], ["9", "17", "Joie Chitwood", "Kurtis Kraft-Offenhauser", "4:35.32", "+ 7.35"], ["10", "3", "Bill Holland", "Deidt-Offenhauser", "4:35.90", "+ 7.93"], ["11", "59", "Pat Flaherty", "Kurtis Kraft-Offenhauser", "4:37.76", "+ 9.79"], ["12", "54", "Cecil Green", "Kurtis Kraft-Offenhauser", "4:30.86", "+ 2.89"], ["13", "18", "Duane Carter", "Stevens-Offenhauser", "4:33.42", "+ 5.45"], ["14", "21", "Spider Webb", "Maserati-Offenhauser", "4:37.46", "+ 9.49"], ["15", "81", "Jerry Hoyt", "Kurtis Kraft-Offenhauser", "4:37.95", "+ 9.98"], ["16", "2", "Myron Fohr", "Marchese-Offenhauser", "4:33.32", "+ 5.35"], ["17", "24", "Bayliss Levrett", "Adams-Offenhauser", "4:34.43", "+ 6.46"], ["18", "45", "Dick Rathmann", "Watson-Offenhauser", "4:34.96", "+ 6.99"], ["19", "7", "Paul Russo", "Nichels-Offenhauser", "4:35.25", "+ 7.28"], ["20", "4", "Walt Brown", "Kurtis Kraft-Offenhauser", "4:35.96", "+ 7.99"], ["21", "12", "Henry Banks", "Maserati-Offenhauser", "4:37.68", "+ 9.71"], ["22", "67", "Bill Schindler", "Snowberger-Offenhauser", "4:31.31", "+ 3.34"], ["23", "8", "Lee Wallard", "Moore-Offenhauser", "4:31.83", "+ 3.86"], ["24", "55", "Troy Ruttman", "Lesovsky-Offenhauser", "4:32.91", "+ 4.94"], ["25", "23", "Sam Hanks", "Kurtis Kraft-Offenhauser", "4:33.57", "+ 5.60"], ["26", "15", "Mack Hellings", "Kurtis Kraft-Offenhauser", "4:35.32", "+ 7.35"], ["27", "22", "Jimmy Davies", "Ewing-Offenhauser", "4:36.07", "+ 8.10"], ["28", "76", "Jim Rathmann", "Wetteroth-Offenhauser", "4:37.01", "+ 9.04"], ["29", "27", "Walt Ader", "Rae-Offenhauser", "4:37.05", "+ 9.08"], ["30", "77", "Jackie Holmes", "Olson-Offenhauser", "4:37.57", "+ 9.60"], ["31", "75", "Gene Hartley", "Langley-Offenhauser", "4:38.61", "+ 10.64"], ["32", "61", "Jimmy Jackson", "Kurtis Kraft-Cummins", "4:38.62", "+ 10.65"], ["33", "62", "Johnny McDowell", "Kurtis Kraft-Offenhauser", "4:37.58", "+ 9.61"]]}, "chart_type": "line", "question": "Please help me draw a line chart showing the time difference between all competitors and the first-place finisher.", "answer": "y_references = [[0, 3.13, 4.10, 4.42, 4.46, 5.03, 6.70, 6.95, 7.35, 7.93, 9.79, 2.89, 5.45, 9.49, 9.98, 5.35, 6.46, 6.99, 7.28, 7.99, 9.71, 3.34, 3.86, 4.94, 5.60, 7.35, 8.10, 9.04, 9.08, 9.60, 10.64, 10.65, 9.61]]"}
{"id": "12015a78608d814a680338824e98cc15", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["year", "start", "qual", "rank", "finish", "laps"], "data": [["1960", "28", "142.354", "29", "9", "200"], ["1962", "10", "147.753", "10", "15", "200"], ["1963", "25", "148.227", "27", "28", "46"], ["1964", "19", "151.21", "26", "6", "198"], ["1965", "24", "154.672", "23", "16", "115"], ["1966", "27", "159.144", "26", "21", "16"], ["1967", "25", "163.228", "22", "14", "182"], ["1968", "18", "164.444", "17", "16", "158"], ["1969", "18", "166.597", "18", "15", "155"], ["1971", "17", "170.156", "24", "7", "198"]]}, "question": "Based on the historical data from 1960 to 1971, how has the qualifying speed ('qual') trended, and how might this trend influence the finishing positions in next year?", "answer": "Increasing trend, 13"}
{"id": "92dd1f500ae353c8d3cb4561626c578f", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["position", "club", "played", "points", "wins", "draws", "losses", "goals for", "goals against", "goal difference"], "data": [["1", "rcd español", "38", "52", "20", "12", "6", "59", "25", "+ 34"], ["2", "real betis", "38", "51", "22", "7", "9", "66", "38", "+ 28"], ["3", "sd compostela", "38", "49", "21", "7", "10", "56", "36", "+ 20"], ["4", "cd toledo", "38", "47", "18", "11", "9", "50", "32", "+ 18"], ["5", "rcd mallorca", "38", "47", "20", "7", "11", "66", "39", "+ 27"], ["6", "real madrid b", "38", "46", "19", "8", "11", "57", "41", "+ 16"], ["7", "hércules cf", "38", "44", "16", "12", "10", "41", "35", "+ 6"], ["8", "barcelona b", "38", "39", "11", "17", "10", "59", "51", "+ 8"], ["9", "cp mérida", "38", "37", "12", "13", "13", "47", "41", "+ 6"], ["10", "sd eibar", "38", "35", "10", "15", "13", "30", "40", "- 10"], ["11", "cd badajoz", "38", "35", "12", "11", "15", "45", "46", "- 1"], ["12", "atlético marbella", "38", "35", "10", "15", "13", "40", "41", "- 1"], ["13", "palamós cf", "38", "34", "11", "12", "15", "40", "49", "- 9"], ["14", "athletic de bilbao b", "38", "34", "10", "14", "14", "46", "52", "- 6"], ["15", "cd leganés", "38", "34", "11", "12", "15", "53", "59", "- 6"], ["16", "villarreal cf", "38", "34", "14", "6", "18", "29", "48", "- 19"], ["17", "cd castellón", "38", "32", "9", "14", "15", "30", "48", "- 18"], ["18", "real murcia", "38", "31", "10", "11", "17", "40", "64", "- 24"], ["19", "real burgos 1", "38", "26", "10", "6", "22", "38", "68", "- 30"], ["20", "cádiz cf", "38", "18", "4", "10", "24", "28", "67", "- 39"]]}, "chart_type": "bar", "question": "Could you create a bar chart to compare the goal differences of the top 5 teams in the league, with each bar representing a team and its corresponding goal difference?", "answer": "y_references = [34, 28, 20, 18, 27]"}
{"id": "c8d2b2e7ca10141b6abffb9068299d93", "qtype": "DataAnalysis", "qsubtype": "CausalAnalysis", "table": {"columns": ["region", "start datum", "target datum", "c_x ( metre )", "c_y (metre)", "c_z (metre)", "s ( ppm )", "r x ( arcsecond )", "r y ( arcsecond )", "r z ( arcsecond )"], "data": [["slovenia etrs89", "d48", "d96", "409.545", "72.164", "486.872", "17.919665", "3.085957", "5.46911", "11.020289"], ["england , scotland , wales", "wgs84", "osgb36", "446.448", "125.157", "542.06", "20.4894", "0.1502", "0.247", "0.8421"], ["ireland", "wgs84", "ireland 1965", "482.53", "130.596", "564.557", "8.15", "1.042", "0.214", "0.631"], ["germany", "wgs84", "dhdn", "591.28", "81.35", "396.39", "9.82", "1.477", "0.0736", "1.458"], ["germany", "wgs84", "bessel 1841", "582.0", "105.0", "414.0", "8.3", "1.04", "0.35", "3.08"], ["germany", "wgs84", "krassovski 1940", "24.0", "123.0", "94.0", "1.1", "0.02", "0.26", "0.13"], ["austria (bev)", "wgs84", "mgi", "577.326", "90.129", "463.92", "2.423", "5.137", "1.474", "5.297"]]}, "question": "Does a higher value of 's (ppm)' causally influence the coordinates 'c_x (metre)', 'c_y (metre)', or 'c_z (metre)' in the geospatial transformations listed in the table?", "answer": "Higher values of 's (ppm)' show a moderate positive causal influence on 'c_z (metre)' (correlation coefficient of 0.60), but no causal effect on 'c_x (metre)' (0.25) or 'c_y (metre)' (-0.14)."}
{"id": "e15204a55dd9aa141e95354c91a62bd0", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["interval name", "size (steps)", "size (cents)", "just ratio", "just (cents)", "error", "audio"], "data": [["perfect fifth", "9", "720", "3:2", "701.96", "+ 18.04", "play category : articles with haudio microformats"], ["septimal tritone", "7", "560", "7:5", "582.51", "22.51", "play category : articles with haudio microformats"], ["11:8 wide fourth", "7", "560", "11:8", "551.32", "+ 8.68", "play category : articles with haudio microformats"], ["15:11 wide fourth", "7", "560", "15:11", "536.95", "+ 23.05", "play category : articles with haudio microformats"], ["perfect fourth", "6", "480", "4:3", "498.04", "18.04", "play category : articles with haudio microformats"], ["septimal major third", "5", "400", "9:7", "435.08", "35.08", "play category : articles with haudio microformats"], ["undecimal major third", "5", "400", "14:11", "417.51", "17.51", "play category : articles with haudio microformats"], ["major third", "5", "400", "5:4", "386.31", "+ 13.69", "play category : articles with haudio microformats"], ["minor third", "4", "320", "6:5", "315.64", "+ 4.36", "play category : articles with haudio microformats"], ["septimal minor third", "3", "240", "7:6", "266.87", "26.87", "play category : articles with haudio microformats"], ["septimal whole tone", "3", "240", "8:7", "231.17", "+ 8.83", "play category : articles with haudio microformats"], ["major tone", "3", "240", "9:8", "203.91", "+ 36.09", "play category : articles with haudio microformats"], ["minor tone", "2", "160", "10:9", "182.4", "22.40", "play category : articles with haudio microformats"], ["greater undecimal neutral second", "2", "160", "11:10", "165.0", "5.00", "play category : articles with haudio microformats"], ["lesser undecimal neutral second", "2", "160", "12:11", "150.63", "+ 9.36", "play category : articles with haudio microformats"], ["just diatonic semitone", "1", "80", "16:15", "111.73", "31.73", "play category : articles with haudio microformats"], ["septimal chromatic semitone", "1", "80", "21:20", "84.46", "4.47", "play category : articles with haudio microformats"]]}, "question": "What is the total error (in cents) of the intervals with the smallest sizes (in steps)?", "answer": "36.2"}
{"id": "4b4ff8f17963fa7ba3edd5cae2c32abb", "qtype": "NumericalReasoning", "qsubtype": "Multi-hop NumericalReasoing", "table": {"columns": ["team", "wins", "losses", "ties", "win pct"], "data": [["arizona cardinals", "2", "1", "0", "0.667"], ["atlanta falcons", "3", "1", "1", "0.7"], ["baltimore ravens", "13", "9", "0", "0.591"], ["buffalo bills", "5", "2", "0", "0.714"], ["carolina panthers", "3", "1", "0", "0.75"], ["chicago bears", "3", "1", "0", "0.75"], ["cincinnati bengals", "21", "9", "0", "0.7"], ["cleveland browns", "19", "5", "0", "0.792"], ["dallas cowboys", "1", "2", "0", "0.333"], ["denver broncos", "1", "3", "0", "0.25"], ["detroit lions", "4", "1", "0", "0.8"], ["green bay packers", "2", "2", "0", "0.5"], ["houston texans", "1", "1", "0", "0.5"], ["indianapolis colts", "4", "1", "0", "0.8"], ["jacksonville jaguars", "8", "10", "0", "0.444"], ["kansas city chiefs", "5", "3", "0", "0.625"], ["miami dolphins", "5", "2", "0", "0.714"], ["minnesota vikings", "2", "2", "0", "0.5"], ["new england patriots", "4", "3", "0", "0.571"], ["new orleans saints", "2", "1", "0", "0.667"], ["new york giants", "2", "1", "0", "0.667"], ["new york jets", "4", "1", "0", "0.8"], ["oakland raiders", "5", "2", "0", "0.714"], ["philadelphia eagles", "2", "2", "0", "0.5"], ["st louis rams", "1", "2", "0", "0.333"], ["san diego chargers", "7", "2", "0", "0.778"], ["san francisco 49ers", "1", "3", "0", "0.25"], ["seattle seahawks", "2", "4", "0", "0.333"], ["tampa bay buccaneers", "3", "1", "0", "0.75"], ["tennessee titans", "11", "12", "0", "0.478"], ["washington redskins", "3", "0", "0", "1.0"], ["totals :", "149", "90", "1", "0.623"]]}, "question": "What is the total number of wins by teams that have a win percentage greater than 0.7?", "answer": "65"}
{"id": "f08b94c7f4b830fd8c090b2ef668f701", "qtype": "DataAnalysis", "qsubtype": "TrendForecasting", "table": {"columns": ["rank", "city", "province", "date of official foundation of municipality", "2006", "1996", "1986", "1976", "1966", "1956"], "data": [["1", "tehran", "tehran", "1885", "7705036", "6758845", "6042584", "4530223", "2719730", "1512082"], ["2", "mashhad", "razavi khorasan", "1918", "2410800", "1887405", "1463508", "667770", "409616", "241984"], ["3", "esfahān", "esfahān", "1928", "1602110", "1266072", "986753", "661510", "424045", "254708"], ["4", "tabriz", "east azarbaijan", "1917", "1398060", "1191043", "971482", "597976", "403413", "289996"], ["5", "karaj", "alborz", "1934", "1377450", "940968", "611510", "137926", "44243", "14526"], ["6", "shiraz", "fars", "1950", "1227311", "1053025", "848289", "425813", "269865", "170659"]]}, "question": "Based on the historical population growth from 1956 to 2006, what could be the projected population of Tabriz in 2026?", "answer": "1872811"}
{"id": "b6219d257925aa2abc3e4511e5a2ac16", "qtype": "NumericalReasoning", "qsubtype": "Aggregation", "table": {"columns": ["states", "males (%)", "males rank", "females (%)", "females rank"], "data": [["india", "12.1", "14", "16.0", "15"], ["punjab", "30.3", "1", "37.5", "1"], ["kerala", "24.3", "2", "34.0", "2"], ["goa", "20.8", "3", "27.0", "3"], ["tamil nadu", "19.8", "4", "24.4", "4"], ["andhra pradesh", "17.6", "5", "22.7", "10"], ["sikkim", "17.3", "6", "21.0", "8"], ["mizoram", "16.9", "7", "20.3", "17"], ["himachal pradesh", "16.0", "8", "19.5", "12"], ["maharashtra", "15.9", "9", "18.1", "13"], ["gujarat", "15.4", "10", "17.7", "7"], ["haryana", "14.4", "11", "17.6", "6"], ["karnataka", "14.0", "12", "17.3", "9"], ["manipur", "13.4", "13", "17.1", "11"], ["uttarakhand", "11.4", "15", "14.8", "14"], ["arunachal pradesh", "10.6", "16", "12.5", "19"], ["uttar pradesh", "9.9", "17", "12.0", "18"], ["jammu and kashmir", "8.7", "18", "11.1", "5"], ["bihar", "8.5", "19", "10.5", "29"], ["nagaland", "8.4", "20", "10.2", "22"], ["rajasthan", "8.4", "20", "9.0", "20"], ["meghalaya", "8.2", "22", "8.9", "26"], ["orissa", "6.9", "23", "8.6", "25"], ["assam", "6.7", "24", "7.8", "21"], ["chattisgarh", "6.5", "25", "7.6", "27"], ["west bengal", "6.1", "26", "7.1", "16"], ["madhya pradesh", "5.4", "27", "6.7", "23"], ["jharkhand", "5.3", "28", "5.9", "28"]]}, "question": "What is the average percentage of males across all states in India?", "answer": "12.82"}
{"id": "7b87f70bc3d95922c6b3335e7a737fe2", "qtype": "Visualization", "qsubtype": "ChartGeneration", "table": {"columns": ["Year", "Team", "GP", "GS", "MPG", "FG%", "3P%", "FT%", "RPG", "APG", "SPG", "BPG", "PPG"], "data": [["1993–94", "Golden State", "76", "76", "32.1", "0.552", "0.0", "0.532", "9.1", "3.6", "1.2", "2.2", "17.5"], ["1994–95", "Washington", "54", "52", "38.3", "0.495", "0.276", "0.502", "9.6", "4.7", "1.5", "1.6", "20.1"], ["1995–96", "Washington", "15", "15", "37.2", "0.543", "0.441", "0.594", "7.6", "5.0", "1.8", ".6", "23.7"], ["1996–97", "Washington", "72", "72", "39.0", "0.518", "0.397", "0.565", "10.3", "4.6", "1.7", "1.9", "20.1"], ["1997–98", "Washington", "71", "71", "39.6", "0.482", "0.317", "0.589", "9.5", "3.8", "1.6", "1.7", "21.9"], ["1998–99", "Sacramento", "42", "42", "40.9", "0.486", "0.118", "0.454", "13.0", "4.1", "1.4", "2.1", "20.0"], ["1999–00", "Sacramento", "75", "75", "38.4", "0.483", "0.284", "0.751", "10.5", "4.6", "1.6", "1.7", "24.5"], ["2000–01", "Sacramento", "70", "70", "40.5", "0.481", "0.071", "0.703", "11.1", "4.2", "1.3", "1.7", "27.1"], ["2001–02", "Sacramento", "54", "54", "38.4", "0.495", "0.263", "0.749", "10.1", "4.8", "1.7", "1.4", "24.5"], ["2002–03", "Sacramento", "67", "67", "39.1", "0.461", "0.238", "0.607", "10.5", "5.4", "1.6", "1.3", "23.0"], ["2003–04", "Sacramento", "23", "23", "36.1", "0.413", "0.2", "0.711", "8.7", "4.6", "1.3", ".9", "18.7"], ["2004–05", "Sacramento", "46", "46", "36.3", "0.449", "0.379", "0.799", "9.7", "5.5", "1.5", ".7", "21.3"], ["2004–05", "Philadelphia", "21", "21", "33.4", "0.391", "0.267", "0.776", "7.9", "3.1", "1.2", ".9", "15.6"], ["2005–06", "Philadelphia", "75", "75", "38.6", "0.434", "0.273", "0.756", "9.9", "3.4", "1.4", ".8", "20.2"], ["2006–07", "Philadelphia", "18", "18", "30.2", "0.387", "0.4", "0.643", "8.3", "3.4", "1.0", ".8", "11.0"], ["2006–07", "Detroit", "43", "42", "29.7", "0.489", "0.333", "0.636", "6.7", "3.0", "1.0", ".6", "11.3"], ["2007–08", "Golden State", "9", "8", "14.0", "0.484", "0.0", "0.417", "3.6", "2.0", "0.4", ".7", "3.9"], ["Career", null, "831", "827", "37.1", "0.479", "0.299", "0.649", "9.8", "4.2", "1.4", "1.4", "20.7"], ["All-Star", null, "4", "4", "19.0", "0.371", "0.333", "0.375", "6.0", "3.3", "1.0", "-", "7.5"]]}, "chart_type": "radar", "question": "Please help me draw a radar chart, showing the average rebounds, assists, steals, and blocks per game for this athlete in the 2006-07 season.", "answer": "y_references = [[7.5, 3.2, 1.0, 0.7]]"}
{"id": "2962b6f7f7a0902cee3063e870704e24", "qtype": "DataAnalysis", "qsubtype": "CorrelationAnalysis", "table": {"columns": ["Player", "G", "IP", "W", "L", "ERA", "SO"], "data": [["Noodles Hahn", "35", "297.2", "16", "18", "2.06", "98"], ["Jack Harper", "34", "293.2", "23", "9", "2.30", "125"], ["Win Kellum", "31", "224.2", "15", "10", "2.60", "70"], ["Tom Walker", "24", "217", "15", "8", "2.24", "64"], ["Bob Ewing", "26", "212", "11", "13", "2.46", "99"], ["Jack Sutthoff", "12", "90", "5", "6", "2.30", "27"]]}, "question": "What is the correlation between 'innings pitched (IP)' and 'strikeouts (SO)' in the table, and can you provide the correlation coefficient as evidence?", "answer": "Strong positive correlation, 0.89"}
